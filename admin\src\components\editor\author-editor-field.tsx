// EditorField.tsx
"use client";

import * as React from "react";
import { BlockNoteEditorField } from "./blocknote-editor";
import { PartialBlock } from "@blocknote/core";
import { prepareContentForEditor, prepareContentForSave } from "@/lib/utils/content-format";
import { BlockNoteEditor } from "@blocknote/core";
import { useEffect, useState } from "react";

/**
 * Props for the EditorField component.
 */
export interface EditorFieldProps {
    /**
     * Current value of the editor (array of blocks)
     */
    value?: PartialBlock[];

    /**
     * Called when the editor value changes.
     */
    onChange?: (value: PartialBlock[]) => void;

    /**
     * Placeholder text to display when editor is empty.
     */
    placeholder?: string;

    /**
     * Additional CSS classes
     */
    className?: string;
}

// Safe wrapper for BlockNoteEditorField to ensure only blocks are passed
function SafeBlockNoteEditorField({ value, onChange, ...props }: {
    value: string | PartialBlock[] | undefined,
    onChange: (blocks: PartialBlock[]) => void,
    [key: string]: any
}) {
    const [blocks, setBlocks] = useState<PartialBlock[] | undefined>(undefined);

    useEffect(() => {
        let cancelled = false;
        async function loadBlocks() {
            if (typeof value === 'string') {
                const tempEditor = BlockNoteEditor.create();
                const parsed = await prepareContentForEditor(value, tempEditor);
                if (!cancelled) setBlocks(parsed);
            } else if (Array.isArray(value)) {
                setBlocks(value);
            } else {
                setBlocks([
                    {
                        type: 'paragraph',
                        content: [],
                    },
                ]);
            }
        }
        loadBlocks();
        return () => { cancelled = true; };
    }, [value]);

    if (!blocks) return <div>Loading...</div>;

    return <BlockNoteEditorField value={blocks} onChange={onChange} {...props} />;
}

export function EditorField({
    value,
    onChange,
    placeholder = "Эчтәлек языгыз...",
    className,
    ...props
}: EditorFieldProps) {
    return (
        <BlockNoteEditorField
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            className={`border rounded-md ${className || ""}`}
            {...props}
        />
    );
}
