(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules__pnpm_63dc90a5._.js", {

"[project]/node_modules/.pnpm/rehype-parse@9.0.1/node_modules/rehype-parse/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_b563c648._.js",
  "static/chunks/9aa75_rehype-parse_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-parse@9.0.1/node_modules/rehype-parse/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-stringify@10.0.1/node_modules/rehype-stringify/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_d220d224._.js",
  "static/chunks/90e6d_rehype-stringify_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-stringify@10.0.1/node_modules/rehype-stringify/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/unified@11.0.5/node_modules/unified/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_6abd4f66._.js",
  "static/chunks/d5201_unified_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/unified@11.0.5/node_modules/unified/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/hast-util-from-dom@5.0.1/node_modules/hast-util-from-dom/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_0704ccaa._.js",
  "static/chunks/cfdcb_hast-util-from-dom_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/hast-util-from-dom@5.0.1/node_modules/hast-util-from-dom/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_60ffa051._.js",
  "static/chunks/15c97_rehype-remark_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-gfm@4.0.1/node_modules/remark-gfm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_aee9f323._.js",
  "static/chunks/98aea_remark-gfm_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-gfm@4.0.1/node_modules/remark-gfm/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-stringify@11.0.0/node_modules/remark-stringify/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_cc889884._.js",
  "static/chunks/409d3_remark-stringify_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-stringify@11.0.0/node_modules/remark-stringify/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-parse@11.0.0/node_modules/remark-parse/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_94e44683._.js",
  "static/chunks/45bea_remark-parse_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-parse@11.0.0/node_modules/remark-parse/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-rehype@11.1.2/node_modules/remark-rehype/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_719e713e._.js",
  "static/chunks/1ccfd_remark-rehype_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-rehype@11.1.2/node_modules/remark-rehype/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_fb3b9850._.js",
  "static/chunks/03142_rehype-format_index_192d1365.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/emoji-mart@5.6.0/node_modules/emoji-mart/dist/module.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/c3d6e_emoji-mart_dist_module_cc437c67.js",
  "static/chunks/c3d6e_emoji-mart_dist_module_2d07b205.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/emoji-mart@5.6.0/node_modules/emoji-mart/dist/module.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@emoji-mart+data@1.2.1/node_modules/@emoji-mart/data/sets/15/native.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6c0ff_@emoji-mart_data_sets_15_native_json_8ac1e7eb._.js",
  "static/chunks/6c0ff_@emoji-mart_data_sets_15_native_json_8de93a9e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@emoji-mart+data@1.2.1/node_modules/@emoji-mart/data/sets/15/native.json (json)");
    });
});
}}),
}]);