module.exports = {

"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast-util-format').Options} Options
 */ __turbopack_context__.s({});
;
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Element} Element
 * @typedef {import('hast').Parents} Parents
 */ /**
 * @template Fn
 * @template Fallback
 * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate
 */ /**
 * @callback Check
 *   Check that an arbitrary value is an element.
 * @param {unknown} this
 *   Context object (`this`) to call `test` with
 * @param {unknown} [element]
 *   Anything (typically a node).
 * @param {number | null | undefined} [index]
 *   Position of `element` in its parent.
 * @param {Parents | null | undefined} [parent]
 *   Parent of `element`.
 * @returns {boolean}
 *   Whether this is an element and passes a test.
 *
 * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test
 *   Check for an arbitrary element.
 *
 *   * when `string`, checks that the element has that tag name
 *   * when `function`, see `TestFunction`
 *   * when `Array`, checks if one of the subtests pass
 *
 * @callback TestFunction
 *   Check if an element passes a test.
 * @param {unknown} this
 *   The given context.
 * @param {Element} element
 *   An element.
 * @param {number | undefined} [index]
 *   Position of `element` in its parent.
 * @param {Parents | undefined} [parent]
 *   Parent of `element`.
 * @returns {boolean | undefined | void}
 *   Whether this element passes the test.
 *
 *   Note: `void` is included until TS sees no return as `undefined`.
 */ /**
 * Check if `element` is an `Element` and whether it passes the given test.
 *
 * @param element
 *   Thing to check, typically `element`.
 * @param test
 *   Check for a specific element.
 * @param index
 *   Position of `element` in its parent.
 * @param parent
 *   Parent of `element`.
 * @param context
 *   Context object (`this`) to call `test` with.
 * @returns
 *   Whether `element` is an `Element` and passes a test.
 * @throws
 *   When an incorrect `test`, `index`, or `parent` is given; there is no error
 *   thrown when `element` is not a node or not an element.
 */ __turbopack_context__.s({
    "convertElement": (()=>convertElement),
    "isElement": (()=>isElement)
});
const isElement = /**
     * @param {unknown} [element]
     * @param {Test | undefined} [test]
     * @param {number | null | undefined} [index]
     * @param {Parents | null | undefined} [parent]
     * @param {unknown} [context]
     * @returns {boolean}
     */ // eslint-disable-next-line max-params
function(element, test, index, parent, context) {
    const check = convertElement(test);
    if (index !== null && index !== undefined && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {
        throw new Error('Expected positive finite `index`');
    }
    if (parent !== null && parent !== undefined && (!parent.type || !parent.children)) {
        throw new Error('Expected valid `parent`');
    }
    if ((index === null || index === undefined) !== (parent === null || parent === undefined)) {
        throw new Error('Expected both `index` and `parent`');
    }
    return looksLikeAnElement(element) ? check.call(context, element, index, parent) : false;
};
const convertElement = /**
     * @param {Test | null | undefined} [test]
     * @returns {Check}
     */ function(test) {
    if (test === null || test === undefined) {
        return element;
    }
    if (typeof test === 'string') {
        return tagNameFactory(test);
    }
    // Assume array.
    if (typeof test === 'object') {
        return anyFactory(test);
    }
    if (typeof test === 'function') {
        return castFactory(test);
    }
    throw new Error('Expected function, string, or array as `test`');
};
/**
 * Handle multiple tests.
 *
 * @param {Array<TestFunction | string>} tests
 * @returns {Check}
 */ function anyFactory(tests) {
    /** @type {Array<Check>} */ const checks = [];
    let index = -1;
    while(++index < tests.length){
        checks[index] = convertElement(tests[index]);
    }
    return castFactory(any);
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {TestFunction}
   */ function any(...parameters) {
        let index = -1;
        while(++index < checks.length){
            if (checks[index].apply(this, parameters)) return true;
        }
        return false;
    }
}
/**
 * Turn a string into a test for an element with a certain type.
 *
 * @param {string} check
 * @returns {Check}
 */ function tagNameFactory(check) {
    return castFactory(tagName);
    "TURBOPACK unreachable";
    /**
   * @param {Element} element
   * @returns {boolean}
   */ function tagName(element) {
        return element.tagName === check;
    }
}
/**
 * Turn a custom test into a test for an element that passes that test.
 *
 * @param {TestFunction} testFunction
 * @returns {Check}
 */ function castFactory(testFunction) {
    return check;
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {Check}
   */ function check(value, index, parent) {
        return Boolean(looksLikeAnElement(value) && testFunction.call(this, value, typeof index === 'number' ? index : undefined, parent || undefined));
    }
}
/**
 * Make sure something is an element.
 *
 * @param {unknown} element
 * @returns {element is Element}
 */ function element(element) {
    return Boolean(element && typeof element === 'object' && 'type' in element && element.type === 'element' && 'tagName' in element && typeof element.tagName === 'string');
}
/**
 * @param {unknown} value
 * @returns {value is Element}
 */ function looksLikeAnElement(value) {
    return value !== null && typeof value === 'object' && 'type' in value && 'tagName' in value;
}
}}),
"[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "embedded": (()=>embedded)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
;
const embedded = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])(/**
   * @param element
   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}
   */ function(element) {
    return element.tagName === 'audio' || element.tagName === 'canvas' || element.tagName === 'embed' || element.tagName === 'iframe' || element.tagName === 'img' || element.tagName === 'math' || element.tagName === 'object' || element.tagName === 'picture' || element.tagName === 'svg' || element.tagName === 'video';
});
}}),
"[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Nodes} Nodes
 */ // HTML whitespace expression.
// See <https://infra.spec.whatwg.org/#ascii-whitespace>.
__turbopack_context__.s({
    "whitespace": (()=>whitespace)
});
const re = /[ \t\n\f\r]/g;
function whitespace(thing) {
    return typeof thing === 'object' ? thing.type === 'text' ? empty(thing.value) : false : empty(thing);
}
/**
 * @param {string} value
 * @returns {boolean}
 */ function empty(value) {
    return value.replace(re, '') === '';
}
}}),
"[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} Node
 * @typedef {import('unist').Parent} Parent
 */ /**
 * @template Fn
 * @template Fallback
 * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate
 */ /**
 * @callback Check
 *   Check that an arbitrary value is a node.
 * @param {unknown} this
 *   The given context.
 * @param {unknown} [node]
 *   Anything (typically a node).
 * @param {number | null | undefined} [index]
 *   The node’s position in its parent.
 * @param {Parent | null | undefined} [parent]
 *   The node’s parent.
 * @returns {boolean}
 *   Whether this is a node and passes a test.
 *
 * @typedef {Record<string, unknown> | Node} Props
 *   Object to check for equivalence.
 *
 *   Note: `Node` is included as it is common but is not indexable.
 *
 * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test
 *   Check for an arbitrary node.
 *
 * @callback TestFunction
 *   Check if a node passes a test.
 * @param {unknown} this
 *   The given context.
 * @param {Node} node
 *   A node.
 * @param {number | undefined} [index]
 *   The node’s position in its parent.
 * @param {Parent | undefined} [parent]
 *   The node’s parent.
 * @returns {boolean | undefined | void}
 *   Whether this node passes the test.
 *
 *   Note: `void` is included until TS sees no return as `undefined`.
 */ /**
 * Check if `node` is a `Node` and whether it passes the given test.
 *
 * @param {unknown} node
 *   Thing to check, typically `Node`.
 * @param {Test} test
 *   A check for a specific node.
 * @param {number | null | undefined} index
 *   The node’s position in its parent.
 * @param {Parent | null | undefined} parent
 *   The node’s parent.
 * @param {unknown} context
 *   Context object (`this`) to pass to `test` functions.
 * @returns {boolean}
 *   Whether `node` is a node and passes a test.
 */ __turbopack_context__.s({
    "convert": (()=>convert),
    "is": (()=>is)
});
const is = /**
     * @param {unknown} [node]
     * @param {Test} [test]
     * @param {number | null | undefined} [index]
     * @param {Parent | null | undefined} [parent]
     * @param {unknown} [context]
     * @returns {boolean}
     */ // eslint-disable-next-line max-params
function(node, test, index, parent, context) {
    const check = convert(test);
    if (index !== undefined && index !== null && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {
        throw new Error('Expected positive finite index');
    }
    if (parent !== undefined && parent !== null && (!is(parent) || !parent.children)) {
        throw new Error('Expected parent node');
    }
    if ((parent === undefined || parent === null) !== (index === undefined || index === null)) {
        throw new Error('Expected both parent and index');
    }
    return looksLikeANode(node) ? check.call(context, node, index, parent) : false;
};
const convert = /**
     * @param {Test} [test]
     * @returns {Check}
     */ function(test) {
    if (test === null || test === undefined) {
        return ok;
    }
    if (typeof test === 'function') {
        return castFactory(test);
    }
    if (typeof test === 'object') {
        return Array.isArray(test) ? anyFactory(test) : propsFactory(test);
    }
    if (typeof test === 'string') {
        return typeFactory(test);
    }
    throw new Error('Expected function, string, or object as test');
};
/**
 * @param {Array<Props | TestFunction | string>} tests
 * @returns {Check}
 */ function anyFactory(tests) {
    /** @type {Array<Check>} */ const checks = [];
    let index = -1;
    while(++index < tests.length){
        checks[index] = convert(tests[index]);
    }
    return castFactory(any);
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {TestFunction}
   */ function any(...parameters) {
        let index = -1;
        while(++index < checks.length){
            if (checks[index].apply(this, parameters)) return true;
        }
        return false;
    }
}
/**
 * Turn an object into a test for a node with a certain fields.
 *
 * @param {Props} check
 * @returns {Check}
 */ function propsFactory(check) {
    const checkAsRecord = check;
    return castFactory(all);
    "TURBOPACK unreachable";
    /**
   * @param {Node} node
   * @returns {boolean}
   */ function all(node) {
        const nodeAsRecord = node;
        /** @type {string} */ let key;
        for(key in check){
            if (nodeAsRecord[key] !== checkAsRecord[key]) return false;
        }
        return true;
    }
}
/**
 * Turn a string into a test for a node with a certain type.
 *
 * @param {string} check
 * @returns {Check}
 */ function typeFactory(check) {
    return castFactory(type);
    "TURBOPACK unreachable";
    /**
   * @param {Node} node
   */ function type(node) {
        return node && node.type === check;
    }
}
/**
 * Turn a custom test into a test for a node that passes that test.
 *
 * @param {TestFunction} testFunction
 * @returns {Check}
 */ function castFactory(testFunction) {
    return check;
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {Check}
   */ function check(value, index, parent) {
        return Boolean(looksLikeANode(value) && testFunction.call(this, value, typeof index === 'number' ? index : undefined, parent || undefined));
    }
}
function ok() {
    return true;
}
/**
 * @param {unknown} value
 * @returns {value is Node}
 */ function looksLikeANode(value) {
    return value !== null && typeof value === 'object' && 'type' in value;
}
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/block.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>
__turbopack_context__.s({
    "blocks": (()=>blocks)
});
const blocks = [
    'address',
    'article',
    'aside',
    'blockquote',
    'body',
    'br',
    'caption',
    'center',
    'col',
    'colgroup',
    'dd',
    'dialog',
    'dir',
    'div',
    'dl',
    'dt',
    'figcaption',
    'figure',
    'footer',
    'form',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'head',
    'header',
    'hgroup',
    'hr',
    'html',
    'legend',
    'li',
    'li',
    'listing',
    'main',
    'menu',
    'nav',
    'ol',
    'optgroup',
    'option',
    'p',
    'plaintext',
    'pre',
    'section',
    'summary',
    'table',
    'tbody',
    'td',
    'td',
    'tfoot',
    'th',
    'th',
    'thead',
    'tr',
    'ul',
    'wbr',
    'xmp' // Flow content, legacy
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/content.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "content": (()=>content)
});
const content = [
    // Form.
    'button',
    'input',
    'select',
    'textarea'
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/skippable.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "skippable": (()=>skippable)
});
const skippable = [
    'area',
    'base',
    'basefont',
    'dialog',
    'datalist',
    'head',
    'link',
    'meta',
    'noembed',
    'noframes',
    'param',
    'rp',
    'script',
    'source',
    'style',
    'template',
    'track',
    'title'
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes, Parents, Text} from 'hast'
 */ /**
 * @callback Collapse
 *   Collapse a string.
 * @param {string} value
 *   Value to collapse.
 * @returns {string}
 *   Collapsed value.
 *
 * @typedef Options
 *   Configuration.
 * @property {boolean | null | undefined} [newlines=false]
 *   Collapse whitespace containing newlines to `'\n'` instead of `' '`
 *   (default: `false`); the default is to collapse to a single space.
 *
 * @typedef Result
 *   Result.
 * @property {boolean} remove
 *   Whether to remove.
 * @property {boolean} ignore
 *   Whether to ignore.
 * @property {boolean} stripAtStart
 *   Whether to strip at the start.
 *
 * @typedef State
 *   Info passed around.
 * @property {Collapse} collapse
 *   Collapse.
 * @property {Whitespace} whitespace
 *   Current whitespace.
 * @property {boolean | undefined} [before]
 *   Whether there is a break before (default: `false`).
 * @property {boolean | undefined} [after]
 *   Whether there is a break after (default: `false`).
 *
 * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace
 *   Whitespace setting.
 */ __turbopack_context__.s({
    "minifyWhitespace": (()=>minifyWhitespace)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$content$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/content.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$skippable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/skippable.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/** @type {Options} */ const emptyOptions = {};
const ignorableNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])([
    'comment',
    'doctype'
]);
function minifyWhitespace(tree, options) {
    const settings = options || emptyOptions;
    minify(tree, {
        collapse: collapseFactory(settings.newlines ? replaceNewlines : replaceWhitespace),
        whitespace: 'normal'
    });
}
/**
 * @param {Nodes} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function minify(node, state) {
    if ('children' in node) {
        const settings = {
            ...state
        };
        if (node.type === 'root' || blocklike(node)) {
            settings.before = true;
            settings.after = true;
        }
        settings.whitespace = inferWhiteSpace(node, state);
        return all(node, settings);
    }
    if (node.type === 'text') {
        if (state.whitespace === 'normal') {
            return minifyText(node, state);
        }
        // Naïve collapse, but no trimming:
        if (state.whitespace === 'nowrap') {
            node.value = state.collapse(node.value);
        }
    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor
    // trimmed.
    }
    return {
        ignore: ignorableNode(node),
        stripAtStart: false,
        remove: false
    };
}
/**
 * @param {Text} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function minifyText(node, state) {
    const value = state.collapse(node.value);
    const result = {
        ignore: false,
        stripAtStart: false,
        remove: false
    };
    let start = 0;
    let end = value.length;
    if (state.before && removable(value.charAt(0))) {
        start++;
    }
    if (start !== end && removable(value.charAt(end - 1))) {
        if (state.after) {
            end--;
        } else {
            result.stripAtStart = true;
        }
    }
    if (start === end) {
        result.remove = true;
    } else {
        node.value = value.slice(start, end);
    }
    return result;
}
/**
 * @param {Parents} parent
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function all(parent, state) {
    let before = state.before;
    const after = state.after;
    const children = parent.children;
    let length = children.length;
    let index = -1;
    while(++index < length){
        const result = minify(children[index], {
            ...state,
            after: collapsableAfter(children, index, after),
            before
        });
        if (result.remove) {
            children.splice(index, 1);
            index--;
            length--;
        } else if (!result.ignore) {
            before = result.stripAtStart;
        }
        // If this element, such as a `<select>` or `<img>`, contributes content
        // somehow, allow whitespace again.
        if (content(children[index])) {
            before = false;
        }
    }
    return {
        ignore: false,
        stripAtStart: Boolean(before || after),
        remove: false
    };
}
/**
 * @param {Array<Nodes>} nodes
 *   Nodes.
 * @param {number} index
 *   Index.
 * @param {boolean | undefined} [after]
 *   Whether there is a break after `nodes` (default: `false`).
 * @returns {boolean | undefined}
 *   Whether there is a break after the node at `index`.
 */ function collapsableAfter(nodes, index, after) {
    while(++index < nodes.length){
        const node = nodes[index];
        let result = inferBoundary(node);
        if (result === undefined && 'children' in node && !skippable(node)) {
            result = collapsableAfter(node.children, -1);
        }
        if (typeof result === 'boolean') {
            return result;
        }
    }
    return after;
}
/**
 * Infer two types of boundaries:
 *
 * 1. `true` — boundary for which whitespace around it does not contribute
 *    anything
 * 2. `false` — boundary for which whitespace around it *does* contribute
 *
 * No result (`undefined`) is returned if it is unknown.
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean | undefined}
 *   Boundary.
 */ function inferBoundary(node) {
    if (node.type === 'element') {
        if (content(node)) {
            return false;
        }
        if (blocklike(node)) {
            return true;
        }
    // Unknown: either depends on siblings if embedded or metadata, or on
    // children.
    } else if (node.type === 'text') {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(node)) {
            return false;
        }
    } else if (!ignorableNode(node)) {
        return false;
    }
}
/**
 * Infer whether a node is skippable.
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is skippable.
 */ function content(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["embedded"])(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$content$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["content"]);
}
/**
 * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is block-like.
 */ function blocklike(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["blocks"]);
}
/**
 * @param {Parents} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is skippable.
 */ function skippable(node) {
    return Boolean(node.type === 'element' && node.properties.hidden) || ignorableNode(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$skippable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["skippable"]);
}
/**
 * @param {string} character
 *   Character.
 * @returns {boolean}
 *   Whether `character` is removable.
 */ function removable(character) {
    return character === ' ' || character === '\n';
}
/**
 * @type {Collapse}
 */ function replaceNewlines(value) {
    const match = /\r?\n|\r/.exec(value);
    return match ? match[0] : ' ';
}
/**
 * @type {Collapse}
 */ function replaceWhitespace() {
    return ' ';
}
/**
 * @param {Collapse} replace
 * @returns {Collapse}
 *   Collapse.
 */ function collapseFactory(replace) {
    return collapse;
    "TURBOPACK unreachable";
    /**
   * @type {Collapse}
   */ function collapse(value) {
        return String(value).replace(/[\t\n\v\f\r ]+/g, replace);
    }
}
/**
 * We don’t need to support void elements here (so `nobr wbr` -> `normal` is
 * ignored).
 *
 * @param {Parents} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Whitespace}
 *   Whitespace.
 */ function inferWhiteSpace(node, state) {
    if ('tagName' in node && node.properties) {
        switch(node.tagName){
            // Whitespace in script/style, while not displayed by CSS as significant,
            // could have some meaning in JS/CSS, so we can’t touch them.
            case 'listing':
            case 'plaintext':
            case 'script':
            case 'style':
            case 'xmp':
                {
                    return 'pre';
                }
            case 'nobr':
                {
                    return 'nowrap';
                }
            case 'pre':
                {
                    return node.properties.wrap ? 'pre-wrap' : 'pre';
                }
            case 'td':
            case 'th':
                {
                    return node.properties.noWrap ? 'nowrap' : state.whitespace;
                }
            case 'textarea':
                {
                    return 'pre-wrap';
                }
            default:
        }
    }
    return state.whitespace;
}
}}),
"[project]/node_modules/.pnpm/hast-util-has-property@3.0.0/node_modules/hast-util-has-property/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Element} Element
 * @typedef {import('hast').Nodes} Nodes
 */ __turbopack_context__.s({
    "hasProperty": (()=>hasProperty)
});
const own = {}.hasOwnProperty;
function hasProperty(node, name) {
    const value = node.type === 'element' && own.call(node.properties, name) && node.properties[name];
    return value !== null && value !== undefined && value !== false;
}
}}),
"[project]/node_modules/.pnpm/hast-util-is-body-ok-link@3.0.1/node_modules/hast-util-is-body-ok-link/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes} from 'hast'
 */ __turbopack_context__.s({
    "isBodyOkLink": (()=>isBodyOkLink)
});
const list = new Set([
    'pingback',
    'prefetch',
    'stylesheet'
]);
function isBodyOkLink(node) {
    if (node.type !== 'element' || node.tagName !== 'link') {
        return false;
    }
    if (node.properties.itemProp) {
        return true;
    }
    const value = node.properties.rel;
    let index = -1;
    if (!Array.isArray(value) || value.length === 0) {
        return false;
    }
    while(++index < value.length){
        if (!list.has(String(value[index]))) {
            return false;
        }
    }
    return true;
}
}}),
"[project]/node_modules/.pnpm/hast-util-phrasing@3.0.1/node_modules/hast-util-phrasing/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Nodes} Nodes
 */ __turbopack_context__.s({
    "phrasing": (()=>phrasing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$has$2d$property$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$has$2d$property$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-has-property@3.0.0/node_modules/hast-util-has-property/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-body-ok-link@3.0.1/node_modules/hast-util-is-body-ok-link/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
;
;
;
;
const basic = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])([
    'a',
    'abbr',
    // `area` is in fact only phrasing if it is inside a `map` element.
    // However, since `area`s are required to be inside a `map` element, and it’s
    // a rather involved check, it’s ignored here for now.
    'area',
    'b',
    'bdi',
    'bdo',
    'br',
    'button',
    'cite',
    'code',
    'data',
    'datalist',
    'del',
    'dfn',
    'em',
    'i',
    'input',
    'ins',
    'kbd',
    'keygen',
    'label',
    'map',
    'mark',
    'meter',
    'noscript',
    'output',
    'progress',
    'q',
    'ruby',
    's',
    'samp',
    'script',
    'select',
    'small',
    'span',
    'strong',
    'sub',
    'sup',
    'template',
    'textarea',
    'time',
    'u',
    'var',
    'wbr'
]);
const meta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])('meta');
function phrasing(value) {
    return Boolean(value.type === 'text' || basic(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["embedded"])(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBodyOkLink"])(value) || meta(value) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$has$2d$property$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$has$2d$property$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hasProperty"])(value, 'itemProp'));
}
}}),
"[project]/node_modules/.pnpm/html-whitespace-sensitive-tag-names@3.0.1/node_modules/html-whitespace-sensitive-tag-names/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * List of HTML tag names that are whitespace sensitive.
 */ __turbopack_context__.s({
    "whitespaceSensitiveTagNames": (()=>whitespaceSensitiveTagNames)
});
const whitespaceSensitiveTagNames = [
    'pre',
    'script',
    'style',
    'textarea'
];
}}),
"[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/color.node.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @param {string} d
 * @returns {string}
 */ __turbopack_context__.s({
    "color": (()=>color)
});
function color(d) {
    return '\u001B[33m' + d + '\u001B[39m';
}
}}),
"[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} UnistNode
 * @typedef {import('unist').Parent} UnistParent
 */ /**
 * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test
 *   Test from `unist-util-is`.
 *
 *   Note: we have remove and add `undefined`, because otherwise when generating
 *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,
 *   which doesn’t work when publishing on npm.
 */ /**
 * @typedef {(
 *   Fn extends (value: any) => value is infer Thing
 *   ? Thing
 *   : Fallback
 * )} Predicate
 *   Get the value of a type guard `Fn`.
 * @template Fn
 *   Value; typically function that is a type guard (such as `(x): x is Y`).
 * @template Fallback
 *   Value to yield if `Fn` is not a type guard.
 */ /**
 * @typedef {(
 *   Check extends null | undefined // No test.
 *   ? Value
 *   : Value extends {type: Check} // String (type) test.
 *   ? Value
 *   : Value extends Check // Partial test.
 *   ? Value
 *   : Check extends Function // Function test.
 *   ? Predicate<Check, Value> extends Value
 *     ? Predicate<Check, Value>
 *     : never
 *   : never // Some other test?
 * )} MatchesOne
 *   Check whether a node matches a primitive check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test, but not arrays.
 */ /**
 * @typedef {(
 *   Check extends Array<any>
 *   ? MatchesOne<Value, Check[keyof Check]>
 *   : MatchesOne<Value, Check>
 * )} Matches
 *   Check whether a node matches a check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test.
 */ /**
 * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint
 *   Number; capped reasonably.
 */ /**
 * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment
 *   Increment a number in the type system.
 * @template {Uint} [I=0]
 *   Index.
 */ /**
 * @typedef {(
 *   Node extends UnistParent
 *   ? Node extends {children: Array<infer Children>}
 *     ? Child extends Children ? Node : never
 *     : never
 *   : never
 * )} InternalParent
 *   Collect nodes that can be parents of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent
 *   Collect nodes in `Tree` that can be parents of `Child`.
 * @template {UnistNode} Tree
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {(
 *   Depth extends Max
 *   ? never
 *   :
 *     | InternalParent<Node, Child>
 *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>
 * )} InternalAncestor
 *   Collect nodes in `Tree` that can be ancestors of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor
 *   Collect nodes in `Tree` that can be ancestors of `Child`.
 * @template {UnistNode} Tree
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {(
 *   Tree extends UnistParent
 *     ? Depth extends Max
 *       ? Tree
 *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>
 *     : Tree
 * )} InclusiveDescendant
 *   Collect all (inclusive) descendants of `Tree`.
 *
 *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to
 *   > recurse without actually running into an infinite loop, which the
 *   > previous version did.
 *   >
 *   > Practically, a max of `2` is typically enough assuming a `Root` is
 *   > passed, but it doesn’t improve performance.
 *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.
 *   > Using up to `10` doesn’t hurt or help either.
 * @template {UnistNode} Tree
 *   Tree type.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @typedef {'skip' | boolean} Action
 *   Union of the action types.
 *
 * @typedef {number} Index
 *   Move to the sibling at `index` next (after node itself is completely
 *   traversed).
 *
 *   Useful if mutating the tree, such as removing the node the visitor is
 *   currently on, or any of its previous siblings.
 *   Results less than 0 or greater than or equal to `children.length` stop
 *   traversing the parent.
 *
 * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple
 *   List with one or two values, the first an action, the second an index.
 *
 * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult
 *   Any value that can be returned from a visitor.
 */ /**
 * @callback Visitor
 *   Handle a node (matching `test`, if given).
 *
 *   Visitors are free to transform `node`.
 *   They can also transform the parent of node (the last of `ancestors`).
 *
 *   Replacing `node` itself, if `SKIP` is not returned, still causes its
 *   descendants to be walked (which is a bug).
 *
 *   When adding or removing previous siblings of `node` (or next siblings, in
 *   case of reverse), the `Visitor` should return a new `Index` to specify the
 *   sibling to traverse after `node` is traversed.
 *   Adding or removing next siblings of `node` (or previous siblings, in case
 *   of reverse) is handled as expected without needing to return a new `Index`.
 *
 *   Removing the children property of an ancestor still results in them being
 *   traversed.
 * @param {Visited} node
 *   Found node.
 * @param {Array<VisitedParents>} ancestors
 *   Ancestors of `node`.
 * @returns {VisitorResult}
 *   What to do next.
 *
 *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.
 *   An `Action` is treated as a tuple of `[Action]`.
 *
 *   Passing a tuple back only makes sense if the `Action` is `SKIP`.
 *   When the `Action` is `EXIT`, that action can be returned.
 *   When the `Action` is `CONTINUE`, `Index` can be returned.
 * @template {UnistNode} [Visited=UnistNode]
 *   Visited node type.
 * @template {UnistParent} [VisitedParents=UnistParent]
 *   Ancestor type.
 */ /**
 * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor
 *   Build a typed `Visitor` function from a tree and a test.
 *
 *   It will infer which values are passed as `node` and which as `parents`.
 * @template {UnistNode} [Tree=UnistNode]
 *   Tree type.
 * @template {Test} [Check=Test]
 *   Test type.
 */ __turbopack_context__.s({
    "CONTINUE": (()=>CONTINUE),
    "EXIT": (()=>EXIT),
    "SKIP": (()=>SKIP),
    "visitParents": (()=>visitParents)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$color$2e$node$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/color.node.js [app-ssr] (ecmascript)");
;
;
/** @type {Readonly<ActionTuple>} */ const empty = [];
const CONTINUE = true;
const EXIT = false;
const SKIP = 'skip';
function visitParents(tree, test, visitor, reverse) {
    /** @type {Test} */ let check;
    if (typeof test === 'function' && typeof visitor !== 'function') {
        reverse = visitor;
        // @ts-expect-error no visitor given, so `visitor` is test.
        visitor = test;
    } else {
        // @ts-expect-error visitor given, so `test` isn’t a visitor.
        check = test;
    }
    const is = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])(check);
    const step = reverse ? -1 : 1;
    factory(tree, undefined, [])();
    /**
   * @param {UnistNode} node
   * @param {number | undefined} index
   * @param {Array<UnistParent>} parents
   */ function factory(node, index, parents) {
        const value = node && typeof node === 'object' ? node : {};
        if (typeof value.type === 'string') {
            const name = // `hast`
            typeof value.tagName === 'string' ? value.tagName : typeof value.name === 'string' ? value.name : undefined;
            Object.defineProperty(visit, 'name', {
                value: 'node (' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$color$2e$node$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["color"])(node.type + (name ? '<' + name + '>' : '')) + ')'
            });
        }
        return visit;
        "TURBOPACK unreachable";
        function visit() {
            /** @type {Readonly<ActionTuple>} */ let result = empty;
            /** @type {Readonly<ActionTuple>} */ let subresult;
            /** @type {number} */ let offset;
            /** @type {Array<UnistParent>} */ let grandparents;
            if (!test || is(node, index, parents[parents.length - 1] || undefined)) {
                // @ts-expect-error: `visitor` is now a visitor.
                result = toResult(visitor(node, parents));
                if (result[0] === EXIT) {
                    return result;
                }
            }
            if ('children' in node && node.children) {
                const nodeAsParent = node;
                if (nodeAsParent.children && result[0] !== SKIP) {
                    offset = (reverse ? nodeAsParent.children.length : -1) + step;
                    grandparents = parents.concat(nodeAsParent);
                    while(offset > -1 && offset < nodeAsParent.children.length){
                        const child = nodeAsParent.children[offset];
                        subresult = factory(child, offset, grandparents)();
                        if (subresult[0] === EXIT) {
                            return subresult;
                        }
                        offset = typeof subresult[1] === 'number' ? subresult[1] : offset + step;
                    }
                }
            }
            return result;
        }
    }
}
/**
 * Turn a return value into a clean result.
 *
 * @param {VisitorResult} value
 *   Valid return values from visitors.
 * @returns {Readonly<ActionTuple>}
 *   Clean result.
 */ function toResult(value) {
    if (Array.isArray(value)) {
        return value;
    }
    if (typeof value === 'number') {
        return [
            CONTINUE,
            value
        ];
    }
    return value === null || value === undefined ? empty : [
        value
    ];
}
}}),
"[project]/node_modules/.pnpm/hast-util-format@1.1.0/node_modules/hast-util-format/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes, RootContent, Root} from 'hast'
 * @import {BuildVisitor} from 'unist-util-visit-parents'
 * @import {Options, State} from './types.js'
 */ __turbopack_context__.s({
    "format": (()=>format)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-phrasing@3.0.1/node_modules/hast-util-phrasing/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$html$2d$whitespace$2d$sensitive$2d$tag$2d$names$40$3$2e$0$2e$1$2f$node_modules$2f$html$2d$whitespace$2d$sensitive$2d$tag$2d$names$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/html-whitespace-sensitive-tag-names@3.0.1/node_modules/html-whitespace-sensitive-tag-names/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
/** @type {Options} */ const emptyOptions = {};
function format(tree, options) {
    const settings = options || emptyOptions;
    /** @type {State} */ const state = {
        blanks: settings.blanks || [],
        head: false,
        indentInitial: settings.indentInitial !== false,
        indent: typeof settings.indent === 'number' ? ' '.repeat(settings.indent) : typeof settings.indent === 'string' ? settings.indent : '  '
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["minifyWhitespace"])(tree, {
        newlines: true
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["visitParents"])(tree, visitor);
    /**
   * @type {BuildVisitor<Root>}
   */ function visitor(node, parents) {
        if (!('children' in node)) {
            return;
        }
        if (node.type === 'element' && node.tagName === 'head') {
            state.head = true;
        }
        if (state.head && node.type === 'element' && node.tagName === 'body') {
            state.head = false;
        }
        if (node.type === 'element' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$html$2d$whitespace$2d$sensitive$2d$tag$2d$names$40$3$2e$0$2e$1$2f$node_modules$2f$html$2d$whitespace$2d$sensitive$2d$tag$2d$names$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespaceSensitiveTagNames"].includes(node.tagName)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SKIP"];
        }
        // Don’t indent content of whitespace-sensitive nodes / inlines.
        if (node.children.length === 0 || !padding(state, node)) {
            return;
        }
        let level = parents.length;
        if (!state.indentInitial) {
            level--;
        }
        let eol = false;
        // Indent newlines in `text`.
        for (const child of node.children){
            if (child.type === 'comment' || child.type === 'text') {
                if (child.value.includes('\n')) {
                    eol = true;
                }
                child.value = child.value.replace(/ *\n/g, '$&' + state.indent.repeat(level));
            }
        }
        /** @type {Array<RootContent>} */ const result = [];
        /** @type {RootContent | undefined} */ let previous;
        for (const child of node.children){
            if (padding(state, child) || eol && !previous) {
                addBreak(result, level, child);
                eol = true;
            }
            previous = child;
            result.push(child);
        }
        if (previous && (eol || padding(state, previous))) {
            // Ignore trailing whitespace (if that already existed), as we’ll add
            // properly indented whitespace.
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(previous)) {
                result.pop();
                previous = result[result.length - 1];
            }
            addBreak(result, level - 1);
        }
        node.children = result;
    }
    /**
   * @param {Array<RootContent>} list
   *   Nodes.
   * @param {number} level
   *   Indentation level.
   * @param {RootContent | undefined} [next]
   *   Next node.
   * @returns {undefined}
   *   Nothing.
   */ function addBreak(list, level, next) {
        const tail = list[list.length - 1];
        const previous = tail && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(tail) ? list[list.length - 2] : tail;
        const replace = (blank(state, previous) && blank(state, next) ? '\n\n' : '\n') + state.indent.repeat(Math.max(level, 0));
        if (tail && tail.type === 'text') {
            tail.value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(tail) ? replace : tail.value + replace;
        } else {
            list.push({
                type: 'text',
                value: replace
            });
        }
    }
}
/**
 * @param {State} state
 *   Info passed around.
 * @param {Nodes | undefined} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is a blank.
 */ function blank(state, node) {
    return Boolean(node && node.type === 'element' && state.blanks.length > 0 && state.blanks.includes(node.tagName));
}
/**
 * @param {State} state
 *   Info passed around.
 * @param {Nodes} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` should be padded.
 */ function padding(state, node) {
    return node.type === 'root' || (node.type === 'element' ? state.head || node.tagName === 'script' || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["embedded"])(node) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["phrasing"])(node) : false);
}
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Options} from 'hast-util-format'
 * @import {Root} from 'hast'
 */ __turbopack_context__.s({
    "default": (()=>rehypeFormat)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$format$40$1$2e$1$2e$0$2f$node_modules$2f$hast$2d$util$2d$format$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-format@1.1.0/node_modules/hast-util-format/lib/index.js [app-ssr] (ecmascript)");
;
function rehypeFormat(options) {
    /**
   * Transform.
   *
   * @param {Root} tree
   *   Tree.
   * @returns {undefined}
   *   Nothing.
   */ return function(tree) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$format$40$1$2e$1$2e$0$2f$node_modules$2f$hast$2d$util$2d$format$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["format"])(tree, options);
    };
}
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$format$40$5$2e$0$2e$1$2f$node_modules$2f$rehype$2d$format$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=node_modules__pnpm_3f39f5a5._.js.map