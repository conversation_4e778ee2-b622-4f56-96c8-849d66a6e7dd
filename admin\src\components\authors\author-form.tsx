"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { EditorField } from "@/components/editor/author-editor-field";
import { BlockNoteEditorField } from "@/components/editor/blocknote-editor";
import { Input } from "@/components/ui/input";
import { useCreateAuthor, useUpdateAuthor } from "@/lib/hooks/use-authors";
import { authorSchema } from "@/lib/schemas/author-schema";
import { Author } from "@/lib/types";
import { useState } from "react";
import { toast, Toaster } from "sonner";
import { formatDateForInput } from "@/lib/utils/format-date";
import { transliterate } from "@/lib/utils/transliterate";
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { BlockNoteEditor } from '@blocknote/core';
import { useEffect } from 'react';
import { PartialBlock } from '@blocknote/core';
import { prepareContentForEditor, isBlockNoteJson } from '@/lib/utils/content-format';
import { SafeBlockNoteEditorField } from '@/components/editor/blocknote-editor';

interface AuthorFormProps {
  author?: Author;
}

export function AuthorForm({ author }: AuthorFormProps) {
  const router = useRouter();
  const createAuthor = useCreateAuthor();
  const updateAuthor = useUpdateAuthor();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<typeof authorSchema._type>({
    resolver: zodResolver(authorSchema),
    defaultValues: author
      ? {
          name: author.name,
          surName: author.surName || "",
          lastName: author.lastName || "",
          displayName: author.displayName,
          urlPart: author.urlPart || "",
          biography: author.biography || "",
          birthDate: author.birthDate ? formatDateForInput(author.birthDate) : "",
          deathDate: author.deathDate ? formatDateForInput(author.deathDate) : "",
        }
      : {
          name: "",
          surName: "",
          lastName: "",
          displayName: "",
          urlPart: "",
          biography: "",
          birthDate: "",
          deathDate: "",
        },
  });

  async function onSubmit(values: typeof authorSchema._type) {
    setIsSubmitting(true);
    try {
      // Преобразуем строковые даты в объекты Date или null
      const formattedValues = {
        ...values,
        content: values.content, // Сохраняем JSON напрямую        
        birthDate: values.birthDate ? new Date(values.birthDate).toISOString() : null,
        deathDate: values.deathDate ? new Date(values.deathDate).toISOString() : null,
      };

      if (author) {
        // При редактировании не отправляем urlPart, так как он не должен изменяться
        const { urlPart, ...updateData } = formattedValues;
        await updateAuthor.mutateAsync({
          id: author.id,
          data: updateData,
        });
        toast.success("Шагыйрь мәгълүматлары уңышлы үзгәртелде");
      } else {
        // При создании отправляем urlPart
        await createAuthor.mutateAsync(formattedValues);
        toast.success("Яңа шагыйрь уңышлы өстәлде");
      }
      router.push("/authors");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Хата: " + (error instanceof Error ? error.message : "Билгесез хата"));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <DndProvider backend={HTML5Backend}>
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Исем</FormLabel>
                <FormControl>
                  <Input placeholder="Габдулла" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="surName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Фамилия</FormLabel>
                <FormControl>
                  <Input placeholder="Тукай" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Отчество</FormLabel>
                <FormControl>
                  <Input placeholder="Мөхәммәтгариф улы" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="displayName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Күрсәтелә торган исем</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Габдулла Тукай"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      // Если это новый автор, автоматически генерируем urlPart
                      if (!author) {
                        const transliteratedValue = transliterate(e.target.value);
                        form.setValue("urlPart", transliteratedValue);
                      }
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Сайтта күрсәтелә торган тулы исем
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="urlPart"
            render={({ field }) => (
              <FormItem>
                <FormLabel>URL өлеше</FormLabel>
                <FormControl>
                  <Input
                    placeholder="gabdulla-tukay"
                    {...field}
                    readOnly={!!author} // Только для чтения при редактировании
                    className={author ? "bg-muted cursor-not-allowed" : ""}
                  />
                </FormControl>
                <FormDescription>
                  {author
                    ? "URL өлешен үзгәртеп булмый"
                    : "Латин хәрефләре, саннар һәм дефис символыннан гына торырга тиеш"}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Туган көне</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="deathDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Үлгән көне</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="biography"
          render={({ field }) => (
              <FormItem>
                <FormLabel>Биография</FormLabel>
                <FormControl>
                  <div className="space-y-4">
                    <SafeBlockNoteEditorField
                      value={field.value}
                      onChange={(blocks: PartialBlock[]) => {
                        const jsonContent = JSON.stringify(blocks);
                        field.onChange(jsonContent);
                      }}
                      placeholder="Шагыйрь турында мәгълүмат..."
                    />
                    <Toaster />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/authors")}
          >
            Кире кайту
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Саклана..."
              : author
              ? "Үзгәртүләрне саклау"
              : "Шагыйрьне өстәү"}
          </Button>
        </div>
      </form>
    </Form>
    </DndProvider>
  );
}
