{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts", "download-schema": "node scripts/download-schema.js"}, "dependencies": {"@ai-sdk/openai": "^1.1.9", "@ariakit/react": "^0.4.17", "@blocknote/core": "^0.33.0", "@blocknote/mantine": "^0.33.0", "@blocknote/react": "^0.33.0", "@emoji-mart/data": "^1.2.1", "@faker-js/faker": "^9.7.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toolbar": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.71.10", "@uploadthing/react": "7.1.0", "ai": "^4.1.24", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "lowlight": "^3.3.0", "lucide-react": "^0.487.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-lite-youtube-embed": "^2.4.0", "react-player": "^2.16.0", "react-textarea-autosize": "^8.5.9", "react-tweet": "^3.2.2", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uploadthing": "7.2.0", "use-file-picker": "^2.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.8.0", "@graphql-codegen/schema-ast": "^4.1.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.4", "graphql": "^16.10.0", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@9.2.0", "pnpm": {"peerDependencyRules": {"allowAny": ["react", "react-dom"], "ignoreMissing": ["scheduler"]}}}