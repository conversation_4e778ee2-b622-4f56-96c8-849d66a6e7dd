(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/72ed8_@mantine_core_esm_core_cad5b088._.js", {

"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "em": (()=>em),
    "rem": (()=>rem)
});
function scaleRem(remValue) {
    if (remValue === "0rem") {
        return "0rem";
    }
    return `calc(${remValue} * var(--mantine-scale))`;
}
function createConverter(units, { shouldScale = false } = {}) {
    function converter(value) {
        if (value === 0 || value === "0") {
            return `0${units}`;
        }
        if (typeof value === "number") {
            const val = `${value / 16}${units}`;
            return shouldScale ? scaleRem(val) : val;
        }
        if (typeof value === "string") {
            if (value === "") {
                return value;
            }
            if (value.startsWith("calc(") || value.startsWith("clamp(") || value.includes("rgba(")) {
                return value;
            }
            if (value.includes(",")) {
                return value.split(",").map((val)=>converter(val)).join(",");
            }
            if (value.includes(" ")) {
                return value.split(" ").map((val)=>converter(val)).join(" ");
            }
            if (value.includes(units)) {
                return shouldScale ? scaleRem(value) : value;
            }
            const replaced = value.replace("px", "");
            if (!Number.isNaN(Number(replaced))) {
                const val = `${Number(replaced) / 16}${units}`;
                return shouldScale ? scaleRem(val) : val;
            }
        }
        return value;
    }
    return converter;
}
const rem = createConverter("rem", {
    shouldScale: true
});
const em = createConverter("em");
;
 //# sourceMappingURL=rem.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isNumberLike": (()=>isNumberLike)
});
'use client';
function isNumberLike(value) {
    if (typeof value === "number") {
        return true;
    }
    if (typeof value === "string") {
        if (value.startsWith("calc(") || value.startsWith("var(") || value.includes(" ") && value.trim() !== "") {
            return true;
        }
        const cssUnitsRegex = /^[+-]?[0-9]+(\.[0-9]+)?(px|em|rem|ex|ch|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cm|mm|in|pt|pc|q|cqw|cqh|cqi|cqb|cqmin|cqmax|%)?$/;
        const values = value.trim().split(/\s+/);
        return values.every((val)=>cssUnitsRegex.test(val));
    }
    return false;
}
;
 //# sourceMappingURL=is-number-like.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/get-size/get-size.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getFontSize": (()=>getFontSize),
    "getLineHeight": (()=>getLineHeight),
    "getRadius": (()=>getRadius),
    "getShadow": (()=>getShadow),
    "getSize": (()=>getSize),
    "getSpacing": (()=>getSpacing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$is$2d$number$2d$like$2f$is$2d$number$2d$like$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
'use client';
;
;
function getSize(size, prefix = "size", convertToRem = true) {
    if (size === void 0) {
        return void 0;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$is$2d$number$2d$like$2f$is$2d$number$2d$like$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumberLike"])(size) ? convertToRem ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(size) : size : `var(--${prefix}-${size})`;
}
function getSpacing(size) {
    return getSize(size, "mantine-spacing");
}
function getRadius(size) {
    if (size === void 0) {
        return "var(--mantine-radius-default)";
    }
    return getSize(size, "mantine-radius");
}
function getFontSize(size) {
    return getSize(size, "mantine-font-size");
}
function getLineHeight(size) {
    return getSize(size, "mantine-line-height", false);
}
function getShadow(size) {
    if (!size) {
        return void 0;
    }
    return getSize(size, "mantine-shadow", false);
}
;
 //# sourceMappingURL=get-size.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/create-vars-resolver/create-vars-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createVarsResolver": (()=>createVarsResolver)
});
'use client';
function createVarsResolver(resolver) {
    return resolver;
}
;
 //# sourceMappingURL=create-vars-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "filterProps": (()=>filterProps)
});
'use client';
function filterProps(props) {
    return Object.keys(props).reduce((acc, key)=>{
        if (props[key] !== void 0) {
            acc[key] = props[key];
        }
        return acc;
    }, {});
}
;
 //# sourceMappingURL=filter-props.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/to-rgba/to-rgba.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "toRgba": (()=>toRgba)
});
function isHexColor(hex) {
    const HEX_REGEXP = /^#?([0-9A-F]{3}){1,2}([0-9A-F]{2})?$/i;
    return HEX_REGEXP.test(hex);
}
function hexToRgba(color) {
    let hexString = color.replace("#", "");
    if (hexString.length === 3) {
        const shorthandHex = hexString.split("");
        hexString = [
            shorthandHex[0],
            shorthandHex[0],
            shorthandHex[1],
            shorthandHex[1],
            shorthandHex[2],
            shorthandHex[2]
        ].join("");
    }
    if (hexString.length === 8) {
        const alpha = parseInt(hexString.slice(6, 8), 16) / 255;
        return {
            r: parseInt(hexString.slice(0, 2), 16),
            g: parseInt(hexString.slice(2, 4), 16),
            b: parseInt(hexString.slice(4, 6), 16),
            a: alpha
        };
    }
    const parsed = parseInt(hexString, 16);
    const r = parsed >> 16 & 255;
    const g = parsed >> 8 & 255;
    const b = parsed & 255;
    return {
        r,
        g,
        b,
        a: 1
    };
}
function rgbStringToRgba(color) {
    const [r, g, b, a] = color.replace(/[^0-9,./]/g, "").split(/[/,]/).map(Number);
    return {
        r,
        g,
        b,
        a: a === void 0 ? 1 : a
    };
}
function hslStringToRgba(hslaString) {
    const hslaRegex = /^hsla?\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*(,\s*(0?\.\d+|\d+(\.\d+)?))?\s*\)$/i;
    const matches = hslaString.match(hslaRegex);
    if (!matches) {
        return {
            r: 0,
            g: 0,
            b: 0,
            a: 1
        };
    }
    const h = parseInt(matches[1], 10);
    const s = parseInt(matches[2], 10) / 100;
    const l = parseInt(matches[3], 10) / 100;
    const a = matches[5] ? parseFloat(matches[5]) : void 0;
    const chroma = (1 - Math.abs(2 * l - 1)) * s;
    const huePrime = h / 60;
    const x = chroma * (1 - Math.abs(huePrime % 2 - 1));
    const m = l - chroma / 2;
    let r;
    let g;
    let b;
    if (huePrime >= 0 && huePrime < 1) {
        r = chroma;
        g = x;
        b = 0;
    } else if (huePrime >= 1 && huePrime < 2) {
        r = x;
        g = chroma;
        b = 0;
    } else if (huePrime >= 2 && huePrime < 3) {
        r = 0;
        g = chroma;
        b = x;
    } else if (huePrime >= 3 && huePrime < 4) {
        r = 0;
        g = x;
        b = chroma;
    } else if (huePrime >= 4 && huePrime < 5) {
        r = x;
        g = 0;
        b = chroma;
    } else {
        r = chroma;
        g = 0;
        b = x;
    }
    return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255),
        a: a || 1
    };
}
function toRgba(color) {
    if (isHexColor(color)) {
        return hexToRgba(color);
    }
    if (color.startsWith("rgb")) {
        return rgbStringToRgba(color);
    }
    if (color.startsWith("hsl")) {
        return hslStringToRgba(color);
    }
    return {
        r: 0,
        g: 0,
        b: 0,
        a: 1
    };
}
;
 //# sourceMappingURL=to-rgba.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/darken/darken.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "darken": (()=>darken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/to-rgba/to-rgba.mjs [app-client] (ecmascript)");
;
function darken(color, alpha) {
    if (color.startsWith("var(")) {
        return `color-mix(in srgb, ${color}, black ${alpha * 100}%)`;
    }
    const { r, g, b, a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toRgba"])(color);
    const f = 1 - alpha;
    const dark = (input)=>Math.round(input * f);
    return `rgba(${dark(r)}, ${dark(g)}, ${dark(b)}, ${a})`;
}
;
 //# sourceMappingURL=darken.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPrimaryShade": (()=>getPrimaryShade)
});
'use client';
function getPrimaryShade(theme, colorScheme) {
    if (typeof theme.primaryShade === "number") {
        return theme.primaryShade;
    }
    if (colorScheme === "dark") {
        return theme.primaryShade.dark;
    }
    return theme.primaryShade.light;
}
;
 //# sourceMappingURL=get-primary-shade.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/luminance/luminance.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isLightColor": (()=>isLightColor),
    "luminance": (()=>luminance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/to-rgba/to-rgba.mjs [app-client] (ecmascript)");
;
function gammaCorrect(c) {
    return c <= 0.03928 ? c / 12.92 : ((c + 0.055) / 1.055) ** 2.4;
}
function getLightnessFromOklch(oklchColor) {
    const match = oklchColor.match(/oklch\((.*?)%\s/);
    return match ? parseFloat(match[1]) : null;
}
function luminance(color) {
    if (color.startsWith("oklch(")) {
        return (getLightnessFromOklch(color) || 0) / 100;
    }
    const { r, g, b } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toRgba"])(color);
    const sR = r / 255;
    const sG = g / 255;
    const sB = b / 255;
    const rLinear = gammaCorrect(sR);
    const gLinear = gammaCorrect(sG);
    const bLinear = gammaCorrect(sB);
    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
}
function isLightColor(color, luminanceThreshold = 0.179) {
    if (color.startsWith("var(")) {
        return false;
    }
    return luminance(color) > luminanceThreshold;
}
;
 //# sourceMappingURL=luminance.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "parseThemeColor": (()=>parseThemeColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/luminance/luminance.mjs [app-client] (ecmascript)");
'use client';
;
;
function parseThemeColor({ color, theme, colorScheme }) {
    if (typeof color !== "string") {
        throw new Error(`[@mantine/core] Failed to parse color. Expected color to be a string, instead got ${typeof color}`);
    }
    if (color === "bright") {
        return {
            color,
            value: colorScheme === "dark" ? theme.white : theme.black,
            shade: void 0,
            isThemeColor: false,
            isLight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLightColor"])(colorScheme === "dark" ? theme.white : theme.black, theme.luminanceThreshold),
            variable: "--mantine-color-bright"
        };
    }
    if (color === "dimmed") {
        return {
            color,
            value: colorScheme === "dark" ? theme.colors.dark[2] : theme.colors.gray[7],
            shade: void 0,
            isThemeColor: false,
            isLight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLightColor"])(colorScheme === "dark" ? theme.colors.dark[2] : theme.colors.gray[6], theme.luminanceThreshold),
            variable: "--mantine-color-dimmed"
        };
    }
    if (color === "white" || color === "black") {
        return {
            color,
            value: color === "white" ? theme.white : theme.black,
            shade: void 0,
            isThemeColor: false,
            isLight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLightColor"])(color === "white" ? theme.white : theme.black, theme.luminanceThreshold),
            variable: `--mantine-color-${color}`
        };
    }
    const [_color, shade] = color.split(".");
    const colorShade = shade ? Number(shade) : void 0;
    const isThemeColor = _color in theme.colors;
    if (isThemeColor) {
        const colorValue = colorShade !== void 0 ? theme.colors[_color][colorShade] : theme.colors[_color][(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryShade"])(theme, colorScheme || "light")];
        return {
            color: _color,
            value: colorValue,
            shade: colorShade,
            isThemeColor,
            isLight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLightColor"])(colorValue, theme.luminanceThreshold),
            variable: shade ? `--mantine-color-${_color}-${colorShade}` : `--mantine-color-${_color}-filled`
        };
    }
    return {
        color,
        value: color,
        isThemeColor,
        isLight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$luminance$2f$luminance$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isLightColor"])(color, theme.luminanceThreshold),
        shade: colorShade,
        variable: void 0
    };
}
;
 //# sourceMappingURL=parse-theme-color.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getThemeColor": (()=>getThemeColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs [app-client] (ecmascript)");
'use client';
;
function getThemeColor(color, theme) {
    const parsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseThemeColor"])({
        color: color || theme.primaryColor,
        theme
    });
    return parsed.variable ? `var(${parsed.variable})` : color;
}
;
 //# sourceMappingURL=get-theme-color.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getGradient": (()=>getGradient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$theme$2d$color$2f$get$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-theme-color/get-theme-color.mjs [app-client] (ecmascript)");
'use client';
;
function getGradient(gradient, theme) {
    const merged = {
        from: gradient?.from || theme.defaultGradient.from,
        to: gradient?.to || theme.defaultGradient.to,
        deg: gradient?.deg ?? theme.defaultGradient.deg ?? 0
    };
    const fromColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$theme$2d$color$2f$get$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeColor"])(merged.from, theme);
    const toColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$theme$2d$color$2f$get$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeColor"])(merged.to, theme);
    return `linear-gradient(${merged.deg}deg, ${fromColor} 0%, ${toColor} 100%)`;
}
;
 //# sourceMappingURL=get-gradient.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/rgba/rgba.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "alpha": (()=>alpha),
    "rgba": (()=>rgba)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/to-rgba/to-rgba.mjs [app-client] (ecmascript)");
;
function rgba(color, alpha2) {
    if (typeof color !== "string" || alpha2 > 1 || alpha2 < 0) {
        return "rgba(0, 0, 0, 1)";
    }
    if (color.startsWith("var(")) {
        const mixPercentage = (1 - alpha2) * 100;
        return `color-mix(in srgb, ${color}, transparent ${mixPercentage}%)`;
    }
    if (color.startsWith("oklch")) {
        if (color.includes("/")) {
            return color.replace(/\/\s*[\d.]+\s*\)/, `/ ${alpha2})`);
        }
        return color.replace(")", ` / ${alpha2})`);
    }
    const { r, g, b } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$to$2d$rgba$2f$to$2d$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toRgba"])(color);
    return `rgba(${r}, ${g}, ${b}, ${alpha2})`;
}
const alpha = rgba;
;
 //# sourceMappingURL=rgba.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultVariantColorsResolver": (()=>defaultVariantColorsResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$darken$2f$darken$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/darken/darken.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$gradient$2f$get$2d$gradient$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-gradient/get-gradient.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/rgba/rgba.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
const defaultVariantColorsResolver = ({ color, theme, variant, gradient, autoContrast })=>{
    const parsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseThemeColor"])({
        color,
        theme
    });
    const _autoContrast = typeof autoContrast === "boolean" ? autoContrast : theme.autoContrast;
    if (variant === "filled") {
        const textColor = _autoContrast ? parsed.isLight ? "var(--mantine-color-black)" : "var(--mantine-color-white)" : "var(--mantine-color-white)";
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: `var(--mantine-color-${color}-filled)`,
                    hover: `var(--mantine-color-${color}-filled-hover)`,
                    color: textColor,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
                };
            }
            return {
                background: `var(--mantine-color-${parsed.color}-${parsed.shade})`,
                hover: `var(--mantine-color-${parsed.color}-${parsed.shade === 9 ? 8 : parsed.shade + 1})`,
                color: textColor,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
            };
        }
        return {
            background: color,
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$darken$2f$darken$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["darken"])(color, 0.1),
            color: textColor,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
        };
    }
    if (variant === "light") {
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: `var(--mantine-color-${color}-light)`,
                    hover: `var(--mantine-color-${color}-light-hover)`,
                    color: `var(--mantine-color-${color}-light-color)`,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
                };
            }
            const parsedColor = theme.colors[parsed.color][parsed.shade];
            return {
                background: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(parsedColor, 0.1),
                hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(parsedColor, 0.12),
                color: `var(--mantine-color-${parsed.color}-${Math.min(parsed.shade, 6)})`,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
            };
        }
        return {
            background: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(color, 0.1),
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(color, 0.12),
            color,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
        };
    }
    if (variant === "outline") {
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: "transparent",
                    hover: `var(--mantine-color-${color}-outline-hover)`,
                    color: `var(--mantine-color-${color}-outline)`,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid var(--mantine-color-${color}-outline)`
                };
            }
            return {
                background: "transparent",
                hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(theme.colors[parsed.color][parsed.shade], 0.05),
                color: `var(--mantine-color-${parsed.color}-${parsed.shade})`,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid var(--mantine-color-${parsed.color}-${parsed.shade})`
            };
        }
        return {
            background: "transparent",
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(color, 0.05),
            color,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid ${color}`
        };
    }
    if (variant === "subtle") {
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: "transparent",
                    hover: `var(--mantine-color-${color}-light-hover)`,
                    color: `var(--mantine-color-${color}-light-color)`,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
                };
            }
            const parsedColor = theme.colors[parsed.color][parsed.shade];
            return {
                background: "transparent",
                hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(parsedColor, 0.12),
                color: `var(--mantine-color-${parsed.color}-${Math.min(parsed.shade, 6)})`,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
            };
        }
        return {
            background: "transparent",
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgba"])(color, 0.12),
            color,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
        };
    }
    if (variant === "transparent") {
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: "transparent",
                    hover: "transparent",
                    color: `var(--mantine-color-${color}-light-color)`,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
                };
            }
            return {
                background: "transparent",
                hover: "transparent",
                color: `var(--mantine-color-${parsed.color}-${Math.min(parsed.shade, 6)})`,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
            };
        }
        return {
            background: "transparent",
            hover: "transparent",
            color,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
        };
    }
    if (variant === "white") {
        if (parsed.isThemeColor) {
            if (parsed.shade === void 0) {
                return {
                    background: "var(--mantine-color-white)",
                    hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$darken$2f$darken$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["darken"])(theme.white, 0.01),
                    color: `var(--mantine-color-${color}-filled)`,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
                };
            }
            return {
                background: "var(--mantine-color-white)",
                hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$darken$2f$darken$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["darken"])(theme.white, 0.01),
                color: `var(--mantine-color-${parsed.color}-${parsed.shade})`,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
            };
        }
        return {
            background: "var(--mantine-color-white)",
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$darken$2f$darken$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["darken"])(theme.white, 0.01),
            color,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid transparent`
        };
    }
    if (variant === "gradient") {
        return {
            background: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$gradient$2f$get$2d$gradient$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGradient"])(gradient, theme),
            hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$gradient$2f$get$2d$gradient$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGradient"])(gradient, theme),
            color: "var(--mantine-color-white)",
            border: "none"
        };
    }
    if (variant === "default") {
        return {
            background: "var(--mantine-color-default)",
            hover: "var(--mantine-color-default-hover)",
            color: "var(--mantine-color-default-color)",
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} solid var(--mantine-color-default-border)`
        };
    }
    return {};
};
;
 //# sourceMappingURL=default-variant-colors-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/default-colors.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_COLORS": (()=>DEFAULT_COLORS)
});
const DEFAULT_COLORS = {
    dark: [
        "#C9C9C9",
        "#b8b8b8",
        "#828282",
        "#696969",
        "#424242",
        "#3b3b3b",
        "#2e2e2e",
        "#242424",
        "#1f1f1f",
        "#141414"
    ],
    gray: [
        "#f8f9fa",
        "#f1f3f5",
        "#e9ecef",
        "#dee2e6",
        "#ced4da",
        "#adb5bd",
        "#868e96",
        "#495057",
        "#343a40",
        "#212529"
    ],
    red: [
        "#fff5f5",
        "#ffe3e3",
        "#ffc9c9",
        "#ffa8a8",
        "#ff8787",
        "#ff6b6b",
        "#fa5252",
        "#f03e3e",
        "#e03131",
        "#c92a2a"
    ],
    pink: [
        "#fff0f6",
        "#ffdeeb",
        "#fcc2d7",
        "#faa2c1",
        "#f783ac",
        "#f06595",
        "#e64980",
        "#d6336c",
        "#c2255c",
        "#a61e4d"
    ],
    grape: [
        "#f8f0fc",
        "#f3d9fa",
        "#eebefa",
        "#e599f7",
        "#da77f2",
        "#cc5de8",
        "#be4bdb",
        "#ae3ec9",
        "#9c36b5",
        "#862e9c"
    ],
    violet: [
        "#f3f0ff",
        "#e5dbff",
        "#d0bfff",
        "#b197fc",
        "#9775fa",
        "#845ef7",
        "#7950f2",
        "#7048e8",
        "#6741d9",
        "#5f3dc4"
    ],
    indigo: [
        "#edf2ff",
        "#dbe4ff",
        "#bac8ff",
        "#91a7ff",
        "#748ffc",
        "#5c7cfa",
        "#4c6ef5",
        "#4263eb",
        "#3b5bdb",
        "#364fc7"
    ],
    blue: [
        "#e7f5ff",
        "#d0ebff",
        "#a5d8ff",
        "#74c0fc",
        "#4dabf7",
        "#339af0",
        "#228be6",
        "#1c7ed6",
        "#1971c2",
        "#1864ab"
    ],
    cyan: [
        "#e3fafc",
        "#c5f6fa",
        "#99e9f2",
        "#66d9e8",
        "#3bc9db",
        "#22b8cf",
        "#15aabf",
        "#1098ad",
        "#0c8599",
        "#0b7285"
    ],
    teal: [
        "#e6fcf5",
        "#c3fae8",
        "#96f2d7",
        "#63e6be",
        "#38d9a9",
        "#20c997",
        "#12b886",
        "#0ca678",
        "#099268",
        "#087f5b"
    ],
    green: [
        "#ebfbee",
        "#d3f9d8",
        "#b2f2bb",
        "#8ce99a",
        "#69db7c",
        "#51cf66",
        "#40c057",
        "#37b24d",
        "#2f9e44",
        "#2b8a3e"
    ],
    lime: [
        "#f4fce3",
        "#e9fac8",
        "#d8f5a2",
        "#c0eb75",
        "#a9e34b",
        "#94d82d",
        "#82c91e",
        "#74b816",
        "#66a80f",
        "#5c940d"
    ],
    yellow: [
        "#fff9db",
        "#fff3bf",
        "#ffec99",
        "#ffe066",
        "#ffd43b",
        "#fcc419",
        "#fab005",
        "#f59f00",
        "#f08c00",
        "#e67700"
    ],
    orange: [
        "#fff4e6",
        "#ffe8cc",
        "#ffd8a8",
        "#ffc078",
        "#ffa94d",
        "#ff922b",
        "#fd7e14",
        "#f76707",
        "#e8590c",
        "#d9480f"
    ]
};
;
 //# sourceMappingURL=default-colors.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/default-theme.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_THEME": (()=>DEFAULT_THEME)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$default$2d$variant$2d$colors$2d$resolver$2f$default$2d$variant$2d$colors$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/default-variant-colors-resolver/default-variant-colors-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$colors$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/default-colors.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
const DEFAULT_FONT_FAMILY = "-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji";
const DEFAULT_THEME = {
    scale: 1,
    fontSmoothing: true,
    focusRing: "auto",
    white: "#fff",
    black: "#000",
    colors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$colors$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_COLORS"],
    primaryShade: {
        light: 6,
        dark: 8
    },
    primaryColor: "blue",
    variantColorResolver: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$default$2d$variant$2d$colors$2d$resolver$2f$default$2d$variant$2d$colors$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultVariantColorsResolver"],
    autoContrast: false,
    luminanceThreshold: 0.3,
    fontFamily: DEFAULT_FONT_FAMILY,
    fontFamilyMonospace: "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace",
    respectReducedMotion: false,
    cursorType: "default",
    defaultGradient: {
        from: "blue",
        to: "cyan",
        deg: 45
    },
    defaultRadius: "sm",
    activeClassName: "mantine-active",
    focusClassName: "",
    headings: {
        fontFamily: DEFAULT_FONT_FAMILY,
        fontWeight: "700",
        textWrap: "wrap",
        sizes: {
            h1: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(34),
                lineHeight: "1.3"
            },
            h2: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(26),
                lineHeight: "1.35"
            },
            h3: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(22),
                lineHeight: "1.4"
            },
            h4: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(18),
                lineHeight: "1.45"
            },
            h5: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(16),
                lineHeight: "1.5"
            },
            h6: {
                fontSize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(14),
                lineHeight: "1.5"
            }
        }
    },
    fontSizes: {
        xs: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(12),
        sm: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(14),
        md: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(16),
        lg: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(18),
        xl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(20)
    },
    lineHeights: {
        xs: "1.4",
        sm: "1.45",
        md: "1.55",
        lg: "1.6",
        xl: "1.65"
    },
    radius: {
        xs: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(2),
        sm: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(4),
        md: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(8),
        lg: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(16),
        xl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(32)
    },
    spacing: {
        xs: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(10),
        sm: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(12),
        md: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(16),
        lg: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(20),
        xl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(32)
    },
    breakpoints: {
        xs: "36em",
        sm: "48em",
        md: "62em",
        lg: "75em",
        xl: "88em"
    },
    shadows: {
        xs: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(3)} rgba(0, 0, 0, 0.05), 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(2)} rgba(0, 0, 0, 0.1)`,
        sm: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(10)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(15)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-5)}, rgba(0, 0, 0, 0.04) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(7)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(7)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-5)}`,
        md: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(20)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(25)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-5)}, rgba(0, 0, 0, 0.04) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(10)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(10)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-5)}`,
        lg: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(28)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(23)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-7)}, rgba(0, 0, 0, 0.04) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(12)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(12)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-7)}`,
        xl: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(1)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(36)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(28)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-7)}, rgba(0, 0, 0, 0.04) 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(17)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(17)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(-7)}`
    },
    other: {},
    components: {}
};
;
 //# sourceMappingURL=default-theme.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/deep-merge/deep-merge.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deepMerge": (()=>deepMerge)
});
function isObject(item) {
    return item && typeof item === "object" && !Array.isArray(item);
}
function deepMerge(target, source) {
    const result = {
        ...target
    };
    const _source = source;
    if (isObject(target) && isObject(source)) {
        Object.keys(source).forEach((key)=>{
            if (isObject(_source[key])) {
                if (!(key in target)) {
                    result[key] = _source[key];
                } else {
                    result[key] = deepMerge(result[key], _source[key]);
                }
            } else {
                result[key] = _source[key];
            }
        });
    }
    return result;
}
;
 //# sourceMappingURL=deep-merge.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/merge-mantine-theme/merge-mantine-theme.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "INVALID_PRIMARY_COLOR_ERROR": (()=>INVALID_PRIMARY_COLOR_ERROR),
    "INVALID_PRIMARY_SHADE_ERROR": (()=>INVALID_PRIMARY_SHADE_ERROR),
    "mergeMantineTheme": (()=>mergeMantineTheme),
    "validateMantineTheme": (()=>validateMantineTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$deep$2d$merge$2f$deep$2d$merge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/deep-merge/deep-merge.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
;
;
const INVALID_PRIMARY_COLOR_ERROR = "[@mantine/core] MantineProvider: Invalid theme.primaryColor, it accepts only key of theme.colors, learn more \u2013 https://mantine.dev/theming/colors/#primary-color";
const INVALID_PRIMARY_SHADE_ERROR = "[@mantine/core] MantineProvider: Invalid theme.primaryShade, it accepts only 0-9 integers or an object { light: 0-9, dark: 0-9 }";
function isValidPrimaryShade(shade) {
    if (shade < 0 || shade > 9) {
        return false;
    }
    return parseInt(shade.toString(), 10) === shade;
}
function validateMantineTheme(theme) {
    if (!(theme.primaryColor in theme.colors)) {
        throw new Error(INVALID_PRIMARY_COLOR_ERROR);
    }
    if (typeof theme.primaryShade === "object") {
        if (!isValidPrimaryShade(theme.primaryShade.dark) || !isValidPrimaryShade(theme.primaryShade.light)) {
            throw new Error(INVALID_PRIMARY_SHADE_ERROR);
        }
    }
    if (typeof theme.primaryShade === "number" && !isValidPrimaryShade(theme.primaryShade)) {
        throw new Error(INVALID_PRIMARY_SHADE_ERROR);
    }
}
function mergeMantineTheme(currentTheme, themeOverride) {
    if (!themeOverride) {
        validateMantineTheme(currentTheme);
        return currentTheme;
    }
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$deep$2d$merge$2f$deep$2d$merge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deepMerge"])(currentTheme, themeOverride);
    if (themeOverride.fontFamily && !themeOverride.headings?.fontFamily) {
        result.headings.fontFamily = themeOverride.fontFamily;
    }
    validateMantineTheme(result);
    return result;
}
;
 //# sourceMappingURL=merge-mantine-theme.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MantineThemeContext": (()=>MantineThemeContext),
    "MantineThemeProvider": (()=>MantineThemeProvider),
    "useMantineTheme": (()=>useMantineTheme),
    "useSafeMantineTheme": (()=>useSafeMantineTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/default-theme.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$merge$2d$mantine$2d$theme$2f$merge$2d$mantine$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/merge-mantine-theme/merge-mantine-theme.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
const MantineThemeContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useSafeMantineTheme = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MantineThemeContext) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_THEME"];
function useMantineTheme() {
    const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MantineThemeContext);
    if (!ctx) {
        throw new Error("@mantine/core: MantineProvider was not found in component tree, make sure you have it in your app");
    }
    return ctx;
}
function MantineThemeProvider({ theme, children, inherit = true }) {
    const parentTheme = useSafeMantineTheme();
    const mergedTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MantineThemeProvider.useMemo[mergedTheme]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$merge$2d$mantine$2d$theme$2f$merge$2d$mantine$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeMantineTheme"])(inherit ? parentTheme : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_THEME"], theme)
    }["MantineThemeProvider.useMemo[mergedTheme]"], [
        theme,
        parentTheme,
        inherit
    ]);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(MantineThemeContext.Provider, {
        value: mergedTheme,
        children
    });
}
MantineThemeProvider.displayName = "@mantine/core/MantineThemeProvider";
;
 //# sourceMappingURL=MantineThemeProvider.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/use-props/use-props.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useProps": (()=>useProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function useProps(component, defaultProps, props) {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const contextPropsPayload = theme.components[component]?.defaultProps;
    const contextProps = typeof contextPropsPayload === "function" ? contextPropsPayload(theme) : contextPropsPayload;
    return {
        ...defaultProps,
        ...contextProps,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["filterProps"])(props)
    };
}
;
 //# sourceMappingURL=use-props.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MantineContext": (()=>MantineContext),
    "useMantineClassNamesPrefix": (()=>useMantineClassNamesPrefix),
    "useMantineContext": (()=>useMantineContext),
    "useMantineCssVariablesResolver": (()=>useMantineCssVariablesResolver),
    "useMantineEnv": (()=>useMantineEnv),
    "useMantineIsHeadless": (()=>useMantineIsHeadless),
    "useMantineStyleNonce": (()=>useMantineStyleNonce),
    "useMantineStylesTransform": (()=>useMantineStylesTransform),
    "useMantineSxTransform": (()=>useMantineSxTransform),
    "useMantineWithStaticClasses": (()=>useMantineWithStaticClasses)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
const MantineContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function useMantineContext() {
    const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MantineContext);
    if (!ctx) {
        throw new Error("[@mantine/core] MantineProvider was not found in tree");
    }
    return ctx;
}
function useMantineCssVariablesResolver() {
    return useMantineContext().cssVariablesResolver;
}
function useMantineClassNamesPrefix() {
    return useMantineContext().classNamesPrefix;
}
function useMantineStyleNonce() {
    return useMantineContext().getStyleNonce;
}
function useMantineWithStaticClasses() {
    return useMantineContext().withStaticClasses;
}
function useMantineIsHeadless() {
    return useMantineContext().headless;
}
function useMantineSxTransform() {
    return useMantineContext().stylesTransform?.sx;
}
function useMantineStylesTransform() {
    return useMantineContext().stylesTransform?.styles;
}
function useMantineEnv() {
    return useMantineContext().env || "default";
}
;
 //# sourceMappingURL=Mantine.context.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FOCUS_CLASS_NAMES": (()=>FOCUS_CLASS_NAMES),
    "getGlobalClassNames": (()=>getGlobalClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
'use client';
;
const FOCUS_CLASS_NAMES = {
    always: "mantine-focus-always",
    auto: "mantine-focus-auto",
    never: "mantine-focus-never"
};
function getGlobalClassNames({ theme, options, unstyled }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options?.focusable && !unstyled && (theme.focusClassName || FOCUS_CLASS_NAMES[theme.focusRing]), options?.active && !unstyled && theme.activeClassName);
}
;
 //# sourceMappingURL=get-global-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resolveClassNames": (()=>resolveClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
'use client';
;
const EMPTY_CLASS_NAMES = {};
function mergeClassNames(objects) {
    const merged = {};
    objects.forEach((obj)=>{
        Object.entries(obj).forEach(([key, value])=>{
            if (merged[key]) {
                merged[key] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(merged[key], value);
            } else {
                merged[key] = value;
            }
        });
    });
    return merged;
}
function resolveClassNames({ theme, classNames, props, stylesCtx }) {
    const arrayClassNames = Array.isArray(classNames) ? classNames : [
        classNames
    ];
    const resolvedClassNames = arrayClassNames.map((item)=>typeof item === "function" ? item(theme, props, stylesCtx) : item || EMPTY_CLASS_NAMES);
    return mergeClassNames(resolvedClassNames);
}
;
 //# sourceMappingURL=resolve-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-options-class-names/get-options-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getOptionsClassNames": (()=>getOptionsClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs [app-client] (ecmascript)");
'use client';
;
function getOptionsClassNames({ selector, stylesCtx, options, props, theme }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveClassNames"])({
        theme,
        classNames: options?.classNames,
        props: options?.props || props,
        stylesCtx
    })[selector];
}
;
 //# sourceMappingURL=get-options-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-resolved-class-names/get-resolved-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getResolvedClassNames": (()=>getResolvedClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs [app-client] (ecmascript)");
'use client';
;
function getResolvedClassNames({ selector, stylesCtx, theme, classNames, props }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveClassNames"])({
        theme,
        classNames,
        props,
        stylesCtx
    })[selector];
}
;
 //# sourceMappingURL=get-resolved-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-root-class-name/get-root-class-name.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getRootClassName": (()=>getRootClassName)
});
'use client';
function getRootClassName({ rootSelector, selector, className }) {
    return rootSelector === selector ? className : void 0;
}
;
 //# sourceMappingURL=get-root-class-name.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-selector-class-name/get-selector-class-name.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSelectorClassName": (()=>getSelectorClassName)
});
'use client';
function getSelectorClassName({ selector, classes, unstyled }) {
    return unstyled ? void 0 : classes[selector];
}
;
 //# sourceMappingURL=get-selector-class-name.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-static-class-names/get-static-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getStaticClassNames": (()=>getStaticClassNames)
});
'use client';
function getStaticClassNames({ themeName, classNamesPrefix, selector, withStaticClass }) {
    if (withStaticClass === false) {
        return [];
    }
    return themeName.map((n)=>`${classNamesPrefix}-${n}-${selector}`);
}
;
 //# sourceMappingURL=get-static-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-theme-class-names/get-theme-class-names.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getThemeClassNames": (()=>getThemeClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs [app-client] (ecmascript)");
'use client';
;
function getThemeClassNames({ themeName, theme, selector, props, stylesCtx }) {
    return themeName.map((n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveClassNames"])({
            theme,
            classNames: theme.components[n]?.classNames,
            props,
            stylesCtx
        })?.[selector]);
}
;
 //# sourceMappingURL=get-theme-class-names.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-variant-class-name/get-variant-class-name.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getVariantClassName": (()=>getVariantClassName)
});
'use client';
function getVariantClassName({ options, classes, selector, unstyled }) {
    return options?.variant && !unstyled ? classes[`${selector}--${options.variant}`] : void 0;
}
;
 //# sourceMappingURL=get-variant-class-name.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-class-name.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getClassName": (()=>getClassName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$global$2d$class$2d$names$2f$get$2d$global$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-global-class-names/get-global-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$options$2d$class$2d$names$2f$get$2d$options$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-options-class-names/get-options-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$resolved$2d$class$2d$names$2f$get$2d$resolved$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-resolved-class-names/get-resolved-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$root$2d$class$2d$name$2f$get$2d$root$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-root-class-name/get-root-class-name.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$selector$2d$class$2d$name$2f$get$2d$selector$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-selector-class-name/get-selector-class-name.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$static$2d$class$2d$names$2f$get$2d$static$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-static-class-names/get-static-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$theme$2d$class$2d$names$2f$get$2d$theme$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-theme-class-names/get-theme-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$variant$2d$class$2d$name$2f$get$2d$variant$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-variant-class-name/get-variant-class-name.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function getClassName({ theme, options, themeName, selector, classNamesPrefix, classNames, classes, unstyled, className, rootSelector, props, stylesCtx, withStaticClasses, headless, transformedStyles }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$global$2d$class$2d$names$2f$get$2d$global$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGlobalClassNames"])({
        theme,
        options,
        unstyled: unstyled || headless
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$theme$2d$class$2d$names$2f$get$2d$theme$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeClassNames"])({
        theme,
        themeName,
        selector,
        props,
        stylesCtx
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$variant$2d$class$2d$name$2f$get$2d$variant$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVariantClassName"])({
        options,
        classes,
        selector,
        unstyled
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$resolved$2d$class$2d$names$2f$get$2d$resolved$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getResolvedClassNames"])({
        selector,
        stylesCtx,
        theme,
        classNames,
        props
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$resolved$2d$class$2d$names$2f$get$2d$resolved$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getResolvedClassNames"])({
        selector,
        stylesCtx,
        theme,
        classNames: transformedStyles,
        props
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$options$2d$class$2d$names$2f$get$2d$options$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOptionsClassNames"])({
        selector,
        stylesCtx,
        options,
        props,
        theme
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$root$2d$class$2d$name$2f$get$2d$root$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRootClassName"])({
        rootSelector,
        selector,
        className
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$selector$2d$class$2d$name$2f$get$2d$selector$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSelectorClassName"])({
        selector,
        classes,
        unstyled: unstyled || headless
    }), withStaticClasses && !headless && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$static$2d$class$2d$names$2f$get$2d$static$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStaticClassNames"])({
        themeName,
        classNamesPrefix,
        selector,
        withStaticClass: options?.withStaticClass
    }), options?.className);
}
;
 //# sourceMappingURL=get-class-name.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resolveStyles": (()=>resolveStyles)
});
'use client';
function resolveStyles({ theme, styles, props, stylesCtx }) {
    const arrayStyles = Array.isArray(styles) ? styles : [
        styles
    ];
    return arrayStyles.reduce((acc, style)=>{
        if (typeof style === "function") {
            return {
                ...acc,
                ...style(theme, props, stylesCtx)
            };
        }
        return {
            ...acc,
            ...style
        };
    }, {});
}
;
 //# sourceMappingURL=resolve-styles.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/get-theme-styles/get-theme-styles.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getThemeStyles": (()=>getThemeStyles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs [app-client] (ecmascript)");
'use client';
;
function getThemeStyles({ theme, themeName, props, stylesCtx, selector }) {
    return themeName.map((n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyles"])({
            theme,
            styles: theme.components[n]?.styles,
            props,
            stylesCtx
        })[selector]).reduce((acc, val)=>({
            ...acc,
            ...val
        }), {});
}
;
 //# sourceMappingURL=get-theme-styles.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-style/resolve-style.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resolveStyle": (()=>resolveStyle)
});
'use client';
function resolveStyle({ style, theme }) {
    if (Array.isArray(style)) {
        return [
            ...style
        ].reduce((acc, item)=>({
                ...acc,
                ...resolveStyle({
                    style: item,
                    theme
                })
            }), {});
    }
    if (typeof style === "function") {
        return style(theme);
    }
    if (style == null) {
        return {};
    }
    return style;
}
;
 //# sourceMappingURL=resolve-style.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-vars/merge-vars.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeVars": (()=>mergeVars)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
function mergeVars(vars) {
    return vars.reduce((acc, current)=>{
        if (current) {
            Object.keys(current).forEach((key)=>{
                acc[key] = {
                    ...acc[key],
                    ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["filterProps"])(current[key])
                };
            });
        }
        return acc;
    }, {});
}
;
 //# sourceMappingURL=merge-vars.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-vars/resolve-vars.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resolveVars": (()=>resolveVars)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$vars$2f$merge$2d$vars$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-vars/merge-vars.mjs [app-client] (ecmascript)");
'use client';
;
function resolveVars({ vars, varsResolver, theme, props, stylesCtx, selector, themeName, headless }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$vars$2f$merge$2d$vars$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeVars"])([
        headless ? {} : varsResolver?.(theme, props, stylesCtx),
        ...themeName.map((name)=>theme.components?.[name]?.vars?.(theme, props, stylesCtx)),
        vars?.(theme, props, stylesCtx)
    ])?.[selector];
}
;
 //# sourceMappingURL=resolve-vars.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/get-style.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getStyle": (()=>getStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$get$2d$theme$2d$styles$2f$get$2d$theme$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/get-theme-styles/get-theme-styles.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$style$2f$resolve$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-style/resolve-style.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$vars$2f$resolve$2d$vars$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-vars/resolve-vars.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
function getStyle({ theme, themeName, selector, options, props, stylesCtx, rootSelector, styles, style, vars, varsResolver, headless, withStylesTransform }) {
    return {
        ...!withStylesTransform && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$get$2d$theme$2d$styles$2f$get$2d$theme$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeStyles"])({
            theme,
            themeName,
            props,
            stylesCtx,
            selector
        }),
        ...!withStylesTransform && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyles"])({
            theme,
            styles,
            props,
            stylesCtx
        })[selector],
        ...!withStylesTransform && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyles"])({
            theme,
            styles: options?.styles,
            props: options?.props || props,
            stylesCtx
        })[selector],
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$vars$2f$resolve$2d$vars$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveVars"])({
            theme,
            props,
            stylesCtx,
            vars,
            varsResolver,
            selector,
            themeName,
            headless
        }),
        ...rootSelector === selector ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$style$2f$resolve$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyle"])({
            style,
            theme
        }) : null,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$style$2f$resolve$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyle"])({
            style: options?.style,
            theme
        })
    };
}
;
 //# sourceMappingURL=get-style.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/use-transformed-styles.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useStylesTransform": (()=>useStylesTransform)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function useStylesTransform({ props, stylesCtx, themeName }) {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const stylesTransform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineStylesTransform"])()?.();
    const getTransformedStyles = (styles)=>{
        if (!stylesTransform) {
            return [];
        }
        const transformedStyles = styles.map((style)=>stylesTransform(style, {
                props,
                theme,
                ctx: stylesCtx
            }));
        return [
            ...transformedStyles,
            ...themeName.map((n)=>stylesTransform(theme.components[n]?.styles, {
                    props,
                    theme,
                    ctx: stylesCtx
                }))
        ].filter(Boolean);
    };
    return {
        getTransformedStyles,
        withStylesTransform: !!stylesTransform
    };
}
;
 //# sourceMappingURL=use-transformed-styles.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/use-styles.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useStyles": (()=>useStyles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/get-class-name.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$get$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/get-style.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$use$2d$transformed$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/use-transformed-styles.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
function useStyles({ name, classes, props, stylesCtx, className, style, rootSelector = "root", unstyled, classNames, styles, vars, varsResolver }) {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const classNamesPrefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineClassNamesPrefix"])();
    const withStaticClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineWithStaticClasses"])();
    const headless = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineIsHeadless"])();
    const themeName = (Array.isArray(name) ? name : [
        name
    ]).filter((n)=>n);
    const { withStylesTransform, getTransformedStyles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$use$2d$transformed$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useStylesTransform"])({
        props,
        stylesCtx,
        themeName
    });
    return (selector, options)=>({
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$get$2d$class$2d$name$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getClassName"])({
                theme,
                options,
                themeName,
                selector,
                classNamesPrefix,
                classNames,
                classes,
                unstyled,
                className,
                rootSelector,
                props,
                stylesCtx,
                withStaticClasses,
                headless,
                transformedStyles: getTransformedStyles([
                    options?.styles,
                    styles
                ])
            }),
            style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$get$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStyle"])({
                theme,
                themeName,
                selector,
                options,
                props,
                stylesCtx,
                rootSelector,
                styles,
                style,
                vars,
                varsResolver,
                headless,
                withStylesTransform
            })
        });
}
;
 //# sourceMappingURL=use-styles.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/factory/create-polymorphic-component.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createPolymorphicComponent": (()=>createPolymorphicComponent)
});
function createPolymorphicComponent(component) {
    return component;
}
;
 //# sourceMappingURL=create-polymorphic-component.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
'use client';
function keys(object) {
    return Object.keys(object);
}
;
 //# sourceMappingURL=keys.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "camelToKebabCase": (()=>camelToKebabCase)
});
'use client';
function camelToKebabCase(value) {
    return value.replace(/[A-Z]/g, (letter)=>`-${letter.toLowerCase()}`);
}
;
 //# sourceMappingURL=camel-to-kebab-case.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/css-object-to-string/css-object-to-string.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cssObjectToString": (()=>cssObjectToString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$camel$2d$to$2d$kebab$2d$case$2f$camel$2d$to$2d$kebab$2d$case$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/camel-to-kebab-case/camel-to-kebab-case.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function cssObjectToString(css) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(css).reduce((acc, rule)=>css[rule] !== void 0 ? `${acc}${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$camel$2d$to$2d$kebab$2d$case$2f$camel$2d$to$2d$kebab$2d$case$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["camelToKebabCase"])(rule)}:${css[rule]};` : acc, "").trim();
}
;
 //# sourceMappingURL=css-object-to-string.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "stylesToString": (()=>stylesToString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$css$2d$object$2d$to$2d$string$2f$css$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/css-object-to-string/css-object-to-string.mjs [app-client] (ecmascript)");
'use client';
;
function stylesToString({ selector, styles, media, container }) {
    const baseStyles = styles ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$css$2d$object$2d$to$2d$string$2f$css$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssObjectToString"])(styles) : "";
    const mediaQueryStyles = !Array.isArray(media) ? [] : media.map((item)=>`@media${item.query}{${selector}{${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$css$2d$object$2d$to$2d$string$2f$css$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssObjectToString"])(item.styles)}}}`);
    const containerStyles = !Array.isArray(container) ? [] : container.map((item)=>`@container ${item.query}{${selector}{${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$css$2d$object$2d$to$2d$string$2f$css$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssObjectToString"])(item.styles)}}}`);
    return `${baseStyles ? `${selector}{${baseStyles}}` : ""}${mediaQueryStyles.join("")}${containerStyles.join("")}`.trim();
}
;
 //# sourceMappingURL=styles-to-string.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InlineStyles": (()=>InlineStyles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$styles$2d$to$2d$string$2f$styles$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/styles-to-string/styles-to-string.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function InlineStyles(props) {
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineStyleNonce"])();
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("style", {
        "data-mantine-styles": "inline",
        nonce: nonce?.(),
        dangerouslySetInnerHTML: {
            __html: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$styles$2d$to$2d$string$2f$styles$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stylesToString"])(props)
        }
    });
}
;
 //# sourceMappingURL=InlineStyles.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/get-box-mod/get-box-mod.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getBoxMod": (()=>getBoxMod),
    "getMod": (()=>getMod)
});
'use client';
function transformModKey(key) {
    return key.startsWith("data-") ? key : `data-${key}`;
}
function getMod(props) {
    return Object.keys(props).reduce((acc, key)=>{
        const value = props[key];
        if (value === void 0 || value === "" || value === false || value === null) {
            return acc;
        }
        acc[transformModKey(key)] = props[key];
        return acc;
    }, {});
}
function getBoxMod(mod) {
    if (!mod) {
        return null;
    }
    if (typeof mod === "string") {
        return {
            [transformModKey(mod)]: true
        };
    }
    if (Array.isArray(mod)) {
        return [
            ...mod
        ].reduce((acc, value)=>({
                ...acc,
                ...getBoxMod(value)
            }), {});
    }
    return getMod(mod);
}
;
 //# sourceMappingURL=get-box-mod.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/get-box-style/get-box-style.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getBoxStyle": (()=>getBoxStyle)
});
'use client';
function mergeStyles(styles, theme) {
    if (Array.isArray(styles)) {
        return [
            ...styles
        ].reduce((acc, item)=>({
                ...acc,
                ...mergeStyles(item, theme)
            }), {});
    }
    if (typeof styles === "function") {
        return styles(theme);
    }
    if (styles == null) {
        return {};
    }
    return styles;
}
function getBoxStyle({ theme, style, vars, styleProps }) {
    const _style = mergeStyles(style, theme);
    const _vars = mergeStyles(vars, theme);
    return {
        ..._style,
        ..._vars,
        ...styleProps
    };
}
;
 //# sourceMappingURL=get-box-style.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractStyleProps": (()=>extractStyleProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/filter-props/filter-props.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
function extractStyleProps(others) {
    const { m, mx, my, mt, mb, ml, mr, me, ms, p, px, py, pt, pb, pl, pr, pe, ps, bd, bg, c, opacity, ff, fz, fw, lts, ta, lh, fs, tt, td, w, miw, maw, h, mih, mah, bgsz, bgp, bgr, bga, pos, top, left, bottom, right, inset, display, flex, hiddenFrom, visibleFrom, lightHidden, darkHidden, sx, ...rest } = others;
    const styleProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$filter$2d$props$2f$filter$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["filterProps"])({
        m,
        mx,
        my,
        mt,
        mb,
        ml,
        mr,
        me,
        ms,
        p,
        px,
        py,
        pt,
        pb,
        pl,
        pr,
        pe,
        ps,
        bd,
        bg,
        c,
        opacity,
        ff,
        fz,
        fw,
        lts,
        ta,
        lh,
        fs,
        tt,
        td,
        w,
        miw,
        maw,
        h,
        mih,
        mah,
        bgsz,
        bgp,
        bgr,
        bga,
        pos,
        top,
        left,
        bottom,
        right,
        inset,
        display,
        flex,
        hiddenFrom,
        visibleFrom,
        lightHidden,
        darkHidden,
        sx
    });
    return {
        styleProps,
        rest
    };
}
;
 //# sourceMappingURL=extract-style-props.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STYlE_PROPS_DATA": (()=>STYlE_PROPS_DATA)
});
'use client';
const STYlE_PROPS_DATA = {
    m: {
        type: "spacing",
        property: "margin"
    },
    mt: {
        type: "spacing",
        property: "marginTop"
    },
    mb: {
        type: "spacing",
        property: "marginBottom"
    },
    ml: {
        type: "spacing",
        property: "marginLeft"
    },
    mr: {
        type: "spacing",
        property: "marginRight"
    },
    ms: {
        type: "spacing",
        property: "marginInlineStart"
    },
    me: {
        type: "spacing",
        property: "marginInlineEnd"
    },
    mx: {
        type: "spacing",
        property: "marginInline"
    },
    my: {
        type: "spacing",
        property: "marginBlock"
    },
    p: {
        type: "spacing",
        property: "padding"
    },
    pt: {
        type: "spacing",
        property: "paddingTop"
    },
    pb: {
        type: "spacing",
        property: "paddingBottom"
    },
    pl: {
        type: "spacing",
        property: "paddingLeft"
    },
    pr: {
        type: "spacing",
        property: "paddingRight"
    },
    ps: {
        type: "spacing",
        property: "paddingInlineStart"
    },
    pe: {
        type: "spacing",
        property: "paddingInlineEnd"
    },
    px: {
        type: "spacing",
        property: "paddingInline"
    },
    py: {
        type: "spacing",
        property: "paddingBlock"
    },
    bd: {
        type: "border",
        property: "border"
    },
    bg: {
        type: "color",
        property: "background"
    },
    c: {
        type: "textColor",
        property: "color"
    },
    opacity: {
        type: "identity",
        property: "opacity"
    },
    ff: {
        type: "fontFamily",
        property: "fontFamily"
    },
    fz: {
        type: "fontSize",
        property: "fontSize"
    },
    fw: {
        type: "identity",
        property: "fontWeight"
    },
    lts: {
        type: "size",
        property: "letterSpacing"
    },
    ta: {
        type: "identity",
        property: "textAlign"
    },
    lh: {
        type: "lineHeight",
        property: "lineHeight"
    },
    fs: {
        type: "identity",
        property: "fontStyle"
    },
    tt: {
        type: "identity",
        property: "textTransform"
    },
    td: {
        type: "identity",
        property: "textDecoration"
    },
    w: {
        type: "spacing",
        property: "width"
    },
    miw: {
        type: "spacing",
        property: "minWidth"
    },
    maw: {
        type: "spacing",
        property: "maxWidth"
    },
    h: {
        type: "spacing",
        property: "height"
    },
    mih: {
        type: "spacing",
        property: "minHeight"
    },
    mah: {
        type: "spacing",
        property: "maxHeight"
    },
    bgsz: {
        type: "size",
        property: "backgroundSize"
    },
    bgp: {
        type: "identity",
        property: "backgroundPosition"
    },
    bgr: {
        type: "identity",
        property: "backgroundRepeat"
    },
    bga: {
        type: "identity",
        property: "backgroundAttachment"
    },
    pos: {
        type: "identity",
        property: "position"
    },
    top: {
        type: "size",
        property: "top"
    },
    left: {
        type: "size",
        property: "left"
    },
    bottom: {
        type: "size",
        property: "bottom"
    },
    right: {
        type: "size",
        property: "right"
    },
    inset: {
        type: "size",
        property: "inset"
    },
    display: {
        type: "identity",
        property: "display"
    },
    flex: {
        type: "identity",
        property: "flex"
    }
};
;
 //# sourceMappingURL=style-props-data.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/color-resolver/color-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "colorResolver": (()=>colorResolver),
    "textColorResolver": (()=>textColorResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function colorResolver(color, theme) {
    const parsedColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseThemeColor"])({
        color,
        theme
    });
    if (parsedColor.color === "dimmed") {
        return "var(--mantine-color-dimmed)";
    }
    if (parsedColor.color === "bright") {
        return "var(--mantine-color-bright)";
    }
    return parsedColor.variable ? `var(${parsedColor.variable})` : parsedColor.color;
}
function textColorResolver(color, theme) {
    const parsedColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseThemeColor"])({
        color,
        theme
    });
    if (parsedColor.isThemeColor && parsedColor.shade === void 0) {
        return `var(--mantine-color-${parsedColor.color}-text)`;
    }
    return colorResolver(color, theme);
}
;
 //# sourceMappingURL=color-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/border-resolver/border-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "borderResolver": (()=>borderResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$color$2d$resolver$2f$color$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/color-resolver/color-resolver.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function borderResolver(value, theme) {
    if (typeof value === "number") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
    }
    if (typeof value === "string") {
        const [size, style, ...colorTuple] = value.split(" ").filter((val)=>val.trim() !== "");
        let result = `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(size)}`;
        style && (result += ` ${style}`);
        colorTuple.length > 0 && (result += ` ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$color$2d$resolver$2f$color$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colorResolver"])(colorTuple.join(" "), theme)}`);
        return result.trim();
    }
    return value;
}
;
 //# sourceMappingURL=border-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/font-family-resolver/font-family-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fontFamilyResolver": (()=>fontFamilyResolver)
});
'use client';
const values = {
    text: "var(--mantine-font-family)",
    mono: "var(--mantine-font-family-monospace)",
    monospace: "var(--mantine-font-family-monospace)",
    heading: "var(--mantine-font-family-headings)",
    headings: "var(--mantine-font-family-headings)"
};
function fontFamilyResolver(fontFamily) {
    if (typeof fontFamily === "string" && fontFamily in values) {
        return values[fontFamily];
    }
    return fontFamily;
}
;
 //# sourceMappingURL=font-family-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/font-size-resolver/font-size-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fontSizeResolver": (()=>fontSizeResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
const headings = [
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6"
];
function fontSizeResolver(value, theme) {
    if (typeof value === "string" && value in theme.fontSizes) {
        return `var(--mantine-font-size-${value})`;
    }
    if (typeof value === "string" && headings.includes(value)) {
        return `var(--mantine-${value}-font-size)`;
    }
    if (typeof value === "number") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
    }
    if (typeof value === "string") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
    }
    return value;
}
;
 //# sourceMappingURL=font-size-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/identity-resolver/identity-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "identityResolver": (()=>identityResolver)
});
'use client';
function identityResolver(value) {
    return value;
}
;
 //# sourceMappingURL=identity-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/line-height-resolver/line-height-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "lineHeightResolver": (()=>lineHeightResolver)
});
'use client';
const headings = [
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6"
];
function lineHeightResolver(value, theme) {
    if (typeof value === "string" && value in theme.lineHeights) {
        return `var(--mantine-line-height-${value})`;
    }
    if (typeof value === "string" && headings.includes(value)) {
        return `var(--mantine-${value}-line-height)`;
    }
    return value;
}
;
 //# sourceMappingURL=line-height-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/size-resolver/size-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "sizeResolver": (()=>sizeResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
function sizeResolver(value) {
    if (typeof value === "number") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
    }
    return value;
}
;
 //# sourceMappingURL=size-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/spacing-resolver/spacing-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "spacingResolver": (()=>spacingResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
'use client';
;
;
;
;
function spacingResolver(value, theme) {
    if (typeof value === "number") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
    }
    if (typeof value === "string") {
        const mod = value.replace("-", "");
        if (!(mod in theme.spacing)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(value);
        }
        const variable = `--mantine-spacing-${mod}`;
        return value.startsWith("-") ? `calc(var(${variable}) * -1)` : `var(${variable})`;
    }
    return value;
}
;
 //# sourceMappingURL=spacing-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resolvers": (()=>resolvers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$border$2d$resolver$2f$border$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/border-resolver/border-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$color$2d$resolver$2f$color$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/color-resolver/color-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$font$2d$family$2d$resolver$2f$font$2d$family$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/font-family-resolver/font-family-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$font$2d$size$2d$resolver$2f$font$2d$size$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/font-size-resolver/font-size-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$identity$2d$resolver$2f$identity$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/identity-resolver/identity-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$line$2d$height$2d$resolver$2f$line$2d$height$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/line-height-resolver/line-height-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$size$2d$resolver$2f$size$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/size-resolver/size-resolver.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$spacing$2d$resolver$2f$spacing$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/spacing-resolver/spacing-resolver.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
const resolvers = {
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$color$2d$resolver$2f$color$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colorResolver"],
    textColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$color$2d$resolver$2f$color$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["textColorResolver"],
    fontSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$font$2d$size$2d$resolver$2f$font$2d$size$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fontSizeResolver"],
    spacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$spacing$2d$resolver$2f$spacing$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["spacingResolver"],
    identity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$identity$2d$resolver$2f$identity$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["identityResolver"],
    size: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$size$2d$resolver$2f$size$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sizeResolver"],
    lineHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$line$2d$height$2d$resolver$2f$line$2d$height$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lineHeightResolver"],
    fontFamily: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$font$2d$family$2d$resolver$2f$font$2d$family$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fontFamilyResolver"],
    border: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$border$2d$resolver$2f$border$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["borderResolver"]
};
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/sort-media-queries.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "sortMediaQueries": (()=>sortMediaQueries)
});
'use client';
function replaceMediaQuery(query) {
    return query.replace("(min-width: ", "").replace("em)", "");
}
function sortMediaQueries({ media, ...props }) {
    const breakpoints = Object.keys(media);
    const sortedMedia = breakpoints.sort((a, b)=>Number(replaceMediaQuery(a)) - Number(replaceMediaQuery(b))).map((query)=>({
            query,
            styles: media[query]
        }));
    return {
        ...props,
        media: sortedMedia
    };
}
;
 //# sourceMappingURL=sort-media-queries.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "parseStyleProps": (()=>parseStyleProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/resolvers/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$parse$2d$style$2d$props$2f$sort$2d$media$2d$queries$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/sort-media-queries.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
function hasResponsiveStyles(styleProp) {
    if (typeof styleProp !== "object" || styleProp === null) {
        return false;
    }
    const breakpoints = Object.keys(styleProp);
    if (breakpoints.length === 1 && breakpoints[0] === "base") {
        return false;
    }
    return true;
}
function getBaseValue(value) {
    if (typeof value === "object" && value !== null) {
        if ("base" in value) {
            return value.base;
        }
        return void 0;
    }
    return value;
}
function getBreakpointKeys(value) {
    if (typeof value === "object" && value !== null) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(value).filter((key)=>key !== "base");
    }
    return [];
}
function getBreakpointValue(value, breakpoint) {
    if (typeof value === "object" && value !== null && breakpoint in value) {
        return value[breakpoint];
    }
    return value;
}
function parseStyleProps({ styleProps, data, theme }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$parse$2d$style$2d$props$2f$sort$2d$media$2d$queries$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sortMediaQueries"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(styleProps).reduce((acc, styleProp)=>{
        if (styleProp === "hiddenFrom" || styleProp === "visibleFrom" || styleProp === "sx") {
            return acc;
        }
        const propertyData = data[styleProp];
        const properties = Array.isArray(propertyData.property) ? propertyData.property : [
            propertyData.property
        ];
        const baseValue = getBaseValue(styleProps[styleProp]);
        if (!hasResponsiveStyles(styleProps[styleProp])) {
            properties.forEach((property)=>{
                acc.inlineStyles[property] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolvers"][propertyData.type](baseValue, theme);
            });
            return acc;
        }
        acc.hasResponsiveStyles = true;
        const breakpoints = getBreakpointKeys(styleProps[styleProp]);
        properties.forEach((property)=>{
            if (baseValue) {
                acc.styles[property] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolvers"][propertyData.type](baseValue, theme);
            }
            breakpoints.forEach((breakpoint)=>{
                const bp = `(min-width: ${theme.breakpoints[breakpoint]})`;
                acc.media[bp] = {
                    ...acc.media[bp],
                    [property]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$resolvers$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolvers"][propertyData.type](getBreakpointValue(styleProps[styleProp], breakpoint), theme)
                };
            });
        });
        return acc;
    }, {
        hasResponsiveStyles: false,
        styles: {},
        inlineStyles: {},
        media: {}
    }));
}
;
 //# sourceMappingURL=parse-style-props.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useRandomClassName": (()=>useRandomClassName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
function useRandomClassName() {
    const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"])().replace(/:/g, "");
    return `__m__-${id}`;
}
;
 //# sourceMappingURL=use-random-classname.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/Box.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Box": (()=>Box)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$factory$2f$create$2d$polymorphic$2d$component$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/factory/create-polymorphic-component.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$InlineStyles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/InlineStyles/InlineStyles.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$is$2d$number$2d$like$2f$is$2d$number$2d$like$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/is-number-like/is-number-like.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$get$2d$box$2d$mod$2f$get$2d$box$2d$mod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/get-box-mod/get-box-mod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$get$2d$box$2d$style$2f$get$2d$box$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/get-box-style/get-box-style.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$extract$2d$style$2d$props$2f$extract$2d$style$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/extract-style-props/extract-style-props.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$style$2d$props$2d$data$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/style-props-data.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$parse$2d$style$2d$props$2f$parse$2d$style$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/style-props/parse-style-props/parse-style-props.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$use$2d$random$2d$classname$2f$use$2d$random$2d$classname$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/use-random-classname/use-random-classname.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const _Box = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ component, style, __vars, className, variant, mod, size, hiddenFrom, visibleFrom, lightHidden, darkHidden, renderRoot, __size, ...others }, ref)=>{
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const Element = component || "div";
    const { styleProps, rest } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$extract$2d$style$2d$props$2f$extract$2d$style$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractStyleProps"])(others);
    const useSxTransform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineSxTransform"])();
    const transformedSx = useSxTransform?.()?.(styleProps.sx);
    const responsiveClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$use$2d$random$2d$classname$2f$use$2d$random$2d$classname$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRandomClassName"])();
    const parsedStyleProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$parse$2d$style$2d$props$2f$parse$2d$style$2d$props$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseStyleProps"])({
        styleProps,
        theme,
        data: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$style$2d$props$2f$style$2d$props$2d$data$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STYlE_PROPS_DATA"]
    });
    const props = {
        ref,
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$get$2d$box$2d$style$2f$get$2d$box$2d$style$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoxStyle"])({
            theme,
            style,
            vars: __vars,
            styleProps: parsedStyleProps.inlineStyles
        }),
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, transformedSx, {
            [responsiveClassName]: parsedStyleProps.hasResponsiveStyles,
            "mantine-light-hidden": lightHidden,
            "mantine-dark-hidden": darkHidden,
            [`mantine-hidden-from-${hiddenFrom}`]: hiddenFrom,
            [`mantine-visible-from-${visibleFrom}`]: visibleFrom
        }),
        "data-variant": variant,
        "data-size": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$is$2d$number$2d$like$2f$is$2d$number$2d$like$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumberLike"])(size) ? void 0 : size || void 0,
        size: __size,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$Box$2f$get$2d$box$2d$mod$2f$get$2d$box$2d$mod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoxMod"])(mod),
        ...rest
    };
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            parsedStyleProps.hasResponsiveStyles && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$InlineStyles$2f$InlineStyles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InlineStyles"], {
                selector: `.${responsiveClassName}`,
                styles: parsedStyleProps.styles,
                media: parsedStyleProps.media
            }),
            typeof renderRoot === "function" ? renderRoot(props) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Element, {
                ...props
            })
        ]
    });
});
_Box.displayName = "@mantine/core/Box";
const Box = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$factory$2f$create$2d$polymorphic$2d$component$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPolymorphicComponent"])(_Box);
;
 //# sourceMappingURL=Box.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/factory/factory.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "factory": (()=>factory),
    "getWithProps": (()=>getWithProps),
    "identity": (()=>identity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
function identity(value) {
    return value;
}
function getWithProps(Component) {
    const _Component = Component;
    return (fixedProps)=>{
        const Extended = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(_Component, {
                ...fixedProps,
                ...props,
                ref
            }));
        Extended.extend = _Component.extend;
        Extended.displayName = `WithProps(${_Component.displayName})`;
        return Extended;
    };
}
function factory(ui) {
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ui);
    Component.extend = identity;
    Component.withProps = (fixedProps)=>{
        const Extended = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Component, {
                ...fixedProps,
                ...props,
                ref
            }));
        Extended.extend = Component.extend;
        Extended.displayName = `WithProps(${Component.displayName})`;
        return Extended;
    };
    return Component;
}
;
 //# sourceMappingURL=factory.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/factory/polymorphic-factory.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "polymorphicFactory": (()=>polymorphicFactory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$factory$2f$factory$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/factory/factory.mjs [app-client] (ecmascript)");
'use client';
;
;
;
function polymorphicFactory(ui) {
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ui);
    Component.withProps = (fixedProps)=>{
        const Extended = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Component, {
                ...fixedProps,
                ...props,
                ref
            }));
        Extended.extend = Component.extend;
        Extended.displayName = `WithProps(${Component.displayName})`;
        return Extended;
    };
    Component.extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$factory$2f$factory$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["identity"];
    return Component;
}
;
 //# sourceMappingURL=polymorphic-factory.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/is-element/is-element.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isElement": (()=>isElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
function isElement(value) {
    if (Array.isArray(value) || value === null) {
        return false;
    }
    if (typeof value === "object") {
        if (value.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"]) {
            return false;
        }
        return true;
    }
    return false;
}
;
 //# sourceMappingURL=is-element.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/get-default-z-index/get-default-z-index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDefaultZIndex": (()=>getDefaultZIndex)
});
'use client';
const elevations = {
    app: 100,
    modal: 200,
    popover: 300,
    overlay: 400,
    max: 9999
};
function getDefaultZIndex(level) {
    return elevations[level];
}
;
 //# sourceMappingURL=get-default-z-index.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/get-ref-prop/get-ref-prop.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getRefProp": (()=>getRefProp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
function getRefProp(element) {
    const version = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].version;
    if (typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].version !== "string") {
        return element?.ref;
    }
    if (version.startsWith("18.")) {
        return element?.ref;
    }
    return element?.props?.ref;
}
;
 //# sourceMappingURL=get-ref-prop.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/DirectionProvider/DirectionProvider.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DirectionContext": (()=>DirectionContext),
    "DirectionProvider": (()=>DirectionProvider),
    "useDirection": (()=>useDirection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const DirectionContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    dir: "ltr",
    toggleDirection: ()=>{},
    setDirection: ()=>{}
});
function useDirection() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(DirectionContext);
}
function DirectionProvider({ children, initialDirection = "ltr", detectDirection = true }) {
    const [dir, setDir] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialDirection);
    const setDirection = (direction)=>{
        setDir(direction);
        document.documentElement.setAttribute("dir", direction);
    };
    const toggleDirection = ()=>setDirection(dir === "ltr" ? "rtl" : "ltr");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsomorphicEffect"])({
        "DirectionProvider.useIsomorphicEffect": ()=>{
            if (detectDirection) {
                const direction = document.documentElement.getAttribute("dir");
                if (direction === "rtl" || direction === "ltr") {
                    setDirection(direction);
                }
            }
        }
    }["DirectionProvider.useIsomorphicEffect"], []);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(DirectionContext.Provider, {
        value: {
            dir,
            toggleDirection,
            setDirection
        },
        children
    });
}
;
 //# sourceMappingURL=DirectionProvider.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/Box/get-style-object/get-style-object.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getStyleObject": (()=>getStyleObject)
});
'use client';
function getStyleObject(style, theme) {
    if (Array.isArray(style)) {
        return [
            ...style
        ].reduce((acc, item)=>({
                ...acc,
                ...getStyleObject(item, theme)
            }), {});
    }
    if (typeof style === "function") {
        return style(theme);
    }
    if (style == null) {
        return {};
    }
    return style;
}
;
 //# sourceMappingURL=get-style-object.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/create-optional-context/create-optional-context.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createOptionalContext": (()=>createOptionalContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
function createOptionalContext(initialValue = null) {
    const Context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(initialValue);
    const useOptionalContext = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(Context);
    const Provider = ({ children, value })=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Context.Provider, {
            value,
            children
        });
    return [
        Provider,
        useOptionalContext
    ];
}
;
 //# sourceMappingURL=create-optional-context.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/create-safe-context/create-safe-context.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSafeContext": (()=>createSafeContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
function createSafeContext(errorMessage) {
    const Context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
    const useSafeContext = ()=>{
        const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(Context);
        if (ctx === null) {
            throw new Error(errorMessage);
        }
        return ctx;
    };
    const Provider = ({ children, value })=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Context.Provider, {
            value,
            children
        });
    return [
        Provider,
        useSafeContext
    ];
}
;
 //# sourceMappingURL=create-safe-context.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-resolved-styles-api/use-resolved-styles-api.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useResolvedStylesApi": (()=>useResolvedStylesApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-class-name/resolve-class-names/resolve-class-names.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/styles-api/use-styles/get-style/resolve-styles/resolve-styles.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function useResolvedStylesApi({ classNames, styles, props, stylesCtx }) {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    return {
        resolvedClassNames: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$class$2d$name$2f$resolve$2d$class$2d$names$2f$resolve$2d$class$2d$names$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveClassNames"])({
            theme,
            classNames,
            props,
            stylesCtx: stylesCtx || void 0
        }),
        resolvedStyles: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$styles$2d$api$2f$use$2d$styles$2f$get$2d$style$2f$resolve$2d$styles$2f$resolve$2d$styles$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveStyles"])({
            theme,
            styles,
            props,
            stylesCtx: stylesCtx || void 0
        })
    };
}
;
 //# sourceMappingURL=use-resolved-styles-api.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "findElementAncestor": (()=>findElementAncestor)
});
'use client';
function findElementAncestor(element, selector) {
    let _element = element;
    while((_element = _element.parentElement) && !_element.matches(selector)){}
    return _element;
}
;
 //# sourceMappingURL=find-element-ancestor.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/get-context-item-index/get-context-item-index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getContextItemIndex": (()=>getContextItemIndex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs [app-client] (ecmascript)");
'use client';
;
function getContextItemIndex(elementSelector, parentSelector, node) {
    if (!node) {
        return null;
    }
    return Array.from((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findElementAncestor"])(node, parentSelector)?.querySelectorAll(elementSelector) || []).findIndex((element)=>element === node);
}
;
 //# sourceMappingURL=get-context-item-index.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/use-hovered/use-hovered.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useHovered": (()=>useHovered)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
function useHovered() {
    const [hovered, setHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    const resetHovered = ()=>setHovered(-1);
    return [
        hovered,
        {
            setHovered,
            resetHovered
        }
    ];
}
;
 //# sourceMappingURL=use-hovered.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/noop/noop.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "noop": (()=>noop)
});
'use client';
const noop = ()=>{};
;
 //# sourceMappingURL=noop.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/close-on-escape/close-on-escape.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "closeOnEscape": (()=>closeOnEscape)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$noop$2f$noop$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/noop/noop.mjs [app-client] (ecmascript)");
'use client';
;
function closeOnEscape(callback, options = {
    active: true
}) {
    if (typeof callback !== "function" || !options.active) {
        return options.onKeyDown || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$noop$2f$noop$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
    }
    return (event)=>{
        if (event.key === "Escape") {
            callback(event);
            options.onTrigger?.();
        }
    };
}
;
 //# sourceMappingURL=close-on-escape.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/create-event-handler/create-event-handler.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createEventHandler": (()=>createEventHandler)
});
'use client';
function createEventHandler(parentEventHandler, eventHandler) {
    return (event)=>{
        parentEventHandler?.(event);
        eventHandler?.(event);
    };
}
;
 //# sourceMappingURL=create-event-handler.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/create-scoped-keydown-handler/create-scoped-keydown-handler.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createScopedKeydownHandler": (()=>createScopedKeydownHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/find-element-ancestor/find-element-ancestor.mjs [app-client] (ecmascript)");
'use client';
;
function getPreviousIndex(current, elements, loop) {
    for(let i = current - 1; i >= 0; i -= 1){
        if (!elements[i].disabled) {
            return i;
        }
    }
    if (loop) {
        for(let i = elements.length - 1; i > -1; i -= 1){
            if (!elements[i].disabled) {
                return i;
            }
        }
    }
    return current;
}
function getNextIndex(current, elements, loop) {
    for(let i = current + 1; i < elements.length; i += 1){
        if (!elements[i].disabled) {
            return i;
        }
    }
    if (loop) {
        for(let i = 0; i < elements.length; i += 1){
            if (!elements[i].disabled) {
                return i;
            }
        }
    }
    return current;
}
function onSameLevel(target, sibling, parentSelector) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findElementAncestor"])(target, parentSelector) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findElementAncestor"])(sibling, parentSelector);
}
function createScopedKeydownHandler({ parentSelector, siblingSelector, onKeyDown, loop = true, activateOnFocus = false, dir = "rtl", orientation }) {
    return (event)=>{
        onKeyDown?.(event);
        const elements = Array.from((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$find$2d$element$2d$ancestor$2f$find$2d$element$2d$ancestor$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findElementAncestor"])(event.currentTarget, parentSelector)?.querySelectorAll(siblingSelector) || []).filter((node)=>onSameLevel(event.currentTarget, node, parentSelector));
        const current = elements.findIndex((el)=>event.currentTarget === el);
        const _nextIndex = getNextIndex(current, elements, loop);
        const _previousIndex = getPreviousIndex(current, elements, loop);
        const nextIndex = dir === "rtl" ? _previousIndex : _nextIndex;
        const previousIndex = dir === "rtl" ? _nextIndex : _previousIndex;
        switch(event.key){
            case "ArrowRight":
                {
                    if (orientation === "horizontal") {
                        event.stopPropagation();
                        event.preventDefault();
                        elements[nextIndex].focus();
                        activateOnFocus && elements[nextIndex].click();
                    }
                    break;
                }
            case "ArrowLeft":
                {
                    if (orientation === "horizontal") {
                        event.stopPropagation();
                        event.preventDefault();
                        elements[previousIndex].focus();
                        activateOnFocus && elements[previousIndex].click();
                    }
                    break;
                }
            case "ArrowUp":
                {
                    if (orientation === "vertical") {
                        event.stopPropagation();
                        event.preventDefault();
                        elements[_previousIndex].focus();
                        activateOnFocus && elements[_previousIndex].click();
                    }
                    break;
                }
            case "ArrowDown":
                {
                    if (orientation === "vertical") {
                        event.stopPropagation();
                        event.preventDefault();
                        elements[_nextIndex].focus();
                        activateOnFocus && elements[_nextIndex].click();
                    }
                    break;
                }
            case "Home":
                {
                    event.stopPropagation();
                    event.preventDefault();
                    !elements[0].disabled && elements[0].focus();
                    break;
                }
            case "End":
                {
                    event.stopPropagation();
                    event.preventDefault();
                    const last = elements.length - 1;
                    !elements[last].disabled && elements[last].focus();
                    break;
                }
        }
    };
}
;
 //# sourceMappingURL=create-scoped-keydown-handler.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/get-safe-id/get-safe-id.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSafeId": (()=>getSafeId)
});
'use client';
function getSafeId(uid, errorMessage) {
    return (value)=>{
        if (typeof value !== "string" || value.trim().length === 0) {
            throw new Error(errorMessage);
        }
        return `${uid}-${value}`;
    };
}
;
 //# sourceMappingURL=get-safe-id.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getContrastColor": (()=>getContrastColor),
    "getPrimaryContrastColor": (()=>getPrimaryContrastColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/parse-theme-color/parse-theme-color.mjs [app-client] (ecmascript)");
'use client';
;
;
function getContrastColor({ color, theme, autoContrast }) {
    const _autoContrast = typeof autoContrast === "boolean" ? autoContrast : theme.autoContrast;
    if (!_autoContrast) {
        return "var(--mantine-color-white)";
    }
    const parsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$parse$2d$theme$2d$color$2f$parse$2d$theme$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseThemeColor"])({
        color: color || theme.primaryColor,
        theme
    });
    return parsed.isLight ? "var(--mantine-color-black)" : "var(--mantine-color-white)";
}
function getPrimaryContrastColor(theme, colorScheme) {
    return getContrastColor({
        color: theme.colors[theme.primaryColor][(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryShade"])(theme, colorScheme)],
        theme,
        autoContrast: null
    });
}
;
 //# sourceMappingURL=get-contrast-color.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-auto-contrast-value/get-auto-contrast-value.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAutoContrastValue": (()=>getAutoContrastValue)
});
'use client';
function getAutoContrastValue(autoContrast, theme) {
    return typeof autoContrast === "boolean" ? autoContrast : theme.autoContrast;
}
;
 //# sourceMappingURL=get-auto-contrast-value.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isMantineColorScheme": (()=>isMantineColorScheme)
});
'use client';
function isMantineColorScheme(value) {
    return value === "auto" || value === "dark" || value === "light";
}
;
 //# sourceMappingURL=is-mantine-color-scheme.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "localStorageColorSchemeManager": (()=>localStorageColorSchemeManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$scheme$2d$managers$2f$is$2d$mantine$2d$color$2d$scheme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/is-mantine-color-scheme.mjs [app-client] (ecmascript)");
'use client';
;
function localStorageColorSchemeManager({ key = "mantine-color-scheme-value" } = {}) {
    let handleStorageEvent;
    return {
        get: (defaultValue)=>{
            if (typeof window === "undefined") {
                return defaultValue;
            }
            try {
                const storedColorScheme = window.localStorage.getItem(key);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$scheme$2d$managers$2f$is$2d$mantine$2d$color$2d$scheme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMantineColorScheme"])(storedColorScheme) ? storedColorScheme : defaultValue;
            } catch  {
                return defaultValue;
            }
        },
        set: (value)=>{
            try {
                window.localStorage.setItem(key, value);
            } catch (error) {
                console.warn("[@mantine/core] Local storage color scheme manager was unable to save color scheme.", error);
            }
        },
        subscribe: (onUpdate)=>{
            handleStorageEvent = (event)=>{
                if (event.storageArea === window.localStorage && event.key === key) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$scheme$2d$managers$2f$is$2d$mantine$2d$color$2d$scheme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMantineColorScheme"])(event.newValue) && onUpdate(event.newValue);
                }
            };
            window.addEventListener("storage", handleStorageEvent);
        },
        unsubscribe: ()=>{
            window.removeEventListener("storage", handleStorageEvent);
        },
        clear: ()=>{
            window.localStorage.removeItem(key);
        }
    };
}
;
 //# sourceMappingURL=local-storage-manager.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/px.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "px": (()=>px)
});
function getTransformedScaledValue(value) {
    if (typeof value !== "string" || !value.includes("var(--mantine-scale)")) {
        return value;
    }
    return value.match(/^calc\((.*?)\)$/)?.[1].split("*")[0].trim();
}
function px(value) {
    const transformedValue = getTransformedScaledValue(value);
    if (typeof transformedValue === "number") {
        return transformedValue;
    }
    if (typeof transformedValue === "string") {
        if (transformedValue.includes("calc") || transformedValue.includes("var")) {
            return transformedValue;
        }
        if (transformedValue.includes("px")) {
            return Number(transformedValue.replace("px", ""));
        }
        if (transformedValue.includes("rem")) {
            return Number(transformedValue.replace("rem", "")) * 16;
        }
        if (transformedValue.includes("em")) {
            return Number(transformedValue.replace("em", "")) * 16;
        }
        return Number(transformedValue);
    }
    return NaN;
}
;
 //# sourceMappingURL=px.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineClasses/MantineClasses.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MantineClasses": (()=>MantineClasses)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$px$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/px.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function MantineClasses() {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineStyleNonce"])();
    const classes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(theme.breakpoints).reduce((acc, breakpoint)=>{
        const isPxBreakpoint = theme.breakpoints[breakpoint].includes("px");
        const pxValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$px$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["px"])(theme.breakpoints[breakpoint]);
        const maxWidthBreakpoint = isPxBreakpoint ? `${pxValue - 0.1}px` : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["em"])(pxValue - 0.1);
        const minWidthBreakpoint = isPxBreakpoint ? `${pxValue}px` : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["em"])(pxValue);
        return `${acc}@media (max-width: ${maxWidthBreakpoint}) {.mantine-visible-from-${breakpoint} {display: none !important;}}@media (min-width: ${minWidthBreakpoint}) {.mantine-hidden-from-${breakpoint} {display: none !important;}}`;
    }, "");
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("style", {
        "data-mantine-styles": "classes",
        nonce: nonce?.(),
        dangerouslySetInnerHTML: {
            __html: classes
        }
    });
}
;
 //# sourceMappingURL=MantineClasses.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/css-variables-object-to-string.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cssVariablesObjectToString": (()=>cssVariablesObjectToString)
});
'use client';
function cssVariablesObjectToString(variables) {
    return Object.entries(variables).map(([name, value])=>`${name}: ${value};`).join("");
}
;
 //# sourceMappingURL=css-variables-object-to-string.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/wrap-with-selector.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "wrapWithSelector": (()=>wrapWithSelector)
});
'use client';
function wrapWithSelector(selectors, code) {
    const _selectors = Array.isArray(selectors) ? selectors : [
        selectors
    ];
    return _selectors.reduce((acc, selector)=>`${selector}{${acc}}`, code);
}
;
 //# sourceMappingURL=wrap-with-selector.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertCssVariables": (()=>convertCssVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$css$2d$variables$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/css-variables-object-to-string.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/wrap-with-selector.mjs [app-client] (ecmascript)");
'use client';
;
;
function convertCssVariables(input, selector) {
    const sharedVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$css$2d$variables$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssVariablesObjectToString"])(input.variables);
    const shared = sharedVariables ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapWithSelector"])(selector, sharedVariables) : "";
    const dark = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$css$2d$variables$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssVariablesObjectToString"])(input.dark);
    const light = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$css$2d$variables$2d$object$2d$to$2d$string$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssVariablesObjectToString"])(input.light);
    const darkForced = dark ? selector === ":host" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapWithSelector"])(`${selector}([data-mantine-color-scheme="dark"])`, dark) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapWithSelector"])(`${selector}[data-mantine-color-scheme="dark"]`, dark) : "";
    const lightForced = light ? selector === ":host" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapWithSelector"])(`${selector}([data-mantine-color-scheme="light"])`, light) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$wrap$2d$with$2d$selector$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapWithSelector"])(`${selector}[data-mantine-color-scheme="light"]`, light) : "";
    return `${shared}${darkForced}${lightForced}`;
}
;
 //# sourceMappingURL=convert-css-variables.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCSSColorVariables": (()=>getCSSColorVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/rgba/rgba.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function getCSSColorVariables({ theme, color, colorScheme, name = color, withColorValues = true }) {
    if (!theme.colors[color]) {
        return {};
    }
    if (colorScheme === "light") {
        const primaryShade2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryShade"])(theme, "light");
        const dynamicVariables2 = {
            [`--mantine-color-${name}-text`]: `var(--mantine-color-${name}-filled)`,
            [`--mantine-color-${name}-filled`]: `var(--mantine-color-${name}-${primaryShade2})`,
            [`--mantine-color-${name}-filled-hover`]: `var(--mantine-color-${name}-${primaryShade2 === 9 ? 8 : primaryShade2 + 1})`,
            [`--mantine-color-${name}-light`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][primaryShade2], 0.1),
            [`--mantine-color-${name}-light-hover`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][primaryShade2], 0.12),
            [`--mantine-color-${name}-light-color`]: `var(--mantine-color-${name}-${primaryShade2})`,
            [`--mantine-color-${name}-outline`]: `var(--mantine-color-${name}-${primaryShade2})`,
            [`--mantine-color-${name}-outline-hover`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][primaryShade2], 0.05)
        };
        if (!withColorValues) {
            return dynamicVariables2;
        }
        return {
            [`--mantine-color-${name}-0`]: theme.colors[color][0],
            [`--mantine-color-${name}-1`]: theme.colors[color][1],
            [`--mantine-color-${name}-2`]: theme.colors[color][2],
            [`--mantine-color-${name}-3`]: theme.colors[color][3],
            [`--mantine-color-${name}-4`]: theme.colors[color][4],
            [`--mantine-color-${name}-5`]: theme.colors[color][5],
            [`--mantine-color-${name}-6`]: theme.colors[color][6],
            [`--mantine-color-${name}-7`]: theme.colors[color][7],
            [`--mantine-color-${name}-8`]: theme.colors[color][8],
            [`--mantine-color-${name}-9`]: theme.colors[color][9],
            ...dynamicVariables2
        };
    }
    const primaryShade = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryShade"])(theme, "dark");
    const dynamicVariables = {
        [`--mantine-color-${name}-text`]: `var(--mantine-color-${name}-4)`,
        [`--mantine-color-${name}-filled`]: `var(--mantine-color-${name}-${primaryShade})`,
        [`--mantine-color-${name}-filled-hover`]: `var(--mantine-color-${name}-${primaryShade === 9 ? 8 : primaryShade + 1})`,
        [`--mantine-color-${name}-light`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][Math.max(0, primaryShade - 2)], 0.15),
        [`--mantine-color-${name}-light-hover`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][Math.max(0, primaryShade - 2)], 0.2),
        [`--mantine-color-${name}-light-color`]: `var(--mantine-color-${name}-${Math.max(primaryShade - 5, 0)})`,
        [`--mantine-color-${name}-outline`]: `var(--mantine-color-${name}-${Math.max(primaryShade - 4, 0)})`,
        [`--mantine-color-${name}-outline-hover`]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$rgba$2f$rgba$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])(theme.colors[color][Math.max(primaryShade - 4, 0)], 0.05)
    };
    if (!withColorValues) {
        return dynamicVariables;
    }
    return {
        [`--mantine-color-${name}-0`]: theme.colors[color][0],
        [`--mantine-color-${name}-1`]: theme.colors[color][1],
        [`--mantine-color-${name}-2`]: theme.colors[color][2],
        [`--mantine-color-${name}-3`]: theme.colors[color][3],
        [`--mantine-color-${name}-4`]: theme.colors[color][4],
        [`--mantine-color-${name}-5`]: theme.colors[color][5],
        [`--mantine-color-${name}-6`]: theme.colors[color][6],
        [`--mantine-color-${name}-7`]: theme.colors[color][7],
        [`--mantine-color-${name}-8`]: theme.colors[color][8],
        [`--mantine-color-${name}-9`]: theme.colors[color][9],
        ...dynamicVariables
    };
}
;
 //# sourceMappingURL=get-css-color-variables.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/colors-tuple/colors-tuple.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "colorsTuple": (()=>colorsTuple)
});
function colorsTuple(input) {
    if (Array.isArray(input)) {
        return input;
    }
    return Array(10).fill(input);
}
;
 //# sourceMappingURL=colors-tuple.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/virtual-color/virtual-color.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isVirtualColor": (()=>isVirtualColor),
    "virtualColor": (()=>virtualColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$colors$2d$tuple$2f$colors$2d$tuple$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/colors-tuple/colors-tuple.mjs [app-client] (ecmascript)");
;
;
;
;
function virtualColor(input) {
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$colors$2d$tuple$2f$colors$2d$tuple$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colorsTuple"])(Array.from({
        length: 10
    }).map((_, i)=>`var(--mantine-color-${input.name}-${i})`));
    Object.defineProperty(result, "mantine-virtual-color", {
        enumerable: false,
        writable: false,
        configurable: false,
        value: true
    });
    Object.defineProperty(result, "dark", {
        enumerable: false,
        writable: false,
        configurable: false,
        value: input.dark
    });
    Object.defineProperty(result, "light", {
        enumerable: false,
        writable: false,
        configurable: false,
        value: input.light
    });
    Object.defineProperty(result, "name", {
        enumerable: false,
        writable: false,
        configurable: false,
        value: input.name
    });
    return result;
}
function isVirtualColor(value) {
    return !!value && typeof value === "object" && "mantine-virtual-color" in value;
}
;
 //# sourceMappingURL=virtual-color.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultCssVariablesResolver": (()=>defaultCssVariablesResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-primary-shade/get-primary-shade.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$contrast$2d$color$2f$get$2d$contrast$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-functions/get-contrast-color/get-contrast-color.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$css$2d$color$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-css-color-variables.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$virtual$2d$color$2f$virtual$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/virtual-color/virtual-color.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function assignSizeVariables(variables, sizes, name) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(sizes).forEach((size)=>Object.assign(variables, {
            [`--mantine-${name}-${size}`]: sizes[size]
        }));
}
const defaultCssVariablesResolver = (theme)=>{
    const lightPrimaryShade = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$primary$2d$shade$2f$get$2d$primary$2d$shade$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryShade"])(theme, "light");
    const defaultRadius = theme.defaultRadius in theme.radius ? theme.radius[theme.defaultRadius] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$units$2d$converters$2f$rem$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rem"])(theme.defaultRadius);
    const result = {
        variables: {
            "--mantine-scale": theme.scale.toString(),
            "--mantine-cursor-type": theme.cursorType,
            "--mantine-color-scheme": "light dark",
            "--mantine-webkit-font-smoothing": theme.fontSmoothing ? "antialiased" : "unset",
            "--mantine-moz-font-smoothing": theme.fontSmoothing ? "grayscale" : "unset",
            "--mantine-color-white": theme.white,
            "--mantine-color-black": theme.black,
            "--mantine-line-height": theme.lineHeights.md,
            "--mantine-font-family": theme.fontFamily,
            "--mantine-font-family-monospace": theme.fontFamilyMonospace,
            "--mantine-font-family-headings": theme.headings.fontFamily,
            "--mantine-heading-font-weight": theme.headings.fontWeight,
            "--mantine-heading-text-wrap": theme.headings.textWrap,
            "--mantine-radius-default": defaultRadius,
            // Primary colors
            "--mantine-primary-color-filled": `var(--mantine-color-${theme.primaryColor}-filled)`,
            "--mantine-primary-color-filled-hover": `var(--mantine-color-${theme.primaryColor}-filled-hover)`,
            "--mantine-primary-color-light": `var(--mantine-color-${theme.primaryColor}-light)`,
            "--mantine-primary-color-light-hover": `var(--mantine-color-${theme.primaryColor}-light-hover)`,
            "--mantine-primary-color-light-color": `var(--mantine-color-${theme.primaryColor}-light-color)`
        },
        light: {
            "--mantine-primary-color-contrast": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$contrast$2d$color$2f$get$2d$contrast$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryContrastColor"])(theme, "light"),
            "--mantine-color-bright": "var(--mantine-color-black)",
            "--mantine-color-text": theme.black,
            "--mantine-color-body": theme.white,
            "--mantine-color-error": "var(--mantine-color-red-6)",
            "--mantine-color-placeholder": "var(--mantine-color-gray-5)",
            "--mantine-color-anchor": `var(--mantine-color-${theme.primaryColor}-${lightPrimaryShade})`,
            "--mantine-color-default": "var(--mantine-color-white)",
            "--mantine-color-default-hover": "var(--mantine-color-gray-0)",
            "--mantine-color-default-color": "var(--mantine-color-black)",
            "--mantine-color-default-border": "var(--mantine-color-gray-4)",
            "--mantine-color-dimmed": "var(--mantine-color-gray-6)"
        },
        dark: {
            "--mantine-primary-color-contrast": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$functions$2f$get$2d$contrast$2d$color$2f$get$2d$contrast$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrimaryContrastColor"])(theme, "dark"),
            "--mantine-color-bright": "var(--mantine-color-white)",
            "--mantine-color-text": "var(--mantine-color-dark-0)",
            "--mantine-color-body": "var(--mantine-color-dark-7)",
            "--mantine-color-error": "var(--mantine-color-red-8)",
            "--mantine-color-placeholder": "var(--mantine-color-dark-3)",
            "--mantine-color-anchor": `var(--mantine-color-${theme.primaryColor}-4)`,
            "--mantine-color-default": "var(--mantine-color-dark-6)",
            "--mantine-color-default-hover": "var(--mantine-color-dark-5)",
            "--mantine-color-default-color": "var(--mantine-color-white)",
            "--mantine-color-default-border": "var(--mantine-color-dark-4)",
            "--mantine-color-dimmed": "var(--mantine-color-dark-2)"
        }
    };
    assignSizeVariables(result.variables, theme.breakpoints, "breakpoint");
    assignSizeVariables(result.variables, theme.spacing, "spacing");
    assignSizeVariables(result.variables, theme.fontSizes, "font-size");
    assignSizeVariables(result.variables, theme.lineHeights, "line-height");
    assignSizeVariables(result.variables, theme.shadows, "shadow");
    assignSizeVariables(result.variables, theme.radius, "radius");
    theme.colors[theme.primaryColor].forEach((_, index)=>{
        result.variables[`--mantine-primary-color-${index}`] = `var(--mantine-color-${theme.primaryColor}-${index})`;
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(theme.colors).forEach((color)=>{
        const value = theme.colors[color];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$virtual$2d$color$2f$virtual$2d$color$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isVirtualColor"])(value)) {
            Object.assign(result.light, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$css$2d$color$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCSSColorVariables"])({
                theme,
                name: value.name,
                color: value.light,
                colorScheme: "light",
                withColorValues: true
            }));
            Object.assign(result.dark, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$css$2d$color$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCSSColorVariables"])({
                theme,
                name: value.name,
                color: value.dark,
                colorScheme: "dark",
                withColorValues: true
            }));
            return;
        }
        value.forEach((shade, index)=>{
            result.variables[`--mantine-color-${color}-${index}`] = shade;
        });
        Object.assign(result.light, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$css$2d$color$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCSSColorVariables"])({
            theme,
            color,
            colorScheme: "light",
            withColorValues: false
        }));
        Object.assign(result.dark, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$css$2d$color$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCSSColorVariables"])({
            theme,
            color,
            colorScheme: "dark",
            withColorValues: false
        }));
    });
    const headings = theme.headings.sizes;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(headings).forEach((heading)=>{
        result.variables[`--mantine-${heading}-font-size`] = headings[heading].fontSize;
        result.variables[`--mantine-${heading}-line-height`] = headings[heading].lineHeight;
        result.variables[`--mantine-${heading}-font-weight`] = headings[heading].fontWeight || theme.headings.fontWeight;
    });
    return result;
};
;
 //# sourceMappingURL=default-css-variables-resolver.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-merged-variables.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMergedVariables": (()=>getMergedVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$deep$2d$merge$2f$deep$2d$merge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/deep-merge/deep-merge.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$default$2d$css$2d$variables$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
function getMergedVariables({ theme, generator }) {
    const defaultResolver = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$default$2d$css$2d$variables$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultCssVariablesResolver"])(theme);
    const providerGenerator = generator?.(theme);
    return providerGenerator ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$deep$2d$merge$2f$deep$2d$merge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deepMerge"])(defaultResolver, providerGenerator) : defaultResolver;
}
;
 //# sourceMappingURL=get-merged-variables.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/remove-default-variables.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "removeDefaultVariables": (()=>removeDefaultVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/utils/keys/keys.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/default-theme.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$default$2d$css$2d$variables$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/default-css-variables-resolver.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const defaultCssVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$default$2d$css$2d$variables$2d$resolver$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultCssVariablesResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$default$2d$theme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_THEME"]);
function removeDefaultVariables(input) {
    const cleaned = {
        variables: {},
        light: {},
        dark: {}
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(input.variables).forEach((key)=>{
        if (defaultCssVariables.variables[key] !== input.variables[key]) {
            cleaned.variables[key] = input.variables[key];
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(input.light).forEach((key)=>{
        if (defaultCssVariables.light[key] !== input.light[key]) {
            cleaned.light[key] = input.light[key];
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$utils$2f$keys$2f$keys$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["keys"])(input.dark).forEach((key)=>{
        if (defaultCssVariables.dark[key] !== input.dark[key]) {
            cleaned.dark[key] = input.dark[key];
        }
    });
    return cleaned;
}
;
 //# sourceMappingURL=remove-default-variables.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MantineCssVariables": (()=>MantineCssVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$convert$2d$css$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/convert-css-variables/convert-css-variables.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$merged$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/get-merged-variables.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$remove$2d$default$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/remove-default-variables.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
function getColorSchemeCssVariables(selector) {
    return `
  ${selector}[data-mantine-color-scheme="dark"] { --mantine-color-scheme: dark; }
  ${selector}[data-mantine-color-scheme="light"] { --mantine-color-scheme: light; }
`;
}
function MantineCssVariables({ cssVariablesSelector, deduplicateCssVariables }) {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineTheme"])();
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineStyleNonce"])();
    const generator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMantineCssVariablesResolver"])();
    const mergedVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$get$2d$merged$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMergedVariables"])({
        theme,
        generator
    });
    const shouldCleanVariables = cssVariablesSelector === ":root" && deduplicateCssVariables;
    const cleanedVariables = shouldCleanVariables ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$remove$2d$default$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeDefaultVariables"])(mergedVariables) : mergedVariables;
    const css = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$convert$2d$css$2d$variables$2f$convert$2d$css$2d$variables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertCssVariables"])(cleanedVariables, cssVariablesSelector);
    if (css) {
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("style", {
            "data-mantine-styles": true,
            nonce: nonce?.(),
            dangerouslySetInnerHTML: {
                __html: `${css}${shouldCleanVariables ? "" : getColorSchemeCssVariables(cssVariablesSelector)}`
            }
        });
    }
    return null;
}
MantineCssVariables.displayName = "@mantine/CssVariables";
;
 //# sourceMappingURL=MantineCssVariables.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/suppress-nextjs-warning.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "suppressNextjsWarning": (()=>suppressNextjsWarning)
});
'use client';
function suppressNextjsWarning() {
    const originalError = console.error;
    console.error = (...args)=>{
        if (args.length > 1 && typeof args[0] === "string" && args[0].toLowerCase().includes("extra attributes from the server") && typeof args[1] === "string" && args[1].toLowerCase().includes("data-mantine-color-scheme")) ;
        else {
            originalError(...args);
        }
    };
}
;
 //# sourceMappingURL=suppress-nextjs-warning.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useProviderColorScheme": (()=>useProviderColorScheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs [app-client] (ecmascript)");
'use client';
;
;
function setColorSchemeAttribute(colorScheme, getRootElement) {
    const hasDarkColorScheme = typeof window !== "undefined" && "matchMedia" in window && window.matchMedia("(prefers-color-scheme: dark)")?.matches;
    const computedColorScheme = colorScheme !== "auto" ? colorScheme : hasDarkColorScheme ? "dark" : "light";
    getRootElement()?.setAttribute("data-mantine-color-scheme", computedColorScheme);
}
function useProviderColorScheme({ manager, defaultColorScheme, getRootElement, forceColorScheme }) {
    const media = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useProviderColorScheme.useState": ()=>manager.get(defaultColorScheme)
    }["useProviderColorScheme.useState"]);
    const colorSchemeValue = forceColorScheme || value;
    const setColorScheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProviderColorScheme.useCallback[setColorScheme]": (colorScheme)=>{
            if (!forceColorScheme) {
                setColorSchemeAttribute(colorScheme, getRootElement);
                setValue(colorScheme);
                manager.set(colorScheme);
            }
        }
    }["useProviderColorScheme.useCallback[setColorScheme]"], [
        manager.set,
        colorSchemeValue,
        forceColorScheme
    ]);
    const clearColorScheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProviderColorScheme.useCallback[clearColorScheme]": ()=>{
            setValue(defaultColorScheme);
            setColorSchemeAttribute(defaultColorScheme, getRootElement);
            manager.clear();
        }
    }["useProviderColorScheme.useCallback[clearColorScheme]"], [
        manager.clear,
        defaultColorScheme
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProviderColorScheme.useEffect": ()=>{
            manager.subscribe(setColorScheme);
            return manager.unsubscribe;
        }
    }["useProviderColorScheme.useEffect"], [
        manager.subscribe,
        manager.unsubscribe
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsomorphicEffect"])({
        "useProviderColorScheme.useIsomorphicEffect": ()=>{
            setColorSchemeAttribute(manager.get(defaultColorScheme), getRootElement);
        }
    }["useProviderColorScheme.useIsomorphicEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProviderColorScheme.useEffect": ()=>{
            if (forceColorScheme) {
                setColorSchemeAttribute(forceColorScheme, getRootElement);
                return ({
                    "useProviderColorScheme.useEffect": ()=>{}
                })["useProviderColorScheme.useEffect"];
            }
            if (forceColorScheme === void 0) {
                setColorSchemeAttribute(value, getRootElement);
            }
            if (typeof window !== "undefined" && "matchMedia" in window) {
                media.current = window.matchMedia("(prefers-color-scheme: dark)");
            }
            const listener = {
                "useProviderColorScheme.useEffect.listener": (event)=>{
                    if (value === "auto") {
                        setColorSchemeAttribute(event.matches ? "dark" : "light", getRootElement);
                    }
                }
            }["useProviderColorScheme.useEffect.listener"];
            media.current?.addEventListener("change", listener);
            return ({
                "useProviderColorScheme.useEffect": ()=>media.current?.removeEventListener("change", listener)
            })["useProviderColorScheme.useEffect"];
        }
    }["useProviderColorScheme.useEffect"], [
        value,
        forceColorScheme
    ]);
    return {
        colorScheme: colorSchemeValue,
        setColorScheme,
        clearColorScheme
    };
}
;
 //# sourceMappingURL=use-provider-color-scheme.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/use-respect-reduce-motion/use-respect-reduce-motion.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useRespectReduceMotion": (()=>useRespectReduceMotion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-isomorphic-effect/use-isomorphic-effect.mjs [app-client] (ecmascript)");
'use client';
;
function useRespectReduceMotion({ respectReducedMotion, getRootElement }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$isomorphic$2d$effect$2f$use$2d$isomorphic$2d$effect$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsomorphicEffect"])({
        "useRespectReduceMotion.useIsomorphicEffect": ()=>{
            if (respectReducedMotion) {
                getRootElement()?.setAttribute("data-respect-reduced-motion", "true");
            }
        }
    }["useRespectReduceMotion.useIsomorphicEffect"], [
        respectReducedMotion
    ]);
}
;
 //# sourceMappingURL=use-respect-reduce-motion.mjs.map
}}),
"[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HeadlessMantineProvider": (()=>HeadlessMantineProvider),
    "MantineProvider": (()=>MantineProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$scheme$2d$managers$2f$local$2d$storage$2d$manager$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/color-scheme-managers/local-storage-manager.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/Mantine.context.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineClasses$2f$MantineClasses$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineClasses/MantineClasses.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$MantineCssVariables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineCssVariables/MantineCssVariables.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineThemeProvider/MantineThemeProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$suppress$2d$nextjs$2d$warning$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/suppress-nextjs-warning.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$use$2d$mantine$2d$color$2d$scheme$2f$use$2d$provider$2d$color$2d$scheme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-provider-color-scheme.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$use$2d$respect$2d$reduce$2d$motion$2f$use$2d$respect$2d$reduce$2d$motion$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/use-respect-reduce-motion/use-respect-reduce-motion.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$suppress$2d$nextjs$2d$warning$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["suppressNextjsWarning"])();
function MantineProvider({ theme, children, getStyleNonce, withStaticClasses = true, withGlobalClasses = true, deduplicateCssVariables = true, withCssVariables = true, cssVariablesSelector = ":root", classNamesPrefix = "mantine", colorSchemeManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$color$2d$scheme$2d$managers$2f$local$2d$storage$2d$manager$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorageColorSchemeManager"])(), defaultColorScheme = "light", getRootElement = ()=>document.documentElement, cssVariablesResolver, forceColorScheme, stylesTransform, env }) {
    const { colorScheme, setColorScheme, clearColorScheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$use$2d$mantine$2d$color$2d$scheme$2f$use$2d$provider$2d$color$2d$scheme$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProviderColorScheme"])({
        defaultColorScheme,
        forceColorScheme,
        manager: colorSchemeManager,
        getRootElement
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$use$2d$respect$2d$reduce$2d$motion$2f$use$2d$respect$2d$reduce$2d$motion$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRespectReduceMotion"])({
        respectReducedMotion: theme?.respectReducedMotion || false,
        getRootElement
    });
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineContext"].Provider, {
        value: {
            colorScheme,
            setColorScheme,
            clearColorScheme,
            getRootElement,
            classNamesPrefix,
            getStyleNonce,
            cssVariablesResolver,
            cssVariablesSelector,
            withStaticClasses,
            stylesTransform,
            env
        },
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineThemeProvider"], {
            theme,
            children: [
                withCssVariables && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineCssVariables$2f$MantineCssVariables$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineCssVariables"], {
                    cssVariablesSelector,
                    deduplicateCssVariables
                }),
                withGlobalClasses && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineClasses$2f$MantineClasses$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineClasses"], {}),
                children
            ]
        })
    });
}
MantineProvider.displayName = "@mantine/core/MantineProvider";
function HeadlessMantineProvider({ children, theme }) {
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$Mantine$2e$context$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineContext"].Provider, {
        value: {
            colorScheme: "auto",
            setColorScheme: ()=>{},
            clearColorScheme: ()=>{},
            getRootElement: ()=>document.documentElement,
            classNamesPrefix: "mantine",
            cssVariablesSelector: ":root",
            withStaticClasses: false,
            headless: true
        },
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineThemeProvider$2f$MantineThemeProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineThemeProvider"], {
            theme,
            children
        })
    });
}
HeadlessMantineProvider.displayName = "@mantine/core/HeadlessMantineProvider";
;
 //# sourceMappingURL=MantineProvider.mjs.map
}}),
}]);

//# sourceMappingURL=72ed8_%40mantine_core_esm_core_cad5b088._.js.map