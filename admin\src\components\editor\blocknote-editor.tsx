'use client';

import React, { useEffect, useMemo } from 'react';
import { BlockNoteEditor, PartialBlock } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import { useCreateBlockNote } from '@blocknote/react';
import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

interface BlockNoteEditorProps {
  /**
   * Initial content for the editor
   */
  initialContent?: PartialBlock[];
  
  /**
   * Called when the editor content changes
   */
  onChange?: (blocks: PartialBlock[]) => void;
  
  /**
   * Whether the editor is editable
   */
  editable?: boolean;
  
  /**
   * Placeholder text to display when editor is empty
   */
  placeholder?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function BlockNoteEditorComponent({
  initialContent,
  onChange,
  editable = true,
  placeholder = "Эчтәлек языгыз...",
  className = "",
}: BlockNoteEditorProps) {
  // Create the editor instance
  const editor = useCreateBlockNote({
    initialContent,
  });

  // Handle content changes
  const handleChange = () => {
    if (onChange) {
      onChange(editor.document);
    }
  };

  return (
    <div className={`blocknote-editor ${className}`}>
      <BlockNoteView
        editor={editor}
        onChange={handleChange}
        editable={editable}
      />
    </div>
  );
}

// Export a simpler interface for form usage
export interface EditorFieldProps {
  /**
   * Current value of the editor (array of blocks)
   */
  value?: PartialBlock[];
  
  /**
   * Called when the editor value changes
   */
  onChange?: (value: PartialBlock[]) => void;
  
  /**
   * Placeholder text to display when editor is empty
   */
  placeholder?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function BlockNoteEditorField({
  value,
  onChange,
  placeholder = "Эчтәлек языгыз...",
  className = "",
}: EditorFieldProps) {
  // Ensure we have a default value
  const initialContent = useMemo(() => {
    if (value && value.length > 0) {
      return value;
    }
    return [
      {
        type: 'paragraph',
        content: [],
      },
    ] as PartialBlock[];
  }, [value]);

  return (
    <BlockNoteEditorComponent
      initialContent={initialContent}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
    />
  );
}
