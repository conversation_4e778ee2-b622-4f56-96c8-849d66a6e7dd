'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { BlockNoteEditor, PartialBlock } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import { useCreateBlockNote } from '@blocknote/react';
import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';
import { prepareContentForEditor } from '@/lib/utils/content-format';

interface BlockNoteEditorProps {
  /**
   * Initial content for the editor
   */
  initialContent?: PartialBlock[];
  
  /**
   * Called when the editor content changes
   */
  onChange?: (blocks: PartialBlock[]) => void;
  
  /**
   * Whether the editor is editable
   */
  editable?: boolean;
  
  /**
   * Placeholder text to display when editor is empty
   */
  placeholder?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function BlockNoteEditorComponent({
  initialContent,
  onChange,
  editable = true,
  placeholder = "Эчтәлек языгыз...",
  className = "",
}: BlockNoteEditorProps) {
  // Create the editor instance
  const editor = useCreateBlockNote({
    initialContent,
  });

  // Handle content changes
  const handleChange = () => {
    if (onChange) {
      onChange(editor.document);
    }
  };

  return (
    <div className={`blocknote-editor ${className}`} style={{ position: 'relative', zIndex: 1 }}>
      <BlockNoteView
        editor={editor}
        onChange={handleChange}
        editable={editable}
      />
    </div>
  );
}

// Export a simpler interface for form usage
export interface EditorFieldProps {
  /**
   * Current value of the editor (array of blocks)
   */
  value?: PartialBlock[];
  
  /**
   * Called when the editor value changes
   */
  onChange?: (value: PartialBlock[]) => void;
  
  /**
   * Placeholder text to display when editor is empty
   */
  placeholder?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function BlockNoteEditorField({
  value,
  onChange,
  placeholder = "Эчтәлек языгыз...",
  className = "",
}: EditorFieldProps) {
  // Ensure we have a default value
  const initialContent = useMemo(() => {
    if (value && value.length > 0) {
      return value;
    }
    return [
      {
        type: 'paragraph',
        content: [],
      },
    ] as PartialBlock[];
  }, [value]);

  return (
    <BlockNoteEditorComponent
      initialContent={initialContent}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
    />
  );
}

export function SafeBlockNoteEditorField({ value, onChange, contentFormat, ...props }: {
  value: string | PartialBlock[] | undefined,
  onChange: (blocks: PartialBlock[]) => void,
  contentFormat?: string,
  [key: string]: any
}) {
  const [blocks, setBlocks] = useState<PartialBlock[] | undefined>(undefined);

  useEffect(() => {
    let cancelled = false;
    async function loadBlocks() {
      if (typeof value === 'string') {
        const tempEditor = BlockNoteEditor.create();
        const parsed = await prepareContentForEditor(value, tempEditor, contentFormat);
        if (!cancelled) setBlocks(parsed);
      } else if (Array.isArray(value)) {
        setBlocks(value);
      } else {
        setBlocks([
          {
            type: 'paragraph',
            content: [],
          },
        ]);
      }
    }
    loadBlocks();
    return () => { cancelled = true; };
  }, [value, contentFormat]);

  if (!blocks) return <div>Loading...</div>;

  return <BlockNoteEditorField value={blocks} onChange={onChange} {...props} />;
}
