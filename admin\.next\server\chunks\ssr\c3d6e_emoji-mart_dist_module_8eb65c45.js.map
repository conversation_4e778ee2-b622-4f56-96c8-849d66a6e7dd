{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "module.js.map", "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Picker/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Picker/Picker.tsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/%40swc/helpers/src/_define_property.mjs", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/jsx-runtime/dist/jsxRuntime.module.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/jsx-runtime/src/index.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/dist/preact.module.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/constants.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/util.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/options.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/create-element.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/component.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/create-context.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/diff/children.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/diff/props.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/diff/index.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/render.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/clone-element.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/src/diff/catch-error.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/utils.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/helpers/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/helpers/store.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/helpers/native-support.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/helpers/frequently-used.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/helpers/search-index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/config.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart-data/i18n/en.json", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Picker/PickerProps.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/icons.tsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Emoji/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Emoji/Emoji.tsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Emoji/EmojiElement.jsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/HTMLElement.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/ShadowElement.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Emoji/EmojiProps.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Navigation/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Navigation/Navigation.tsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/dist/compat.module.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/util.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/PureComponent.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/memo.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/forwardRef.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/Children.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/suspense.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/suspense-list.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/portals.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/render.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/compat/src/index.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/hooks/dist/hooks.module.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/preact/hooks/src/index.js", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/HOCs/index.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/HOCs/PureInlineComponent.ts", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/packages/emoji-mart/src/components/Picker/PickerElement.tsx", "file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/emoji-mart%405.6.0/node_modules/emoji-mart/dist/node_modules/%40parcel/runtime-js/lib/bundles/runtime-1b9572f9f2947a02.js"], "sourcesContent": ["export { PickerElement as Picker } from './components/Picker'\nexport { EmojiElement as Emoji } from './components/Emoji'\n\nexport { FrequentlyUsed, SafeFlags, SearchIndex, Store } from './helpers'\n\nexport { init, Data, I18n } from './config'\n\nexport { getEmojiDataFromNative } from './utils'\n", "// @ts-nocheck\nexport { default as Picker } from './Picker'\nexport { default as PickerElement } from './PickerElement'\nexport { default as PickerStyles } from 'bundle-text:./PickerStyles.scss'\n", "// @ts-nocheck\nimport { Component, createRef } from 'preact'\n\nimport { deepEqual, sleep, getEmojiData } from '../../utils'\nimport { Data, I18n, init } from '../../config'\nimport { SearchIndex, Store, FrequentlyUsed } from '../../helpers'\nimport Icons from '../../icons'\n\nimport { Emoji } from '../Emoji'\nimport { Navigation } from '../Navigation'\nimport { PureInlineComponent } from '../HOCs'\n\nconst Performance = {\n  rowsPerRender: 10,\n}\n\nexport default class Picker extends Component {\n  constructor(props) {\n    super()\n\n    this.observers = []\n\n    this.state = {\n      pos: [-1, -1],\n      perLine: this.initDynamicPerLine(props),\n      visibleRows: { 0: true },\n      ...this.getInitialState(props),\n    }\n  }\n\n  getInitialState(props = this.props) {\n    return {\n      skin: Store.get('skin') || props.skin,\n      theme: this.initTheme(props.theme),\n    }\n  }\n\n  componentWillMount() {\n    this.dir = I18n.rtl ? 'rtl' : 'ltr'\n    this.refs = {\n      menu: createRef(),\n      navigation: createRef(),\n      scroll: createRef(),\n      search: createRef(),\n      searchInput: createRef(),\n      skinToneButton: createRef(),\n      skinToneRadio: createRef(),\n    }\n\n    this.initGrid()\n\n    if (\n      this.props.stickySearch == false &&\n      this.props.searchPosition == 'sticky'\n    ) {\n      console.warn(\n        '[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.',\n      )\n\n      this.props.searchPosition = 'static'\n    }\n  }\n\n  componentDidMount() {\n    this.register()\n\n    this.shadowRoot = this.base.parentNode\n\n    if (this.props.autoFocus) {\n      const { searchInput } = this.refs\n      if (searchInput.current) {\n        searchInput.current.focus()\n      }\n    }\n  }\n\n  componentWillReceiveProps(nextProps) {\n    this.nextState || (this.nextState = {})\n\n    for (const k in nextProps) {\n      this.nextState[k] = nextProps[k]\n    }\n\n    clearTimeout(this.nextStateTimer)\n    this.nextStateTimer = setTimeout(() => {\n      let requiresGridReset = false\n\n      for (const k in this.nextState) {\n        this.props[k] = this.nextState[k]\n\n        if (k === 'custom' || k === 'categories') {\n          requiresGridReset = true\n        }\n      }\n\n      delete this.nextState\n      const nextState = this.getInitialState()\n\n      if (requiresGridReset) {\n        return this.reset(nextState)\n      }\n\n      this.setState(nextState)\n    })\n  }\n\n  componentWillUnmount() {\n    this.unregister()\n  }\n\n  async reset(nextState = {}) {\n    await init(this.props)\n\n    this.initGrid()\n    this.unobserve()\n\n    this.setState(nextState, () => {\n      this.observeCategories()\n      this.observeRows()\n    })\n  }\n\n  register() {\n    document.addEventListener('click', this.handleClickOutside)\n    this.observe()\n  }\n\n  unregister() {\n    document.removeEventListener('click', this.handleClickOutside)\n    this.darkMedia?.removeEventListener('change', this.darkMediaCallback)\n    this.unobserve()\n  }\n\n  observe() {\n    this.observeCategories()\n    this.observeRows()\n  }\n\n  unobserve({ except = [] } = {}) {\n    if (!Array.isArray(except)) {\n      except = [except]\n    }\n\n    for (const observer of this.observers) {\n      if (except.includes(observer)) continue\n      observer.disconnect()\n    }\n\n    this.observers = [].concat(except)\n  }\n\n  initGrid() {\n    const { categories } = Data\n\n    this.refs.categories = new Map()\n\n    const navKey = Data.categories.map((category) => category.id).join(',')\n    if (this.navKey && this.navKey != navKey) {\n      this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0)\n    }\n    this.navKey = navKey\n\n    this.grid = []\n    this.grid.setsize = 0\n\n    const addRow = (rows, category) => {\n      const row = []\n      row.__categoryId = category.id\n      row.__index = rows.length\n      this.grid.push(row)\n\n      const rowIndex = this.grid.length - 1\n      const rowRef = rowIndex % Performance.rowsPerRender ? {} : createRef()\n      rowRef.index = rowIndex\n      rowRef.posinset = this.grid.setsize + 1\n      rows.push(rowRef)\n\n      return row\n    }\n\n    for (let category of categories) {\n      const rows = []\n      let row = addRow(rows, category)\n\n      for (let emoji of category.emojis) {\n        if (row.length == this.getPerLine()) {\n          row = addRow(rows, category)\n        }\n\n        this.grid.setsize += 1\n        row.push(emoji)\n      }\n\n      this.refs.categories.set(category.id, { root: createRef(), rows })\n    }\n  }\n\n  darkMediaCallback = () => {\n    if (this.props.theme != 'auto') return\n    this.setState({ theme: this.darkMedia.matches ? 'dark' : 'light' })\n  }\n\n  initTheme(theme) {\n    if (theme != 'auto') return theme\n\n    if (!this.darkMedia) {\n      this.darkMedia = matchMedia('(prefers-color-scheme: dark)')\n      if (this.darkMedia.media.match(/^not/)) return 'light'\n\n      this.darkMedia.addEventListener('change', this.darkMediaCallback)\n    }\n\n    return this.darkMedia.matches ? 'dark' : 'light'\n  }\n\n  handleClickOutside = (e) => {\n    const { element } = this.props\n\n    if (e.target != element) {\n      if (this.state.showSkins) {\n        this.closeSkins()\n      }\n\n      if (this.props.onClickOutside) {\n        this.props.onClickOutside(e)\n      }\n    }\n  }\n\n  handleBaseClick = (e) => {\n    if (!this.state.showSkins) return\n    if (!e.target.closest('.menu')) {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  handleBaseKeydown = (e) => {\n    if (!this.state.showSkins) return\n    if (e.key == 'Escape') {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  initDynamicPerLine(props = this.props) {\n    if (!props.dynamicWidth) return\n    const { element, emojiButtonSize } = props\n\n    const calculatePerLine = () => {\n      const { width } = element.getBoundingClientRect()\n      return Math.floor(width / emojiButtonSize)\n    }\n\n    const observer = new ResizeObserver(() => {\n      this.unobserve({ except: observer })\n      this.setState({ perLine: calculatePerLine() }, () => {\n        this.initGrid()\n        this.forceUpdate(() => {\n          this.observeCategories()\n          this.observeRows()\n        })\n      })\n    })\n\n    observer.observe(element)\n    this.observers.push(observer)\n\n    return calculatePerLine()\n  }\n\n  getPerLine() {\n    return this.state.perLine || this.props.perLine\n  }\n\n  getEmojiByPos([p1, p2]) {\n    const grid = this.state.searchResults || this.grid\n    const emoji = grid[p1] && grid[p1][p2]\n\n    if (!emoji) return\n    return SearchIndex.get(emoji)\n  }\n\n  observeCategories() {\n    const navigation = this.refs.navigation.current\n    if (!navigation) return\n\n    const visibleCategories = new Map()\n    const setFocusedCategory = (categoryId) => {\n      if (categoryId != navigation.state.categoryId) {\n        navigation.setState({ categoryId })\n      }\n    }\n\n    const observerOptions = {\n      root: this.refs.scroll.current,\n      threshold: [0.0, 1.0],\n    }\n\n    const observer = new IntersectionObserver((entries) => {\n      for (const entry of entries) {\n        const id = entry.target.dataset.id\n        visibleCategories.set(id, entry.intersectionRatio)\n      }\n\n      const ratios = [...visibleCategories]\n      for (const [id, ratio] of ratios) {\n        if (ratio) {\n          setFocusedCategory(id)\n          break\n        }\n      }\n    }, observerOptions)\n\n    for (const { root } of this.refs.categories.values()) {\n      observer.observe(root.current)\n    }\n\n    this.observers.push(observer)\n  }\n\n  observeRows() {\n    const visibleRows = { ...this.state.visibleRows }\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        for (const entry of entries) {\n          const index = parseInt(entry.target.dataset.index)\n\n          if (entry.isIntersecting) {\n            visibleRows[index] = true\n          } else {\n            delete visibleRows[index]\n          }\n        }\n\n        this.setState({ visibleRows })\n      },\n      {\n        root: this.refs.scroll.current,\n        rootMargin: `${\n          this.props.emojiButtonSize * (Performance.rowsPerRender + 5)\n        }px 0px ${this.props.emojiButtonSize * Performance.rowsPerRender}px`,\n      },\n    )\n\n    for (const { rows } of this.refs.categories.values()) {\n      for (const row of rows) {\n        if (row.current) {\n          observer.observe(row.current)\n        }\n      }\n    }\n\n    this.observers.push(observer)\n  }\n\n  preventDefault(e) {\n    e.preventDefault()\n  }\n\n  handleSearchClick = () => {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    if (!emoji) return\n\n    this.setState({ pos: [-1, -1] })\n  }\n\n  handleSearchInput = async () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    const { value } = input\n    const searchResults = await SearchIndex.search(value)\n    const afterRender = () => {\n      if (!this.refs.scroll.current) return\n      this.refs.scroll.current.scrollTop = 0\n    }\n\n    if (!searchResults) {\n      return this.setState({ searchResults, pos: [-1, -1] }, afterRender)\n    }\n\n    const pos = input.selectionStart == input.value.length ? [0, 0] : [-1, -1]\n    const grid = []\n    grid.setsize = searchResults.length\n    let row = null\n\n    for (let emoji of searchResults) {\n      if (!grid.length || row.length == this.getPerLine()) {\n        row = []\n        row.__categoryId = 'search'\n        row.__index = grid.length\n        grid.push(row)\n      }\n\n      row.push(emoji)\n    }\n\n    this.ignoreMouse()\n    this.setState({ searchResults: grid, pos }, afterRender)\n  }\n\n  handleSearchKeyDown = (e) => {\n    // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n    const input = e.currentTarget\n    e.stopImmediatePropagation()\n\n    switch (e.key) {\n      case 'ArrowLeft':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, left: true })\n        break\n\n      case 'ArrowRight':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, right: true })\n        break\n\n      case 'ArrowUp':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, up: true })\n        break\n\n      case 'ArrowDown':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, down: true })\n        break\n\n      case 'Enter':\n        e.preventDefault()\n        this.handleEmojiClick({ e, pos: this.state.pos })\n        break\n\n      case 'Escape':\n        e.preventDefault()\n        if (this.state.searchResults) {\n          this.clearSearch()\n        } else {\n          this.unfocusSearch()\n        }\n        break\n\n      default:\n        break\n    }\n  }\n\n  clearSearch = () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.value = ''\n    input.focus()\n\n    this.handleSearchInput()\n  }\n\n  unfocusSearch() {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.blur()\n  }\n\n  navigate({ e, input, left, right, up, down }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    let [p1, p2] = this.state.pos\n\n    const pos = (() => {\n      if (p1 == 0) {\n        if (p2 == 0 && !e.repeat && (left || up)) {\n          return null\n        }\n      }\n\n      if (p1 == -1) {\n        if (\n          !e.repeat &&\n          (right || down) &&\n          input.selectionStart == input.value.length\n        ) {\n          return [0, 0]\n        }\n\n        return null\n      }\n\n      if (left || right) {\n        let row = grid[p1]\n        const increment = left ? -1 : 1\n\n        p2 += increment\n        if (!row[p2]) {\n          p1 += increment\n          row = grid[p1]\n\n          if (!row) {\n            p1 = left ? 0 : grid.length - 1\n            p2 = left ? 0 : grid[p1].length - 1\n\n            return [p1, p2]\n          }\n\n          p2 = left ? row.length - 1 : 0\n        }\n\n        return [p1, p2]\n      }\n\n      if (up || down) {\n        p1 += up ? -1 : 1\n        const row = grid[p1]\n\n        if (!row) {\n          p1 = up ? 0 : grid.length - 1\n          p2 = up ? 0 : grid[p1].length - 1\n\n          return [p1, p2]\n        }\n\n        if (!row[p2]) {\n          p2 = row.length - 1\n        }\n\n        return [p1, p2]\n      }\n    })()\n\n    if (pos) {\n      e.preventDefault()\n    } else {\n      if (this.state.pos[0] > -1) {\n        this.setState({ pos: [-1, -1] })\n      }\n\n      return\n    }\n\n    this.setState({ pos, keyboard: true }, () => {\n      this.scrollTo({ row: pos[0] })\n    })\n  }\n\n  scrollTo({ categoryId, row }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    const scroll = this.refs.scroll.current\n    const scrollRect = scroll.getBoundingClientRect()\n\n    let scrollTop = 0\n\n    if (row >= 0) {\n      categoryId = grid[row].__categoryId\n    }\n\n    if (categoryId) {\n      const ref =\n        this.refs[categoryId] || this.refs.categories.get(categoryId).root\n      const categoryRect = ref.current.getBoundingClientRect()\n\n      scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1\n    }\n\n    if (row >= 0) {\n      if (!row) {\n        scrollTop = 0\n      } else {\n        const rowIndex = grid[row].__index\n        const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize\n        const rowBot =\n          rowTop +\n          this.props.emojiButtonSize +\n          this.props.emojiButtonSize * 0.88\n\n        if (rowTop < scroll.scrollTop) {\n          scrollTop = rowTop\n        } else if (rowBot > scroll.scrollTop + scrollRect.height) {\n          scrollTop = rowBot - scrollRect.height\n        } else {\n          return\n        }\n      }\n    }\n\n    this.ignoreMouse()\n    scroll.scrollTop = scrollTop\n  }\n\n  ignoreMouse() {\n    this.mouseIsIgnored = true\n    clearTimeout(this.ignoreMouseTimer)\n    this.ignoreMouseTimer = setTimeout(() => {\n      delete this.mouseIsIgnored\n    }, 100)\n  }\n\n  handleCategoryClick = ({ category, i }) => {\n    this.scrollTo(i == 0 ? { row: -1 } : { categoryId: category.id })\n  }\n\n  handleEmojiOver(pos) {\n    if (this.mouseIsIgnored || this.state.showSkins) return\n    this.setState({ pos: pos || [-1, -1], keyboard: false })\n  }\n\n  handleEmojiClick({ e, emoji, pos }) {\n    if (!this.props.onEmojiSelect) return\n\n    if (!emoji && pos) {\n      emoji = this.getEmojiByPos(pos)\n    }\n\n    if (emoji) {\n      const emojiData = getEmojiData(emoji, { skinIndex: this.state.skin - 1 })\n\n      if (this.props.maxFrequentRows) {\n        FrequentlyUsed.add(emojiData, this.props)\n      }\n\n      this.props.onEmojiSelect(emojiData, e)\n    }\n  }\n\n  openSkins = (e) => {\n    const { currentTarget } = e\n    const rect = currentTarget.getBoundingClientRect()\n\n    this.setState({ showSkins: rect }, async () => {\n      // Firefox requires 2 frames for the transition to consistenly work\n      await sleep(2)\n\n      const menu = this.refs.menu.current\n      if (!menu) return\n\n      menu.classList.remove('hidden')\n      this.refs.skinToneRadio.current.focus()\n\n      this.base.addEventListener('click', this.handleBaseClick, true)\n      this.base.addEventListener('keydown', this.handleBaseKeydown, true)\n    })\n  }\n\n  closeSkins() {\n    if (!this.state.showSkins) return\n    this.setState({ showSkins: null, tempSkin: null })\n\n    this.base.removeEventListener('click', this.handleBaseClick)\n    this.base.removeEventListener('keydown', this.handleBaseKeydown)\n  }\n\n  handleSkinMouseOver(tempSkin) {\n    this.setState({ tempSkin })\n  }\n\n  handleSkinClick(skin) {\n    this.ignoreMouse()\n    this.closeSkins()\n\n    this.setState({ skin, tempSkin: null })\n    Store.set('skin', skin)\n  }\n\n  renderNav() {\n    return (\n      <Navigation\n        key={this.navKey}\n        ref={this.refs.navigation}\n        icons={this.props.icons}\n        theme={this.state.theme}\n        dir={this.dir}\n        unfocused={!!this.state.searchResults}\n        position={this.props.navPosition}\n        onClick={this.handleCategoryClick}\n      />\n    )\n  }\n\n  renderPreview() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const noSearchResults =\n      this.state.searchResults && !this.state.searchResults.length\n\n    return (\n      <div\n        id=\"preview\"\n        class=\"flex flex-middle\"\n        dir={this.dir}\n        data-position={this.props.previewPosition}\n      >\n        <div class=\"flex flex-middle flex-grow\">\n          <div\n            class=\"flex flex-auto flex-middle flex-center\"\n            style={{\n              height: this.props.emojiButtonSize,\n              fontSize: this.props.emojiButtonSize,\n            }}\n          >\n            <Emoji\n              emoji={emoji}\n              id={\n                noSearchResults\n                  ? this.props.noResultsEmoji || 'cry'\n                  : this.props.previewEmoji ||\n                    (this.props.previewPosition == 'top'\n                      ? 'point_down'\n                      : 'point_up')\n              }\n              set={this.props.set}\n              size={this.props.emojiButtonSize}\n              skin={this.state.tempSkin || this.state.skin}\n              spritesheet={true}\n              getSpritesheetURL={this.props.getSpritesheetURL}\n            />\n          </div>\n\n          <div class={`margin-${this.dir[0]}`}>\n            {emoji || noSearchResults ? (\n              <div class={`padding-${this.dir[2]} align-${this.dir[0]}`}>\n                <div class=\"preview-title ellipsis\">\n                  {emoji ? emoji.name : I18n.search_no_results_1}\n                </div>\n                <div class=\"preview-subtitle ellipsis color-c\">\n                  {emoji ? emoji.skins[0].shortcodes : I18n.search_no_results_2}\n                </div>\n              </div>\n            ) : (\n              <div class=\"preview-placeholder color-c\">{I18n.pick}</div>\n            )}\n          </div>\n        </div>\n\n        {!emoji &&\n          this.props.skinTonePosition == 'preview' &&\n          this.renderSkinToneButton()}\n      </div>\n    )\n  }\n\n  renderEmojiButton(emoji, { pos, posinset, grid }) {\n    const size = this.props.emojiButtonSize\n    const skin = this.state.tempSkin || this.state.skin\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n    const native = emojiSkin.native\n    const selected = deepEqual(this.state.pos, pos)\n    const key = pos.concat(emoji.id).join('')\n\n    return (\n      <PureInlineComponent key={key} {...{ selected, skin, size }}>\n        <button\n          aria-label={native}\n          aria-selected={selected || undefined}\n          aria-posinset={posinset}\n          aria-setsize={grid.setsize}\n          data-keyboard={this.state.keyboard}\n          title={this.props.previewPosition == 'none' ? emoji.name : undefined}\n          type=\"button\"\n          class=\"flex flex-center flex-middle\"\n          tabindex=\"-1\"\n          onClick={(e) => this.handleEmojiClick({ e, emoji })}\n          onMouseEnter={() => this.handleEmojiOver(pos)}\n          onMouseLeave={() => this.handleEmojiOver()}\n          style={{\n            width: this.props.emojiButtonSize,\n            height: this.props.emojiButtonSize,\n            fontSize: this.props.emojiSize,\n            lineHeight: 0,\n          }}\n        >\n          <div\n            aria-hidden=\"true\"\n            class=\"background\"\n            style={{\n              borderRadius: this.props.emojiButtonRadius,\n              backgroundColor: this.props.emojiButtonColors\n                ? this.props.emojiButtonColors[\n                    (posinset - 1) % this.props.emojiButtonColors.length\n                  ]\n                : undefined,\n            }}\n          ></div>\n          <Emoji\n            emoji={emoji}\n            set={this.props.set}\n            size={this.props.emojiSize}\n            skin={skin}\n            spritesheet={true}\n            getSpritesheetURL={this.props.getSpritesheetURL}\n          />\n        </button>\n      </PureInlineComponent>\n    )\n  }\n\n  renderSearch() {\n    const renderSkinTone =\n      this.props.previewPosition == 'none' ||\n      this.props.skinTonePosition == 'search'\n\n    return (\n      <div>\n        <div class=\"spacer\"></div>\n        <div class=\"flex flex-middle\">\n          <div class=\"search relative flex-grow\">\n            <input\n              type=\"search\"\n              ref={this.refs.searchInput}\n              placeholder={I18n.search}\n              onClick={this.handleSearchClick}\n              onInput={this.handleSearchInput}\n              onKeyDown={this.handleSearchKeyDown}\n              autoComplete=\"off\"\n            ></input>\n            <span class=\"icon loupe flex\">{Icons.search.loupe}</span>\n            {this.state.searchResults && (\n              <button\n                title=\"Clear\"\n                aria-label=\"Clear\"\n                type=\"button\"\n                class=\"icon delete flex\"\n                onClick={this.clearSearch}\n                onMouseDown={this.preventDefault}\n              >\n                {Icons.search.delete}\n              </button>\n            )}\n          </div>\n\n          {renderSkinTone && this.renderSkinToneButton()}\n        </div>\n      </div>\n    )\n  }\n\n  renderSearchResults() {\n    const { searchResults } = this.state\n    if (!searchResults) return null\n\n    return (\n      <div class=\"category\" ref={this.refs.search}>\n        <div class={`sticky padding-small align-${this.dir[0]}`}>\n          {I18n.categories.search}\n        </div>\n        <div>\n          {!searchResults.length ? (\n            <div class={`padding-small align-${this.dir[0]}`}>\n              {this.props.onAddCustomEmoji && (\n                <a onClick={this.props.onAddCustomEmoji}>{I18n.add_custom}</a>\n              )}\n            </div>\n          ) : (\n            searchResults.map((row, i) => {\n              return (\n                <div class=\"flex\">\n                  {row.map((emoji, ii) => {\n                    return this.renderEmojiButton(emoji, {\n                      pos: [i, ii],\n                      posinset: i * this.props.perLine + ii + 1,\n                      grid: searchResults,\n                    })\n                  })}\n                </div>\n              )\n            })\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  renderCategories() {\n    const { categories } = Data\n    const hidden = !!this.state.searchResults\n    const perLine = this.getPerLine()\n\n    return (\n      <div\n        style={{\n          visibility: hidden ? 'hidden' : undefined,\n          display: hidden ? 'none' : undefined,\n          height: '100%',\n        }}\n      >\n        {categories.map((category) => {\n          const { root, rows } = this.refs.categories.get(category.id)\n\n          return (\n            <div\n              data-id={category.target ? category.target.id : category.id}\n              class=\"category\"\n              ref={root}\n            >\n              <div class={`sticky padding-small align-${this.dir[0]}`}>\n                {category.name || I18n.categories[category.id]}\n              </div>\n              <div\n                class=\"relative\"\n                style={{\n                  height: rows.length * this.props.emojiButtonSize,\n                }}\n              >\n                {rows.map((row, i) => {\n                  const targetRow =\n                    row.index - (row.index % Performance.rowsPerRender)\n                  const visible = this.state.visibleRows[targetRow]\n                  const ref = 'current' in row ? row : undefined\n\n                  if (!visible && !ref) {\n                    return null\n                  }\n\n                  const start = i * perLine\n                  const end = start + perLine\n                  const emojiIds = category.emojis.slice(start, end)\n\n                  if (emojiIds.length < perLine) {\n                    emojiIds.push(...new Array(perLine - emojiIds.length))\n                  }\n\n                  return (\n                    <div\n                      key={row.index}\n                      data-index={row.index}\n                      ref={ref}\n                      class=\"flex row\"\n                      style={{ top: i * this.props.emojiButtonSize }}\n                    >\n                      {visible &&\n                        emojiIds.map((emojiId, ii) => {\n                          if (!emojiId) {\n                            return (\n                              <div\n                                style={{\n                                  width: this.props.emojiButtonSize,\n                                  height: this.props.emojiButtonSize,\n                                }}\n                              ></div>\n                            )\n                          }\n\n                          const emoji = SearchIndex.get(emojiId)\n\n                          return this.renderEmojiButton(emoji, {\n                            pos: [row.index, ii],\n                            posinset: row.posinset + ii,\n                            grid: this.grid,\n                          })\n                        })}\n                    </div>\n                  )\n                })}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  renderSkinToneButton() {\n    if (this.props.skinTonePosition == 'none') {\n      return null\n    }\n\n    return (\n      <div\n        class=\"flex flex-auto flex-center flex-middle\"\n        style={{\n          position: 'relative',\n          width: this.props.emojiButtonSize,\n          height: this.props.emojiButtonSize,\n        }}\n      >\n        <button\n          type=\"button\"\n          ref={this.refs.skinToneButton}\n          class=\"skin-tone-button flex flex-auto flex-center flex-middle\"\n          aria-selected={this.state.showSkins ? '' : undefined}\n          aria-label={I18n.skins.choose}\n          title={I18n.skins.choose}\n          onClick={this.openSkins}\n          style={{\n            width: this.props.emojiSize,\n            height: this.props.emojiSize,\n          }}\n        >\n          <span class={`skin-tone skin-tone-${this.state.skin}`}></span>\n        </button>\n      </div>\n    )\n  }\n\n  renderLiveRegion() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const contents = emoji ? emoji.name : ''\n\n    return (\n      <div aria-live=\"polite\" class=\"sr-only\">\n        {contents}\n      </div>\n    )\n  }\n\n  renderSkins() {\n    const skinToneButton = this.refs.skinToneButton.current\n    const skinToneButtonRect = skinToneButton.getBoundingClientRect()\n    const baseRect = this.base.getBoundingClientRect()\n\n    const position = {}\n\n    if (this.dir == 'ltr') {\n      position.right = baseRect.right - skinToneButtonRect.right - 3\n    } else {\n      position.left = skinToneButtonRect.left - baseRect.left - 3\n    }\n\n    if (\n      this.props.previewPosition == 'bottom' &&\n      this.props.skinTonePosition == 'preview'\n    ) {\n      position.bottom = baseRect.bottom - skinToneButtonRect.top + 6\n    } else {\n      position.top = skinToneButtonRect.bottom - baseRect.top + 3\n      position.bottom = 'auto'\n    }\n\n    return (\n      <div\n        ref={this.refs.menu}\n        role=\"radiogroup\"\n        dir={this.dir}\n        aria-label={I18n.skins.choose}\n        class=\"menu hidden\"\n        data-position={position.top ? 'top' : 'bottom'}\n        style={position}\n      >\n        {[...Array(6).keys()].map((i) => {\n          const skin = i + 1\n          const checked = this.state.skin == skin\n\n          return (\n            <div>\n              <input\n                type=\"radio\"\n                name=\"skin-tone\"\n                value={skin}\n                aria-label={I18n.skins[skin]}\n                ref={checked ? this.refs.skinToneRadio : null}\n                defaultChecked={checked}\n                onChange={() => this.handleSkinMouseOver(skin)}\n                onKeyDown={(e) => {\n                  if (\n                    e.code == 'Enter' ||\n                    e.code == 'Space' ||\n                    e.code == 'Tab'\n                  ) {\n                    e.preventDefault()\n                    this.handleSkinClick(skin)\n                  }\n                }}\n              ></input>\n\n              <button\n                aria-hidden=\"true\"\n                tabindex=\"-1\"\n                onClick={() => this.handleSkinClick(skin)}\n                onMouseEnter={() => this.handleSkinMouseOver(skin)}\n                onMouseLeave={() => this.handleSkinMouseOver()}\n                class=\"option flex flex-grow flex-middle\"\n              >\n                <span class={`skin-tone skin-tone-${skin}`}></span>\n                <span class=\"margin-small-lr\">{I18n.skins[skin]}</span>\n              </button>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  render() {\n    const lineWidth = this.props.perLine * this.props.emojiButtonSize\n\n    return (\n      <section\n        id=\"root\"\n        class=\"flex flex-column\"\n        dir={this.dir}\n        style={{\n          width: this.props.dynamicWidth\n            ? '100%'\n            : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`,\n        }}\n        data-emoji-set={this.props.set}\n        data-theme={this.state.theme}\n        data-menu={this.state.showSkins ? '' : undefined}\n      >\n        {this.props.previewPosition == 'top' && this.renderPreview()}\n        {this.props.navPosition == 'top' && this.renderNav()}\n        {this.props.searchPosition == 'sticky' && (\n          <div class=\"padding-lr\">{this.renderSearch()}</div>\n        )}\n\n        <div ref={this.refs.scroll} class=\"scroll flex-grow padding-lr\">\n          <div\n            style={{\n              width: this.props.dynamicWidth ? '100%' : lineWidth,\n              height: '100%',\n            }}\n          >\n            {this.props.searchPosition == 'static' && this.renderSearch()}\n            {this.renderSearchResults()}\n            {this.renderCategories()}\n          </div>\n        </div>\n\n        {this.props.navPosition == 'bottom' && this.renderNav()}\n        {this.props.previewPosition == 'bottom' && this.renderPreview()}\n        {this.state.showSkins && this.renderSkins()}\n        {this.renderLiveRegion()}\n      </section>\n    )\n  }\n}\n", "export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n", "import{options as r,Fragment as _}from\"preact\";export{Fragment}from\"preact\";var o=0;function e(_,e,n,t,f){var l,s,u={};for(s in e)\"ref\"==s?l=e[s]:u[s]=e[s];var a={type:_,props:u,key:n,ref:l,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--o,__source:t,__self:f};if(\"function\"==typeof _&&(l=_.defaultProps))for(s in l)void 0===u[s]&&(u[s]=l[s]);return r.vnode&&r.vnode(a),a}export{e as jsx,e as jsxs,e as jsxDEV};\n//# sourceMappingURL=jsxRuntime.module.js.map\n", "import { options, Fragment } from 'preact';\r\n\r\n/** @typedef {import('preact').VNode} VNode */\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * @fileoverview\r\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\r\n * - jsx(type, props, key)\r\n * - jsxs(type, props, key)\r\n * - jsxDEV(type, props, key, __source, __self)\r\n *\r\n * The implementation of createVNode here is optimized for performance.\r\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\r\n */\r\n\r\n/**\r\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\r\n * @param {VNode['type']} type\r\n * @param {VNode['props']} props\r\n * @param {VNode['key']} [key]\r\n * @param {string} [__source]\r\n * @param {string} [__self]\r\n */\r\nfunction createVNode(type, props, key, __source, __self) {\r\n\t// We'll want to preserve `ref` in props to get rid of the need for\r\n\t// forwardRef components in the future, but that should happen via\r\n\t// a separate PR.\r\n\tlet normalizedProps = {},\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'ref') {\r\n\t\t\tref = props[i];\r\n\t\t} else {\r\n\t\t\tnormalizedProps[i] = props[i];\r\n\t\t}\r\n\t}\r\n\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops: normalizedProps,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: --vnodeId,\r\n\t\t__source,\r\n\t\t__self\r\n\t};\r\n\r\n\t// If a Component VNode, check for and apply defaultProps.\r\n\t// Note: `type` is often a String, and can be `undefined` in development.\r\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\r\n\t\tfor (i in ref)\r\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\r\n\t\t\t\tnormalizedProps[i] = ref[i];\r\n\t\t\t}\r\n\t}\r\n\r\n\tif (options.vnode) options.vnode(vnode);\r\n\treturn vnode;\r\n}\r\n\r\nexport {\r\n\tcreateVNode as jsx,\r\n\tcreateVNode as jsxs,\r\n\tcreateVNode as jsxDEV,\r\n\tFragment\r\n};\r\n", "var n,l,u,i,t,r,o,f,e={},c=[],s=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function a(n,l){for(var u in l)n[u]=l[u];return n}function h(n){var l=n.parentNode;l&&l.removeChild(n)}function v(l,u,i){var t,r,o,f={};for(o in u)\"key\"==o?t=u[o]:\"ref\"==o?r=u[o]:f[o]=u[o];if(arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),\"function\"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)void 0===f[o]&&(f[o]=l.defaultProps[o]);return y(l,f,t,r,null)}function y(n,i,t,r,o){var f={type:n,props:i,key:t,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++u:o};return null==o&&null!=l.vnode&&l.vnode(f),f}function p(){return{current:null}}function d(n){return n.children}function _(n,l){this.props=n,this.context=l}function k(n,l){if(null==l)return n.__?k(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return\"function\"==typeof n.type?k(n):null}function b(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return b(n)}}function m(n){(!n.__d&&(n.__d=!0)&&t.push(n)&&!g.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||r)(g)}function g(){for(var n;g.__r=t.length;)n=t.sort(function(n,l){return n.__v.__b-l.__v.__b}),t=[],n.some(function(n){var l,u,i,t,r,o;n.__d&&(r=(t=(l=n).__v).__e,(o=l.__P)&&(u=[],(i=a({},t)).__v=t.__v+1,j(o,t,i,l.__n,void 0!==o.ownerSVGElement,null!=t.__h?[r]:null,u,null==r?k(t):r,t.__h),z(u,t),t.__e!=r&&b(t)))})}function w(n,l,u,i,t,r,o,f,s,a){var h,v,p,_,b,m,g,w=i&&i.__k||c,A=w.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(_=u.__k[h]=null==(_=l[h])||\"boolean\"==typeof _?null:\"string\"==typeof _||\"number\"==typeof _||\"bigint\"==typeof _?y(null,_,null,null,_):Array.isArray(_)?y(d,{children:_},null,null,null):_.__b>0?y(_.type,_.props,_.key,null,_.__v):_)){if(_.__=u,_.__b=u.__b+1,null===(p=w[h])||p&&_.key==p.key&&_.type===p.type)w[h]=void 0;else for(v=0;v<A;v++){if((p=w[v])&&_.key==p.key&&_.type===p.type){w[v]=void 0;break}p=null}j(n,_,p=p||e,t,r,o,f,s,a),b=_.__e,(v=_.ref)&&p.ref!=v&&(g||(g=[]),p.ref&&g.push(p.ref,null,_),g.push(v,_.__c||b,_)),null!=b?(null==m&&(m=b),\"function\"==typeof _.type&&_.__k===p.__k?_.__d=s=x(_,s,n):s=P(n,_,p,w,b,s),\"function\"==typeof u.type&&(u.__d=s)):s&&p.__e==s&&s.parentNode!=n&&(s=k(p))}for(u.__e=m,h=A;h--;)null!=w[h]&&(\"function\"==typeof u.type&&null!=w[h].__e&&w[h].__e==u.__d&&(u.__d=k(i,h+1)),N(w[h],w[h]));if(g)for(h=0;h<g.length;h++)M(g[h],g[++h],g[++h])}function x(n,l,u){for(var i,t=n.__k,r=0;t&&r<t.length;r++)(i=t[r])&&(i.__=n,l=\"function\"==typeof i.type?x(i,l,u):P(u,i,i,t,i.__e,l));return l}function A(n,l){return l=l||[],null==n||\"boolean\"==typeof n||(Array.isArray(n)?n.some(function(n){A(n,l)}):l.push(n)),l}function P(n,l,u,i,t,r){var o,f,e;if(void 0!==l.__d)o=l.__d,l.__d=void 0;else if(null==u||t!=r||null==t.parentNode)n:if(null==r||r.parentNode!==n)n.appendChild(t),o=null;else{for(f=r,e=0;(f=f.nextSibling)&&e<i.length;e+=2)if(f==t)break n;n.insertBefore(t,r),o=r}return void 0!==o?o:t.nextSibling}function C(n,l,u,i,t){var r;for(r in u)\"children\"===r||\"key\"===r||r in l||H(n,r,null,u[r],i);for(r in l)t&&\"function\"!=typeof l[r]||\"children\"===r||\"key\"===r||\"value\"===r||\"checked\"===r||u[r]===l[r]||H(n,r,l[r],u[r],i)}function $(n,l,u){\"-\"===l[0]?n.setProperty(l,u):n[l]=null==u?\"\":\"number\"!=typeof u||s.test(l)?u:u+\"px\"}function H(n,l,u,i,t){var r;n:if(\"style\"===l)if(\"string\"==typeof u)n.style.cssText=u;else{if(\"string\"==typeof i&&(n.style.cssText=i=\"\"),i)for(l in i)u&&l in u||$(n.style,l,\"\");if(u)for(l in u)i&&u[l]===i[l]||$(n.style,l,u[l])}else if(\"o\"===l[0]&&\"n\"===l[1])r=l!==(l=l.replace(/Capture$/,\"\")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?i||n.addEventListener(l,r?T:I,r):n.removeEventListener(l,r?T:I,r);else if(\"dangerouslySetInnerHTML\"!==l){if(t)l=l.replace(/xlink[H:h]/,\"h\").replace(/sName$/,\"s\");else if(\"href\"!==l&&\"list\"!==l&&\"form\"!==l&&\"tabIndex\"!==l&&\"download\"!==l&&l in n)try{n[l]=null==u?\"\":u;break n}catch(n){}\"function\"==typeof u||(null!=u&&(!1!==u||\"a\"===l[0]&&\"r\"===l[1])?n.setAttribute(l,u):n.removeAttribute(l))}}function I(n){this.l[n.type+!1](l.event?l.event(n):n)}function T(n){this.l[n.type+!0](l.event?l.event(n):n)}function j(n,u,i,t,r,o,f,e,c){var s,h,v,y,p,k,b,m,g,x,A,P=u.type;if(void 0!==u.constructor)return null;null!=i.__h&&(c=i.__h,e=u.__e=i.__e,u.__h=null,o=[e]),(s=l.__b)&&s(u);try{n:if(\"function\"==typeof P){if(m=u.props,g=(s=P.contextType)&&t[s.__c],x=s?g?g.props.value:s.__:t,i.__c?b=(h=u.__c=i.__c).__=h.__E:(\"prototype\"in P&&P.prototype.render?u.__c=h=new P(m,x):(u.__c=h=new _(m,x),h.constructor=P,h.render=O),g&&g.sub(h),h.props=m,h.state||(h.state={}),h.context=x,h.__n=t,v=h.__d=!0,h.__h=[]),null==h.__s&&(h.__s=h.state),null!=P.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=a({},h.__s)),a(h.__s,P.getDerivedStateFromProps(m,h.__s))),y=h.props,p=h.state,v)null==P.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(null==P.getDerivedStateFromProps&&m!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(m,x),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(m,h.__s,x)||u.__v===i.__v){h.props=m,h.state=h.__s,u.__v!==i.__v&&(h.__d=!1),h.__v=u,u.__e=i.__e,u.__k=i.__k,u.__k.forEach(function(n){n&&(n.__=u)}),h.__h.length&&f.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(m,h.__s,x),null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,p,k)})}h.context=x,h.props=m,h.state=h.__s,(s=l.__r)&&s(u),h.__d=!1,h.__v=u,h.__P=n,s=h.render(h.props,h.state,h.context),h.state=h.__s,null!=h.getChildContext&&(t=a(a({},t),h.getChildContext())),v||null==h.getSnapshotBeforeUpdate||(k=h.getSnapshotBeforeUpdate(y,p)),A=null!=s&&s.type===d&&null==s.key?s.props.children:s,w(n,Array.isArray(A)?A:[A],u,i,t,r,o,f,e,c),h.base=u.__e,u.__h=null,h.__h.length&&f.push(h),b&&(h.__E=h.__=null),h.__e=!1}else null==o&&u.__v===i.__v?(u.__k=i.__k,u.__e=i.__e):u.__e=L(i.__e,u,i,t,r,o,f,c);(s=l.diffed)&&s(u)}catch(n){u.__v=null,(c||null!=o)&&(u.__e=e,u.__h=!!c,o[o.indexOf(e)]=null),l.__e(n,u,i)}}function z(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function L(l,u,i,t,r,o,f,c){var s,a,v,y=i.props,p=u.props,d=u.type,_=0;if(\"svg\"===d&&(r=!0),null!=o)for(;_<o.length;_++)if((s=o[_])&&\"setAttribute\"in s==!!d&&(d?s.localName===d:3===s.nodeType)){l=s,o[_]=null;break}if(null==l){if(null===d)return document.createTextNode(p);l=r?document.createElementNS(\"http://www.w3.org/2000/svg\",d):document.createElement(d,p.is&&p),o=null,c=!1}if(null===d)y===p||c&&l.data===p||(l.data=p);else{if(o=o&&n.call(l.childNodes),a=(y=i.props||e).dangerouslySetInnerHTML,v=p.dangerouslySetInnerHTML,!c){if(null!=o)for(y={},_=0;_<l.attributes.length;_++)y[l.attributes[_].name]=l.attributes[_].value;(v||a)&&(v&&(a&&v.__html==a.__html||v.__html===l.innerHTML)||(l.innerHTML=v&&v.__html||\"\"))}if(C(l,p,y,r,c),v)u.__k=[];else if(_=u.props.children,w(l,Array.isArray(_)?_:[_],u,i,t,r&&\"foreignObject\"!==d,o,f,o?o[0]:i.__k&&k(i,0),c),null!=o)for(_=o.length;_--;)null!=o[_]&&h(o[_]);c||(\"value\"in p&&void 0!==(_=p.value)&&(_!==y.value||_!==l.value||\"progress\"===d&&!_)&&H(l,\"value\",_,y.value,!1),\"checked\"in p&&void 0!==(_=p.checked)&&_!==l.checked&&H(l,\"checked\",_,y.checked,!1))}return l}function M(n,u,i){try{\"function\"==typeof n?n(u):n.current=u}catch(n){l.__e(n,i)}}function N(n,u,i){var t,r;if(l.unmount&&l.unmount(n),(t=n.ref)&&(t.current&&t.current!==n.__e||M(t,null,u)),null!=(t=n.__c)){if(t.componentWillUnmount)try{t.componentWillUnmount()}catch(n){l.__e(n,u)}t.base=t.__P=null}if(t=n.__k)for(r=0;r<t.length;r++)t[r]&&N(t[r],u,\"function\"!=typeof n.type);i||null==n.__e||h(n.__e),n.__e=n.__d=void 0}function O(n,l,u){return this.constructor(n,u)}function S(u,i,t){var r,o,f;l.__&&l.__(u,i),o=(r=\"function\"==typeof t)?null:t&&t.__k||i.__k,f=[],j(i,u=(!r&&t||i).__k=v(d,null,[u]),o||e,e,void 0!==i.ownerSVGElement,!r&&t?[t]:o?null:i.firstChild?n.call(i.childNodes):null,f,!r&&t?t:o?o.__e:i.firstChild,r),z(f,u)}function q(n,l){S(n,l,q)}function B(l,u,i){var t,r,o,f=a({},l.props);for(o in u)\"key\"==o?t=u[o]:\"ref\"==o?r=u[o]:f[o]=u[o];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),y(l.type,f,t||l.key,r||l.ref,null)}function D(n,l){var u={__c:l=\"__cC\"+f++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,i;return this.getChildContext||(u=[],(i={})[l]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(m)},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u}n=c.slice,l={__e:function(n,l){for(var u,i,t;l=l.__;)if((u=l.__c)&&!u.__)try{if((i=u.constructor)&&null!=i.getDerivedStateFromError&&(u.setState(i.getDerivedStateFromError(n)),t=u.__d),null!=u.componentDidCatch&&(u.componentDidCatch(n),t=u.__d),t)return u.__E=u}catch(l){n=l}throw n}},u=0,i=function(n){return null!=n&&void 0===n.constructor},_.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=a({},this.state),\"function\"==typeof n&&(n=n(a({},u),this.props)),n&&a(u,n),null!=n&&this.__v&&(l&&this.__h.push(l),m(this))},_.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),m(this))},_.prototype.render=d,t=[],r=\"function\"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,g.__r=0,f=0;export{S as render,q as hydrate,v as createElement,v as h,d as Fragment,p as createRef,i as isValidElement,_ as Component,B as cloneElement,D as createContext,A as toChildArray,l as options};\n//# sourceMappingURL=preact.module.js.map\n", "export const EMPTY_OBJ = {};\r\nexport const EMPTY_ARR = [];\r\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\r\n", "import { EMPTY_ARR } from \"./constants\";\r\n\r\n/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\t// @ts-ignore We change the type of `obj` to be `O & P`\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Remove a child node from its parent if attached. This is a workaround for\r\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\r\n * is smaller than including a dedicated polyfill.\r\n * @param {Node} node The node to remove\r\n */\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n\r\nexport const slice = EMPTY_ARR.slice;\r\n", "import { _catchError } from './diff/catch-error';\r\n\r\n/**\r\n * The `option` object can potentially contain callback functions\r\n * that are called during various stages of our renderer. This is the\r\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\r\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\r\n * for a full list of available option hooks (most editors/IDEs allow you to\r\n * ctrl+click or cmd+click on mac the type definition below).\r\n * @type {import('./internal').Options}\r\n */\r\nconst options = {\r\n\t_catchError\r\n};\r\n\r\nexport default options;\r\n", "import { slice } from './util';\r\nimport options from './options';\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * Create an virtual node (used for JSX)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * constructor for this virtual node\r\n * @param {object | null | undefined} [props] The properties of the virtual node\r\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createElement(type, props, children) {\r\n\tlet normalizedProps = {},\r\n\t\tkey,\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'key') key = props[i];\r\n\t\telse if (i == 'ref') ref = props[i];\r\n\t\telse normalizedProps[i] = props[i];\r\n\t}\r\n\r\n\tif (arguments.length > 2) {\r\n\t\tnormalizedProps.children =\r\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\r\n\t}\r\n\r\n\t// If a Component VNode, check for and apply defaultProps\r\n\t// Note: type may be undefined in development, must never error here.\r\n\tif (typeof type == 'function' && type.defaultProps != null) {\r\n\t\tfor (i in type.defaultProps) {\r\n\t\t\tif (normalizedProps[i] === undefined) {\r\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn createVNode(type, normalizedProps, key, ref, null);\r\n}\r\n\r\n/**\r\n * Create a VNode (used internally by Preact)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * Constructor for this virtual node\r\n * @param {object | string | number | null} props The properties of this virtual node.\r\n * If this virtual node represents a text node, this is the text of the node (string or number).\r\n * @param {string | number | null} key The key for this virtual node, used when\r\n * diffing it against its children\r\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\r\n * receive a reference to its created child\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createVNode(type, props, key, ref, original) {\r\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\r\n\t// Do not inline into createElement and coerceToVNode!\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\r\n\t\t// be set to dom.nextSibling which can return `null` and it is important\r\n\t\t// to be able to distinguish between an uninitialized _nextDom and\r\n\t\t// a _nextDom that has been set to `null`\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: original == null ? ++vnodeId : original\r\n\t};\r\n\r\n\t// Only invoke the vnode hook if this was *not* a direct copy:\r\n\tif (original == null && options.vnode != null) options.vnode(vnode);\r\n\r\n\treturn vnode;\r\n}\r\n\r\nexport function createRef() {\r\n\treturn { current: null };\r\n}\r\n\r\nexport function Fragment(props) {\r\n\treturn props.children;\r\n}\r\n\r\n/**\r\n * Check if a the argument is a valid Preact VNode.\r\n * @param {*} vnode\r\n * @returns {vnode is import('./internal').VNode}\r\n */\r\nexport const isValidElement = vnode =>\r\n\tvnode != null && vnode.constructor === undefined;\r\n", "import { assign } from './util';\r\nimport { diff, commitRoot } from './diff/index';\r\nimport options from './options';\r\nimport { Fragment } from './create-element';\r\n\r\n/**\r\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\r\n * trigger rendering\r\n * @param {object} props The initial component props\r\n * @param {object} context The initial context from parent components'\r\n * getChildContext\r\n */\r\nexport function Component(props, context) {\r\n\tthis.props = props;\r\n\tthis.context = context;\r\n}\r\n\r\n/**\r\n * Update component state and schedule a re-render.\r\n * @this {import('./internal').Component}\r\n * @param {object | ((s: object, p: object) => object)} update A hash of state\r\n * properties to update with new values or a function that given the current\r\n * state and props returns a new partial state\r\n * @param {() => void} [callback] A function to be called once component state is\r\n * updated\r\n */\r\nComponent.prototype.setState = function(update, callback) {\r\n\t// only clone state when copying to nextState the first time.\r\n\tlet s;\r\n\tif (this._nextState != null && this._nextState !== this.state) {\r\n\t\ts = this._nextState;\r\n\t} else {\r\n\t\ts = this._nextState = assign({}, this.state);\r\n\t}\r\n\r\n\tif (typeof update == 'function') {\r\n\t\t// Some libraries like `immer` mark the current state as readonly,\r\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\r\n\t\tupdate = update(assign({}, s), this.props);\r\n\t}\r\n\r\n\tif (update) {\r\n\t\tassign(s, update);\r\n\t}\r\n\r\n\t// Skip update if updater function returned null\r\n\tif (update == null) return;\r\n\r\n\tif (this._vnode) {\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Immediately perform a synchronous re-render of the component\r\n * @this {import('./internal').Component}\r\n * @param {() => void} [callback] A function to be called after component is\r\n * re-rendered\r\n */\r\nComponent.prototype.forceUpdate = function(callback) {\r\n\tif (this._vnode) {\r\n\t\t// Set render mode so that we can differentiate where the render request\r\n\t\t// is coming from. We need this because forceUpdate should never call\r\n\t\t// shouldComponentUpdate\r\n\t\tthis._force = true;\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\r\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\r\n * @param {object} props Props (eg: JSX attributes) received from parent\r\n * element/component\r\n * @param {object} state The component's current state\r\n * @param {object} context Context object, as returned by the nearest\r\n * ancestor's `getChildContext()`\r\n * @returns {import('./index').ComponentChildren | void}\r\n */\r\nComponent.prototype.render = Fragment;\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n * @param {number | null} [childIndex]\r\n */\r\nexport function getDomSibling(vnode, childIndex) {\r\n\tif (childIndex == null) {\r\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\r\n\t\treturn vnode._parent\r\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\r\n\t\t\t: null;\r\n\t}\r\n\r\n\tlet sibling;\r\n\tfor (; childIndex < vnode._children.length; childIndex++) {\r\n\t\tsibling = vnode._children[childIndex];\r\n\r\n\t\tif (sibling != null && sibling._dom != null) {\r\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\r\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\r\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\r\n\t\t\treturn sibling._dom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we get here, we have not found a DOM node in this vnode's children.\r\n\t// We must resume from this vnode's sibling (in it's parent _children array)\r\n\t// Only climb up and search the parent if we aren't searching through a DOM\r\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\r\n\t// the search)\r\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\r\n}\r\n\r\n/**\r\n * Trigger in-place re-rendering of a component.\r\n * @param {import('./internal').Component} component The component to rerender\r\n */\r\nfunction renderComponent(component) {\r\n\tlet vnode = component._vnode,\r\n\t\toldDom = vnode._dom,\r\n\t\tparentDom = component._parentDom;\r\n\r\n\tif (parentDom) {\r\n\t\tlet commitQueue = [];\r\n\t\tconst oldVNode = assign({}, vnode);\r\n\t\toldVNode._original = vnode._original + 1;\r\n\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tvnode,\r\n\t\t\toldVNode,\r\n\t\t\tcomponent._globalContext,\r\n\t\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t\tvnode._hydrating != null ? [oldDom] : null,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom,\r\n\t\t\tvnode._hydrating\r\n\t\t);\r\n\t\tcommitRoot(commitQueue, vnode);\r\n\r\n\t\tif (vnode._dom != oldDom) {\r\n\t\t\tupdateParentDomPointers(vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n */\r\nfunction updateParentDomPointers(vnode) {\r\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\r\n\t\tvnode._dom = vnode._component.base = null;\r\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\r\n\t\t\tlet child = vnode._children[i];\r\n\t\t\tif (child != null && child._dom != null) {\r\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn updateParentDomPointers(vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * The render queue\r\n * @type {Array<import('./internal').Component>}\r\n */\r\nlet rerenderQueue = [];\r\n\r\n/**\r\n * Asynchronously schedule a callback\r\n * @type {(cb: () => void) => void}\r\n */\r\n/* istanbul ignore next */\r\n// Note the following line isn't tree-shaken by rollup cuz of rollup/rollup#2566\r\nconst defer =\r\n\ttypeof Promise == 'function'\r\n\t\t? Promise.prototype.then.bind(Promise.resolve())\r\n\t\t: setTimeout;\r\n\r\n/*\r\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\r\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\r\n * do, and when their effects will be applied. See the links below for some further reading on designing\r\n * asynchronous APIs.\r\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\r\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\r\n */\r\n\r\nlet prevDebounce;\r\n\r\n/**\r\n * Enqueue a rerender of a component\r\n * @param {import('./internal').Component} c The component to rerender\r\n */\r\nexport function enqueueRender(c) {\r\n\tif (\r\n\t\t(!c._dirty &&\r\n\t\t\t(c._dirty = true) &&\r\n\t\t\trerenderQueue.push(c) &&\r\n\t\t\t!process._rerenderCount++) ||\r\n\t\tprevDebounce !== options.debounceRendering\r\n\t) {\r\n\t\tprevDebounce = options.debounceRendering;\r\n\t\t(prevDebounce || defer)(process);\r\n\t}\r\n}\r\n\r\n/** Flush the render queue by rerendering all queued components */\r\nfunction process() {\r\n\tlet queue;\r\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\r\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\t\trerenderQueue = [];\r\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\r\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\r\n\t\tqueue.some(c => {\r\n\t\t\tif (c._dirty) renderComponent(c);\r\n\t\t});\r\n\t}\r\n}\r\nprocess._rerenderCount = 0;\r\n", "import { enqueueRender } from './component';\r\n\r\nexport let i = 0;\r\n\r\nexport function createContext(defaultValue, contextId) {\r\n\tcontextId = '__cC' + i++;\r\n\r\n\tconst context = {\r\n\t\t_id: contextId,\r\n\t\t_defaultValue: defaultValue,\r\n\t\t/** @type {import('./internal').FunctionComponent} */\r\n\t\tConsumer(props, contextValue) {\r\n\t\t\t// return props.children(\r\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\r\n\t\t\t// );\r\n\t\t\treturn props.children(contextValue);\r\n\t\t},\r\n\t\t/** @type {import('./internal').FunctionComponent} */\r\n\t\tProvider(props) {\r\n\t\t\tif (!this.getChildContext) {\r\n\t\t\t\tlet subs = [];\r\n\t\t\t\tlet ctx = {};\r\n\t\t\t\tctx[contextId] = this;\r\n\r\n\t\t\t\tthis.getChildContext = () => ctx;\r\n\r\n\t\t\t\tthis.shouldComponentUpdate = function(_props) {\r\n\t\t\t\t\tif (this.props.value !== _props.value) {\r\n\t\t\t\t\t\t// I think the forced value propagation here was only needed when `options.debounceRendering` was being bypassed:\r\n\t\t\t\t\t\t// https://github.com/preactjs/preact/commit/4d339fb803bea09e9f198abf38ca1bf8ea4b7771#diff-54682ce380935a717e41b8bfc54737f6R358\r\n\t\t\t\t\t\t// In those cases though, even with the value corrected, we're double-rendering all nodes.\r\n\t\t\t\t\t\t// It might be better to just tell folks not to use force-sync mode.\r\n\t\t\t\t\t\t// Currently, using `useContext()` in a class component will overwrite its `this.context` value.\r\n\t\t\t\t\t\t// subs.some(c => {\r\n\t\t\t\t\t\t// \tc.context = _props.value;\r\n\t\t\t\t\t\t// \tenqueueRender(c);\r\n\t\t\t\t\t\t// });\r\n\r\n\t\t\t\t\t\t// subs.some(c => {\r\n\t\t\t\t\t\t// \tc.context[contextId] = _props.value;\r\n\t\t\t\t\t\t// \tenqueueRender(c);\r\n\t\t\t\t\t\t// });\r\n\t\t\t\t\t\tsubs.some(enqueueRender);\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\tthis.sub = c => {\r\n\t\t\t\t\tsubs.push(c);\r\n\t\t\t\t\tlet old = c.componentWillUnmount;\r\n\t\t\t\t\tc.componentWillUnmount = () => {\r\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\r\n\t\t\t\t\t\tif (old) old.call(c);\r\n\t\t\t\t\t};\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\treturn props.children;\r\n\t\t}\r\n\t};\r\n\r\n\t// Devtools needs access to the context object when it\r\n\t// encounters a Provider. This is necessary to support\r\n\t// setting `displayName` on the context object instead\r\n\t// of on the component itself. See:\r\n\t// https://reactjs.org/docs/context.html#contextdisplayname\r\n\r\n\treturn (context.Provider._contextRef = context.Consumer.contextType = context);\r\n}\r\n", "import { diff, unmount, applyRef } from './index';\r\nimport { createVNode, Fragment } from '../create-element';\r\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\r\nimport { getDomSibling } from '../component';\r\n\r\n/**\r\n * Diff the children of a virtual node\r\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\r\n * children are being diffed\r\n * @param {import('../internal').ComponentChildren[]} renderResult\r\n * @param {import('../internal').VNode} newParentVNode The new virtual\r\n * node whose children should be diff'ed against oldParentVNode\r\n * @param {import('../internal').VNode} oldParentVNode The old virtual\r\n * node whose children should be diff'ed against newParentVNode\r\n * @param {object} globalContext The current context object - modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n */\r\nexport function diffChildren(\r\n\tparentDom,\r\n\trenderResult,\r\n\tnewParentVNode,\r\n\toldParentVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\r\n\r\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\r\n\t// as EMPTY_OBJ._children should be `undefined`.\r\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\r\n\r\n\tlet oldChildrenLength = oldChildren.length;\r\n\r\n\tnewParentVNode._children = [];\r\n\tfor (i = 0; i < renderResult.length; i++) {\r\n\t\tchildVNode = renderResult[i];\r\n\r\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\r\n\t\t\tchildVNode = newParentVNode._children[i] = null;\r\n\t\t}\r\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\r\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\r\n\t\t// it's own DOM & etc. pointers\r\n\t\telse if (\r\n\t\t\ttypeof childVNode == 'string' ||\r\n\t\t\ttypeof childVNode == 'number' ||\r\n\t\t\t// eslint-disable-next-line valid-typeof\r\n\t\t\ttypeof childVNode == 'bigint'\r\n\t\t) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode\r\n\t\t\t);\r\n\t\t} else if (Array.isArray(childVNode)) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tFragment,\r\n\t\t\t\t{ children: childVNode },\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull\r\n\t\t\t);\r\n\t\t} else if (childVNode._depth > 0) {\r\n\t\t\t// VNode is already in use, clone it. This can happen in the following\r\n\t\t\t// scenario:\r\n\t\t\t//   const reuse = <div />\r\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tchildVNode.type,\r\n\t\t\t\tchildVNode.props,\r\n\t\t\t\tchildVNode.key,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode._original\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\r\n\t\t}\r\n\r\n\t\t// Terser removes the `continue` here and wraps the loop body\r\n\t\t// in a `if (childVNode) { ... } condition\r\n\t\tif (childVNode == null) {\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tchildVNode._parent = newParentVNode;\r\n\t\tchildVNode._depth = newParentVNode._depth + 1;\r\n\r\n\t\t// Check if we find a corresponding element in oldChildren.\r\n\t\t// If found, delete the array item by setting to `undefined`.\r\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\r\n\t\t// (holes).\r\n\t\toldVNode = oldChildren[i];\r\n\r\n\t\tif (\r\n\t\t\toldVNode === null ||\r\n\t\t\t(oldVNode &&\r\n\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\tchildVNode.type === oldVNode.type)\r\n\t\t) {\r\n\t\t\toldChildren[i] = undefined;\r\n\t\t} else {\r\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\r\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\r\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\r\n\t\t\t\toldVNode = oldChildren[j];\r\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\r\n\t\t\t\t// We always match by type (in either case).\r\n\t\t\t\tif (\r\n\t\t\t\t\toldVNode &&\r\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\t\tchildVNode.type === oldVNode.type\r\n\t\t\t\t) {\r\n\t\t\t\t\toldChildren[j] = undefined;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\toldVNode = null;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\toldVNode = oldVNode || EMPTY_OBJ;\r\n\r\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tchildVNode,\r\n\t\t\toldVNode,\r\n\t\t\tglobalContext,\r\n\t\t\tisSvg,\r\n\t\t\texcessDomChildren,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom,\r\n\t\t\tisHydrating\r\n\t\t);\r\n\r\n\t\tnewDom = childVNode._dom;\r\n\r\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\r\n\t\t\tif (!refs) refs = [];\r\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\r\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\r\n\t\t}\r\n\r\n\t\tif (newDom != null) {\r\n\t\t\tif (firstChildDom == null) {\r\n\t\t\t\tfirstChildDom = newDom;\r\n\t\t\t}\r\n\r\n\t\t\tif (\r\n\t\t\t\ttypeof childVNode.type == 'function' &&\r\n\t\t\t\tchildVNode._children === oldVNode._children\r\n\t\t\t) {\r\n\t\t\t\tchildVNode._nextDom = oldDom = reorderChildren(\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldDom,\r\n\t\t\t\t\tparentDom\r\n\t\t\t\t);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldVNode,\r\n\t\t\t\t\toldChildren,\r\n\t\t\t\t\tnewDom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tif (typeof newParentVNode.type == 'function') {\r\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\r\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\r\n\t\t\t\t//\r\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\r\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\r\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\r\n\t\t\t\t// node's nextSibling.\r\n\t\t\t\tnewParentVNode._nextDom = oldDom;\r\n\t\t\t}\r\n\t\t} else if (\r\n\t\t\toldDom &&\r\n\t\t\toldVNode._dom == oldDom &&\r\n\t\t\toldDom.parentNode != parentDom\r\n\t\t) {\r\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\r\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\r\n\t\t\toldDom = getDomSibling(oldVNode);\r\n\t\t}\r\n\t}\r\n\r\n\tnewParentVNode._dom = firstChildDom;\r\n\r\n\t// Remove remaining oldChildren if there are any.\r\n\tfor (i = oldChildrenLength; i--; ) {\r\n\t\tif (oldChildren[i] != null) {\r\n\t\t\tif (\r\n\t\t\t\ttypeof newParentVNode.type == 'function' &&\r\n\t\t\t\toldChildren[i]._dom != null &&\r\n\t\t\t\toldChildren[i]._dom == newParentVNode._nextDom\r\n\t\t\t) {\r\n\t\t\t\t// If the newParentVNode.__nextDom points to a dom node that is about to\r\n\t\t\t\t// be unmounted, then get the next sibling of that vnode and set\r\n\t\t\t\t// _nextDom to it\r\n\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldParentVNode, i + 1);\r\n\t\t\t}\r\n\r\n\t\t\tunmount(oldChildren[i], oldChildren[i]);\r\n\t\t}\r\n\t}\r\n\r\n\t// Set refs only after unmount\r\n\tif (refs) {\r\n\t\tfor (i = 0; i < refs.length; i++) {\r\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction reorderChildren(childVNode, oldDom, parentDom) {\r\n\t// Note: VNodes in nested suspended trees may be missing _children.\r\n\tlet c = childVNode._children;\r\n\tlet tmp = 0;\r\n\tfor (; c && tmp < c.length; tmp++) {\r\n\t\tlet vnode = c[tmp];\r\n\t\tif (vnode) {\r\n\t\t\t// We typically enter this code path on sCU bailout, where we copy\r\n\t\t\t// oldVNode._children to newVNode._children. If that is the case, we need\r\n\t\t\t// to update the old children's _parent pointer to point to the newVNode\r\n\t\t\t// (childVNode here).\r\n\t\t\tvnode._parent = childVNode;\r\n\r\n\t\t\tif (typeof vnode.type == 'function') {\r\n\t\t\t\toldDom = reorderChildren(vnode, oldDom, parentDom);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tc,\r\n\t\t\t\t\tvnode._dom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n\r\n/**\r\n * Flatten and loop through the children of a virtual node\r\n * @param {import('../index').ComponentChildren} children The unflattened\r\n * children of a virtual node\r\n * @returns {import('../internal').VNode[]}\r\n */\r\nexport function toChildArray(children, out) {\r\n\tout = out || [];\r\n\tif (children == null || typeof children == 'boolean') {\r\n\t} else if (Array.isArray(children)) {\r\n\t\tchildren.some(child => {\r\n\t\t\ttoChildArray(child, out);\r\n\t\t});\r\n\t} else {\r\n\t\tout.push(children);\r\n\t}\r\n\treturn out;\r\n}\r\n\r\nfunction placeChild(\r\n\tparentDom,\r\n\tchildVNode,\r\n\toldVNode,\r\n\toldChildren,\r\n\tnewDom,\r\n\toldDom\r\n) {\r\n\tlet nextDom;\r\n\tif (childVNode._nextDom !== undefined) {\r\n\t\t// Only Fragments or components that return Fragment like VNodes will\r\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\r\n\t\t// of last DOM child of this child VNode\r\n\t\tnextDom = childVNode._nextDom;\r\n\r\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\r\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\r\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\r\n\t\t// can clean up the property\r\n\t\tchildVNode._nextDom = undefined;\r\n\t} else if (\r\n\t\toldVNode == null ||\r\n\t\tnewDom != oldDom ||\r\n\t\tnewDom.parentNode == null\r\n\t) {\r\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\r\n\t\t\tparentDom.appendChild(newDom);\r\n\t\t\tnextDom = null;\r\n\t\t} else {\r\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\r\n\t\t\tfor (\r\n\t\t\t\tlet sibDom = oldDom, j = 0;\r\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\r\n\t\t\t\tj += 2\r\n\t\t\t) {\r\n\t\t\t\tif (sibDom == newDom) {\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tparentDom.insertBefore(newDom, oldDom);\r\n\t\t\tnextDom = oldDom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\r\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\r\n\t// See more detail in create-element.js:createVNode\r\n\tif (nextDom !== undefined) {\r\n\t\toldDom = nextDom;\r\n\t} else {\r\n\t\toldDom = newDom.nextSibling;\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n", "import { IS_NON_DIMENSIONAL } from '../constants';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff the old and new properties of a VNode and apply changes to the DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to apply\r\n * changes to\r\n * @param {object} newProps The new props\r\n * @param {object} oldProps The old props\r\n * @param {boolean} isSvg Whether or not this node is an SVG node\r\n * @param {boolean} hydrate Whether or not we are in hydration mode\r\n */\r\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\r\n\tlet i;\r\n\r\n\tfor (i in oldProps) {\r\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\r\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n\r\n\tfor (i in newProps) {\r\n\t\tif (\r\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\r\n\t\t\ti !== 'children' &&\r\n\t\t\ti !== 'key' &&\r\n\t\t\ti !== 'value' &&\r\n\t\t\ti !== 'checked' &&\r\n\t\t\toldProps[i] !== newProps[i]\r\n\t\t) {\r\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction setStyle(style, key, value) {\r\n\tif (key[0] === '-') {\r\n\t\tstyle.setProperty(key, value);\r\n\t} else if (value == null) {\r\n\t\tstyle[key] = '';\r\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\r\n\t\tstyle[key] = value;\r\n\t} else {\r\n\t\tstyle[key] = value + 'px';\r\n\t}\r\n}\r\n\r\n/**\r\n * Set a property value on a DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to modify\r\n * @param {string} name The name of the property to set\r\n * @param {*} value The value to set the property to\r\n * @param {*} oldValue The old value the property had\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\r\n */\r\nexport function setProperty(dom, name, value, oldValue, isSvg) {\r\n\tlet useCapture;\r\n\r\n\to: if (name === 'style') {\r\n\t\tif (typeof value == 'string') {\r\n\t\t\tdom.style.cssText = value;\r\n\t\t} else {\r\n\t\t\tif (typeof oldValue == 'string') {\r\n\t\t\t\tdom.style.cssText = oldValue = '';\r\n\t\t\t}\r\n\r\n\t\t\tif (oldValue) {\r\n\t\t\t\tfor (name in oldValue) {\r\n\t\t\t\t\tif (!(value && name in value)) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (value) {\r\n\t\t\t\tfor (name in value) {\r\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\r\n\telse if (name[0] === 'o' && name[1] === 'n') {\r\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\r\n\r\n\t\t// Infer correct casing for DOM built-in events:\r\n\t\tif (name.toLowerCase() in dom) name = name.toLowerCase().slice(2);\r\n\t\telse name = name.slice(2);\r\n\r\n\t\tif (!dom._listeners) dom._listeners = {};\r\n\t\tdom._listeners[name + useCapture] = value;\r\n\r\n\t\tif (value) {\r\n\t\t\tif (!oldValue) {\r\n\t\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\t\tdom.addEventListener(name, handler, useCapture);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\tdom.removeEventListener(name, handler, useCapture);\r\n\t\t}\r\n\t} else if (name !== 'dangerouslySetInnerHTML') {\r\n\t\tif (isSvg) {\r\n\t\t\t// Normalize incorrect prop usage for SVG:\r\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\r\n\t\t\t// - className --> class\r\n\t\t\tname = name.replace(/xlink[H:h]/, 'h').replace(/sName$/, 's');\r\n\t\t} else if (\r\n\t\t\tname !== 'href' &&\r\n\t\t\tname !== 'list' &&\r\n\t\t\tname !== 'form' &&\r\n\t\t\t// Default value in browsers is `-1` and an empty string is\r\n\t\t\t// cast to `0` instead\r\n\t\t\tname !== 'tabIndex' &&\r\n\t\t\tname !== 'download' &&\r\n\t\t\tname in dom\r\n\t\t) {\r\n\t\t\ttry {\r\n\t\t\t\tdom[name] = value == null ? '' : value;\r\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\r\n\t\t\t\tbreak o;\r\n\t\t\t} catch (e) {}\r\n\t\t}\r\n\r\n\t\t// ARIA-attributes have a different notion of boolean values.\r\n\t\t// The value `false` is different from the attribute not\r\n\t\t// existing on the DOM, so we can't remove it. For non-boolean\r\n\t\t// ARIA-attributes we could treat false as a removal, but the\r\n\t\t// amount of exceptions would cost us too many bytes. On top of\r\n\t\t// that other VDOM frameworks also always stringify `false`.\r\n\r\n\t\tif (typeof value === 'function') {\r\n\t\t\t// never serialize functions as attribute values\r\n\t\t} else if (\r\n\t\t\tvalue != null &&\r\n\t\t\t(value !== false || (name[0] === 'a' && name[1] === 'r'))\r\n\t\t) {\r\n\t\t\tdom.setAttribute(name, value);\r\n\t\t} else {\r\n\t\t\tdom.removeAttribute(name);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Proxy an event to hooked event handlers\r\n * @param {Event} e The event object from the browser\r\n * @private\r\n */\r\nfunction eventProxy(e) {\r\n\tthis._listeners[e.type + false](options.event ? options.event(e) : e);\r\n}\r\n\r\nfunction eventProxyCapture(e) {\r\n\tthis._listeners[e.type + true](options.event ? options.event(e) : e);\r\n}\r\n", "import { EMPTY_OBJ } from '../constants';\r\nimport { Component, getDomSibling } from '../component';\r\nimport { Fragment } from '../create-element';\r\nimport { diffChildren } from './children';\r\nimport { diffProps, setProperty } from './props';\r\nimport { assign, removeNode, slice } from '../util';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff two virtual nodes and apply proper changes to the DOM\r\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object. Modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this element is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} [isHydrating] Whether or not we are in hydration\r\n */\r\nexport function diff(\r\n\tparentDom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet tmp,\r\n\t\tnewType = newVNode.type;\r\n\r\n\t// When passing through createElement it assigns the object\r\n\t// constructor as undefined. This to prevent JSON-injection.\r\n\tif (newVNode.constructor !== undefined) return null;\r\n\r\n\t// If the previous diff bailed out, resume creating/hydrating.\r\n\tif (oldVNode._hydrating != null) {\r\n\t\tisHydrating = oldVNode._hydrating;\r\n\t\toldDom = newVNode._dom = oldVNode._dom;\r\n\t\t// if we resume, we want the tree to be \"unlocked\"\r\n\t\tnewVNode._hydrating = null;\r\n\t\texcessDomChildren = [oldDom];\r\n\t}\r\n\r\n\tif ((tmp = options._diff)) tmp(newVNode);\r\n\r\n\ttry {\r\n\t\touter: if (typeof newType == 'function') {\r\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\r\n\t\t\tlet newProps = newVNode.props;\r\n\r\n\t\t\t// Necessary for createContext api. Setting this property will pass\r\n\t\t\t// the context value as `this.context` just for this component.\r\n\t\t\ttmp = newType.contextType;\r\n\t\t\tlet provider = tmp && globalContext[tmp._id];\r\n\t\t\tlet componentContext = tmp\r\n\t\t\t\t? provider\r\n\t\t\t\t\t? provider.props.value\r\n\t\t\t\t\t: tmp._defaultValue\r\n\t\t\t\t: globalContext;\r\n\r\n\t\t\t// Get component and set it to `c`\r\n\t\t\tif (oldVNode._component) {\r\n\t\t\t\tc = newVNode._component = oldVNode._component;\r\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\r\n\t\t\t} else {\r\n\t\t\t\t// Instantiate the new component\r\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\r\n\t\t\t\t\t// @ts-ignore The check above verifies that newType is suppose to be constructed\r\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// @ts-ignore Trust me, Component implements the interface we want\r\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\r\n\t\t\t\t\tc.constructor = newType;\r\n\t\t\t\t\tc.render = doRender;\r\n\t\t\t\t}\r\n\t\t\t\tif (provider) provider.sub(c);\r\n\r\n\t\t\t\tc.props = newProps;\r\n\t\t\t\tif (!c.state) c.state = {};\r\n\t\t\t\tc.context = componentContext;\r\n\t\t\t\tc._globalContext = globalContext;\r\n\t\t\t\tisNew = c._dirty = true;\r\n\t\t\t\tc._renderCallbacks = [];\r\n\t\t\t}\r\n\r\n\t\t\t// Invoke getDerivedStateFromProps\r\n\t\t\tif (c._nextState == null) {\r\n\t\t\t\tc._nextState = c.state;\r\n\t\t\t}\r\n\t\t\tif (newType.getDerivedStateFromProps != null) {\r\n\t\t\t\tif (c._nextState == c.state) {\r\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tassign(\r\n\t\t\t\t\tc._nextState,\r\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\toldProps = c.props;\r\n\t\t\toldState = c.state;\r\n\r\n\t\t\t// Invoke pre-render lifecycle methods\r\n\t\t\tif (isNew) {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tc.componentWillMount != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillMount();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidMount != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tnewProps !== oldProps &&\r\n\t\t\t\t\tc.componentWillReceiveProps != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (\r\n\t\t\t\t\t(!c._force &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate(\r\n\t\t\t\t\t\t\tnewProps,\r\n\t\t\t\t\t\t\tc._nextState,\r\n\t\t\t\t\t\t\tcomponentContext\r\n\t\t\t\t\t\t) === false) ||\r\n\t\t\t\t\tnewVNode._original === oldVNode._original\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.props = newProps;\r\n\t\t\t\t\tc.state = c._nextState;\r\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\r\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\r\n\t\t\t\t\tc._vnode = newVNode;\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t\tnewVNode._children.forEach(vnode => {\r\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\t\t\tcommitQueue.push(c);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentWillUpdate != null) {\r\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidUpdate != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(() => {\r\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tc.context = componentContext;\r\n\t\t\tc.props = newProps;\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif ((tmp = options._render)) tmp(newVNode);\r\n\r\n\t\t\tc._dirty = false;\r\n\t\t\tc._vnode = newVNode;\r\n\t\t\tc._parentDom = parentDom;\r\n\r\n\t\t\ttmp = c.render(c.props, c.state, c.context);\r\n\r\n\t\t\t// Handle setState called in render, see #2553\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif (c.getChildContext != null) {\r\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\r\n\t\t\t}\r\n\r\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\r\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\r\n\t\t\t}\r\n\r\n\t\t\tlet isTopLevelFragment =\r\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\r\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\r\n\r\n\t\t\tdiffChildren(\r\n\t\t\t\tparentDom,\r\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\toldDom,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\tc.base = newVNode._dom;\r\n\r\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\r\n\t\t\tnewVNode._hydrating = null;\r\n\r\n\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\tcommitQueue.push(c);\r\n\t\t\t}\r\n\r\n\t\t\tif (clearProcessingException) {\r\n\t\t\t\tc._pendingError = c._processingException = null;\r\n\t\t\t}\r\n\r\n\t\t\tc._force = false;\r\n\t\t} else if (\r\n\t\t\texcessDomChildren == null &&\r\n\t\t\tnewVNode._original === oldVNode._original\r\n\t\t) {\r\n\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t} else {\r\n\t\t\tnewVNode._dom = diffElementNodes(\r\n\t\t\t\toldVNode._dom,\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\r\n\t} catch (e) {\r\n\t\tnewVNode._original = null;\r\n\t\t// if hydrating or creating initial tree, bailout preserves DOM:\r\n\t\tif (isHydrating || excessDomChildren != null) {\r\n\t\t\tnewVNode._dom = oldDom;\r\n\t\t\tnewVNode._hydrating = !!isHydrating;\r\n\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\r\n\t\t\t// ^ could possibly be simplified to:\r\n\t\t\t// excessDomChildren.length = 0;\r\n\t\t}\r\n\t\toptions._catchError(e, newVNode, oldVNode);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').VNode} root\r\n */\r\nexport function commitRoot(commitQueue, root) {\r\n\tif (options._commit) options._commit(root, commitQueue);\r\n\r\n\tcommitQueue.some(c => {\r\n\t\ttry {\r\n\t\t\t// @ts-ignore Reuse the commitQueue variable here so the type changes\r\n\t\t\tcommitQueue = c._renderCallbacks;\r\n\t\t\tc._renderCallbacks = [];\r\n\t\t\tcommitQueue.some(cb => {\r\n\t\t\t\t// @ts-ignore See above ts-ignore on commitQueue\r\n\t\t\t\tcb.call(c);\r\n\t\t\t});\r\n\t\t} catch (e) {\r\n\t\t\toptions._catchError(e, c._vnode);\r\n\t\t}\r\n\t});\r\n}\r\n\r\n/**\r\n * Diff two virtual nodes representing DOM element\r\n * @param {import('../internal').PreactElement} dom The DOM element representing\r\n * the virtual nodes being diffed\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {*} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n * @returns {import('../internal').PreactElement}\r\n */\r\nfunction diffElementNodes(\r\n\tdom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\tisHydrating\r\n) {\r\n\tlet oldProps = oldVNode.props;\r\n\tlet newProps = newVNode.props;\r\n\tlet nodeType = newVNode.type;\r\n\tlet i = 0;\r\n\r\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\r\n\tif (nodeType === 'svg') isSvg = true;\r\n\r\n\tif (excessDomChildren != null) {\r\n\t\tfor (; i < excessDomChildren.length; i++) {\r\n\t\t\tconst child = excessDomChildren[i];\r\n\r\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\r\n\t\t\t// argument matches an element in excessDomChildren, remove it from\r\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\r\n\t\t\tif (\r\n\t\t\t\tchild &&\r\n\t\t\t\t'setAttribute' in child === !!nodeType &&\r\n\t\t\t\t(nodeType ? child.localName === nodeType : child.nodeType === 3)\r\n\t\t\t) {\r\n\t\t\t\tdom = child;\r\n\t\t\t\texcessDomChildren[i] = null;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (dom == null) {\r\n\t\tif (nodeType === null) {\r\n\t\t\t// @ts-ignore createTextNode returns Text, we expect PreactElement\r\n\t\t\treturn document.createTextNode(newProps);\r\n\t\t}\r\n\r\n\t\tif (isSvg) {\r\n\t\t\tdom = document.createElementNS(\r\n\t\t\t\t'http://www.w3.org/2000/svg',\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tdom = document.createElement(\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType,\r\n\t\t\t\tnewProps.is && newProps\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// we created a new parent, so none of the previously attached children can be reused:\r\n\t\texcessDomChildren = null;\r\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\r\n\t\tisHydrating = false;\r\n\t}\r\n\r\n\tif (nodeType === null) {\r\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\r\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\r\n\t\t\tdom.data = newProps;\r\n\t\t}\r\n\t} else {\r\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\r\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\r\n\r\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\r\n\r\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\r\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\r\n\r\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\r\n\t\t// @TODO we should warn in debug mode when props don't match here.\r\n\t\tif (!isHydrating) {\r\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\r\n\t\t\t// we should read the existing DOM attributes to diff them\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\toldProps = {};\r\n\t\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\r\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (newHtml || oldHtml) {\r\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\r\n\t\t\t\tif (\r\n\t\t\t\t\t!newHtml ||\r\n\t\t\t\t\t((!oldHtml || newHtml.__html != oldHtml.__html) &&\r\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML)\r\n\t\t\t\t) {\r\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\r\n\r\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\r\n\t\tif (newHtml) {\r\n\t\t\tnewVNode._children = [];\r\n\t\t} else {\r\n\t\t\ti = newVNode.props.children;\r\n\t\t\tdiffChildren(\r\n\t\t\t\tdom,\r\n\t\t\t\tArray.isArray(i) ? i : [i],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg && nodeType !== 'foreignObject',\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\texcessDomChildren\r\n\t\t\t\t\t? excessDomChildren[0]\r\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\t// Remove children that are not part of any vnode.\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\r\n\t\t\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// (as above, don't diff props during hydration)\r\n\t\tif (!isHydrating) {\r\n\t\t\tif (\r\n\t\t\t\t'value' in newProps &&\r\n\t\t\t\t(i = newProps.value) !== undefined &&\r\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\r\n\t\t\t\t// despite the attribute not being present. When the attribute\r\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\r\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\r\n\t\t\t\t(i !== oldProps.value ||\r\n\t\t\t\t\ti !== dom.value ||\r\n\t\t\t\t\t(nodeType === 'progress' && !i))\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\r\n\t\t\t}\r\n\t\t\tif (\r\n\t\t\t\t'checked' in newProps &&\r\n\t\t\t\t(i = newProps.checked) !== undefined &&\r\n\t\t\t\ti !== dom.checked\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn dom;\r\n}\r\n\r\n/**\r\n * Invoke or update a ref, depending on whether it is a function or object ref.\r\n * @param {object|function} ref\r\n * @param {any} value\r\n * @param {import('../internal').VNode} vnode\r\n */\r\nexport function applyRef(ref, value, vnode) {\r\n\ttry {\r\n\t\tif (typeof ref == 'function') ref(value);\r\n\t\telse ref.current = value;\r\n\t} catch (e) {\r\n\t\toptions._catchError(e, vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * Unmount a virtual node from the tree and apply DOM changes\r\n * @param {import('../internal').VNode} vnode The virtual node to unmount\r\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\r\n * initiated the unmount\r\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\r\n * current element is already detached from the DOM.\r\n */\r\nexport function unmount(vnode, parentVNode, skipRemove) {\r\n\tlet r;\r\n\tif (options.unmount) options.unmount(vnode);\r\n\r\n\tif ((r = vnode.ref)) {\r\n\t\tif (!r.current || r.current === vnode._dom) applyRef(r, null, parentVNode);\r\n\t}\r\n\r\n\tif ((r = vnode._component) != null) {\r\n\t\tif (r.componentWillUnmount) {\r\n\t\t\ttry {\r\n\t\t\t\tr.componentWillUnmount();\r\n\t\t\t} catch (e) {\r\n\t\t\t\toptions._catchError(e, parentVNode);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tr.base = r._parentDom = null;\r\n\t}\r\n\r\n\tif ((r = vnode._children)) {\r\n\t\tfor (let i = 0; i < r.length; i++) {\r\n\t\t\tif (r[i]) {\r\n\t\t\t\tunmount(r[i], parentVNode, typeof vnode.type != 'function');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (!skipRemove && vnode._dom != null) removeNode(vnode._dom);\r\n\r\n\t// Must be set to `undefined` to properly clean up `_nextDom`\r\n\t// for which `null` is a valid value. See comment in `create-element.js`\r\n\tvnode._dom = vnode._nextDom = undefined;\r\n}\r\n\r\n/** The `.render()` method for a PFC backing instance. */\r\nfunction doRender(props, state, context) {\r\n\treturn this.constructor(props, context);\r\n}\r\n", "import { EMPTY_OBJ } from './constants';\r\nimport { commitRoot, diff } from './diff/index';\r\nimport { createElement, Fragment } from './create-element';\r\nimport options from './options';\r\nimport { slice } from './util';\r\n\r\n/**\r\n * Render a Preact virtual node into a DOM element\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * render into\r\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\r\n * existing DOM tree rooted at `replaceNode`\r\n */\r\nexport function render(vnode, parentDom, replaceNode) {\r\n\tif (options._root) options._root(vnode, parentDom);\r\n\r\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\r\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\r\n\t// element..\r\n\tlet isHydrating = typeof replaceNode === 'function';\r\n\r\n\t// To be able to support calling `render()` multiple times on the same\r\n\t// DOM node, we need to obtain a reference to the previous tree. We do\r\n\t// this by assigning a new `_children` property to DOM nodes which points\r\n\t// to the last rendered tree. By default this property is not present, which\r\n\t// means that we are mounting a new tree for the first time.\r\n\tlet oldVNode = isHydrating\r\n\t\t? null\r\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\r\n\r\n\tvnode = (\r\n\t\t(!isHydrating && replaceNode) ||\r\n\t\tparentDom\r\n\t)._children = createElement(Fragment, null, [vnode]);\r\n\r\n\t// List of effects that need to be called after diffing.\r\n\tlet commitQueue = [];\r\n\tdiff(\r\n\t\tparentDom,\r\n\t\t// Determine the new vnode tree and store it on the DOM element on\r\n\t\t// our custom `_children` property.\r\n\t\tvnode,\r\n\t\toldVNode || EMPTY_OBJ,\r\n\t\tEMPTY_OBJ,\r\n\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? [replaceNode]\r\n\t\t\t: oldVNode\r\n\t\t\t? null\r\n\t\t\t: parentDom.firstChild\r\n\t\t\t? slice.call(parentDom.childNodes)\r\n\t\t\t: null,\r\n\t\tcommitQueue,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? replaceNode\r\n\t\t\t: oldVNode\r\n\t\t\t? oldVNode._dom\r\n\t\t\t: parentDom.firstChild,\r\n\t\tisHydrating\r\n\t);\r\n\r\n\t// Flush all queued effects\r\n\tcommitRoot(commitQueue, vnode);\r\n}\r\n\r\n/**\r\n * Update an existing DOM element with data from a Preact virtual node\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * update\r\n */\r\nexport function hydrate(vnode, parentDom) {\r\n\trender(vnode, parentDom, hydrate);\r\n}\r\n", "import { assign, slice } from './util';\r\nimport { createVNode } from './create-element';\r\n\r\n/**\r\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\r\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\r\n * @param {object} props Attributes/props to add when cloning\r\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used as replacement children.\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function cloneElement(vnode, props, children) {\r\n\tlet normalizedProps = assign({}, vnode.props),\r\n\t\tkey,\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'key') key = props[i];\r\n\t\telse if (i == 'ref') ref = props[i];\r\n\t\telse normalizedProps[i] = props[i];\r\n\t}\r\n\r\n\tif (arguments.length > 2) {\r\n\t\tnormalizedProps.children =\r\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\r\n\t}\r\n\r\n\treturn createVNode(\r\n\t\tvnode.type,\r\n\t\tnormalizedProps,\r\n\t\tkey || vnode.key,\r\n\t\tref || vnode.ref,\r\n\t\tnull\r\n\t);\r\n}\r\n", "/**\r\n * Find the closest error boundary to a thrown error and call it\r\n * @param {object} error The thrown value\r\n * @param {import('../internal').VNode} vnode The vnode that threw\r\n * the error that was caught (except for unmounting when this parameter\r\n * is the highest parent that was being unmounted)\r\n */\r\nexport function _catchError(error, vnode) {\r\n\t/** @type {import('../internal').Component} */\r\n\tlet component, ctor, handled;\r\n\r\n\tfor (; (vnode = vnode._parent); ) {\r\n\t\tif ((component = vnode._component) && !component._processingException) {\r\n\t\t\ttry {\r\n\t\t\t\tctor = component.constructor;\r\n\r\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\r\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (component.componentDidCatch != null) {\r\n\t\t\t\t\tcomponent.componentDidCatch(error);\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\r\n\t\t\t\tif (handled) {\r\n\t\t\t\t\treturn (component._pendingError = component);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\terror = e;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tthrow error;\r\n}\r\n", "import { SearchIndex } from './helpers'\n\nexport function deepEqual(a: any, b: any): boolean {\n  return (\n    Array.isArray(a) &&\n    Array.isArray(b) &&\n    a.length === b.length &&\n    a.every((val, index) => val == b[index])\n  )\n}\n\nexport async function sleep(frames = 1) {\n  for (let _ in [...Array(frames).keys()]) {\n    await new Promise(requestAnimationFrame)\n  }\n}\n\nexport function getEmojiData(emoji, { skinIndex = 0 } = {}) {\n  const skin =\n    emoji.skins[skinIndex] ||\n    (() => {\n      skinIndex = 0\n      return emoji.skins[skinIndex]\n    })()\n\n  const emojiData: any = {\n    id: emoji.id,\n    name: emoji.name,\n    native: skin.native,\n    unified: skin.unified,\n    keywords: emoji.keywords,\n    shortcodes: skin.shortcodes || emoji.shortcodes,\n  }\n\n  if (emoji.skins.length > 1) {\n    emojiData.skin = skinIndex + 1\n  }\n\n  if (skin.src) {\n    emojiData.src = skin.src\n  }\n\n  if (emoji.aliases && emoji.aliases.length) {\n    emojiData.aliases = emoji.aliases\n  }\n\n  if (emoji.emoticons && emoji.emoticons.length) {\n    emojiData.emoticons = emoji.emoticons\n  }\n\n  return emojiData\n}\n\nexport async function getEmojiDataFromNative(nativeString) {\n  const results = await SearchIndex.search(nativeString, {\n    maxResults: 1,\n    caller: 'getEmojiDataFromNative',\n  })\n  if (!results || !results.length) return null\n\n  const emoji = results[0]\n  let skinIndex = 0\n\n  for (let skin of emoji.skins) {\n    if (skin.native == nativeString) {\n      break\n    }\n\n    skinIndex++\n  }\n\n  return getEmojiData(emoji, { skinIndex })\n}\n", "export { default as Store } from './store'\n\nexport { default as NativeSupport } from './native-support'\nexport { default as FrequentlyUsed } from './frequently-used'\nexport { default as SearchIndex } from './search-index'\n\nexport const SafeFlags = [\n  'checkered_flag',\n  'crossed_flags',\n  'pirate_flag',\n  'rainbow-flag',\n  'transgender_flag',\n  'triangular_flag_on_post',\n  'waving_black_flag',\n  'waving_white_flag',\n]\n", "function set(key: string, value: string) {\n  try {\n    window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value)\n  } catch (error) {}\n}\n\nfunction get(key: string): any {\n  try {\n    const value = window.localStorage[`emoji-mart.${key}`]\n\n    if (value) {\n      return JSON.parse(value)\n    }\n  } catch (error) {}\n}\n\nexport default { set, get }\n", "const CACHE = new Map()\nconst VERSIONS = [\n  { v: 15, emoji: '🫨' },\n  { v: 14, emoji: '🫠' },\n  { v: 13.1, emoji: '😶‍🌫️' },\n  { v: 13, emoji: '🥸' },\n  { v: 12.1, emoji: '🧑‍🦰' },\n  { v: 12, emoji: '🥱' },\n  { v: 11, emoji: '🥰' },\n  { v: 5, emoji: '🤩' },\n  { v: 4, emoji: '👱‍♀️' },\n  { v: 3, emoji: '🤣' },\n  { v: 2, emoji: '👋🏻' },\n  { v: 1, emoji: '🙃' },\n]\n\nfunction latestVersion() {\n  for (const { v, emoji } of VERSIONS) {\n    if (isSupported(emoji)) {\n      return v\n    }\n  }\n}\n\nfunction noCountryFlags() {\n  if (isSupported('🇨🇦')) {\n    return false\n  }\n\n  return true\n}\n\nfunction isSupported(emoji) {\n  if (CACHE.has(emoji)) {\n    return CACHE.get(emoji)\n  }\n\n  const supported = isEmojiSupported(emoji)\n  CACHE.set(emoji, supported)\n\n  return supported\n}\n\n// https://github.com/koala-interactive/is-emoji-supported\nconst isEmojiSupported = (() => {\n  let ctx = null\n  try {\n    if (!navigator.userAgent.includes('jsdom')) {\n      ctx = document\n        .createElement('canvas')\n        .getContext('2d', { willReadFrequently: true })\n    }\n  } catch {}\n\n  // Not in browser env\n  if (!ctx) {\n    return () => false\n  }\n\n  const CANVAS_HEIGHT = 25\n  const CANVAS_WIDTH = 20\n  const textSize = Math.floor(CANVAS_HEIGHT / 2)\n\n  // Initialize convas context\n  ctx.font = textSize + 'px Arial, Sans-Serif'\n  ctx.textBaseline = 'top'\n  ctx.canvas.width = CANVAS_WIDTH * 2\n  ctx.canvas.height = CANVAS_HEIGHT\n\n  return (unicode) => {\n    ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT)\n\n    // Draw in red on the left\n    ctx.fillStyle = '#FF0000'\n    ctx.fillText(unicode, 0, 22)\n\n    // Draw in blue on right\n    ctx.fillStyle = '#0000FF'\n    ctx.fillText(unicode, CANVAS_WIDTH, 22)\n\n    const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data\n    const count = a.length\n    let i = 0\n\n    // Search the first visible pixel\n    for (; i < count && !a[i + 3]; i += 4);\n\n    // No visible pixel\n    if (i >= count) {\n      return false\n    }\n\n    // Emoji has immutable color, so we check the color of the emoji in two different colors\n    // the result show be the same.\n    const x = CANVAS_WIDTH + ((i / 4) % CANVAS_WIDTH)\n    const y = Math.floor(i / 4 / CANVAS_WIDTH)\n    const b = ctx.getImageData(x, y, 1, 1).data\n\n    if (a[i] !== b[0] || a[i + 2] !== b[2]) {\n      return false\n    }\n\n    // Some emojis are a contraction of different ones, so if it's not\n    // supported, it will show multiple characters\n    if (ctx.measureText(unicode).width >= CANVAS_WIDTH) {\n      return false\n    }\n\n    // Supported\n    return true\n  }\n})()\n\nexport default { latestVersion, noCountryFlags }\n", "// @ts-nocheck\nimport { Store } from '../helpers'\n\nconst DEFAULTS = [\n  '+1',\n  'grinning',\n  'kissing_heart',\n  'heart_eyes',\n  'laughing',\n  'stuck_out_tongue_winking_eye',\n  'sweat_smile',\n  'joy',\n  'scream',\n  'disappointed',\n  'unamused',\n  'weary',\n  'sob',\n  'sunglasses',\n  'heart',\n]\n\nlet Index: any | null = null\n\nfunction add(emoji: { id: string }) {\n  Index || (Index = Store.get('frequently') || {})\n\n  const emojiId = emoji.id || emoji\n  if (!emojiId) return\n\n  Index[emojiId] || (Index[emojiId] = 0)\n  Index[emojiId] += 1\n\n  Store.set('last', emojiId)\n  Store.set('frequently', Index)\n}\n\nfunction get({ maxFrequentRows, perLine }) {\n  if (!maxFrequentRows) return []\n\n  Index || (Index = Store.get('frequently'))\n  let emojiIds = []\n\n  if (!Index) {\n    Index = {}\n\n    for (let i in DEFAULTS.slice(0, perLine)) {\n      const emojiId = DEFAULTS[i]\n\n      Index[emojiId] = perLine - i\n      emojiIds.push(emojiId)\n    }\n\n    return emojiIds\n  }\n\n  const max = maxFrequentRows * perLine\n  const last = Store.get('last')\n\n  for (let emojiId in Index) {\n    emojiIds.push(emojiId)\n  }\n\n  emojiIds.sort((a, b) => {\n    const aScore = Index[b]\n    const bScore = Index[a]\n\n    if (aScore == bScore) {\n      return a.localeCompare(b)\n    }\n\n    return aScore - bScore\n  })\n\n  if (emojiIds.length > max) {\n    const removedIds = emojiIds.slice(max)\n    emojiIds = emojiIds.slice(0, max)\n\n    for (let removedId of removedIds) {\n      if (removedId == last) continue\n      delete Index[removedId]\n    }\n\n    if (last && emojiIds.indexOf(last) == -1) {\n      delete Index[emojiIds[emojiIds.length - 1]]\n      emojiIds.splice(-1, 1, last)\n    }\n\n    Store.set('frequently', Index)\n  }\n\n  return emojiIds\n}\n\nexport default { add, get, DEFAULTS }\n", "// @ts-nocheck\nimport { init, Data } from '../config'\n\nconst SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/\nlet Pool = null\n\nfunction get(emojiId) {\n  if (emojiId.id) {\n    return emojiId\n  }\n\n  return (\n    Data.emojis[emojiId] ||\n    Data.emojis[Data.aliases[emojiId]] ||\n    Data.emojis[Data.natives[emojiId]]\n  )\n}\n\nfunction reset() {\n  Pool = null\n}\n\nasync function search(value, { maxResults, caller } = {}) {\n  if (!value || !value.trim().length) return null\n  maxResults || (maxResults = 90)\n\n  await init(null, { caller: caller || 'SearchIndex.search' })\n\n  const values = value\n    .toLowerCase()\n    .replace(/(\\w)-/, '$1 ')\n    .split(/[\\s|,]+/)\n    .filter((word, i, words) => {\n      return word.trim() && words.indexOf(word) == i\n    })\n\n  if (!values.length) return\n\n  let pool = Pool || (Pool = Object.values(Data.emojis))\n  let results, scores\n\n  for (const value of values) {\n    if (!pool.length) break\n\n    results = []\n    scores = {}\n\n    for (const emoji of pool) {\n      if (!emoji.search) continue\n      const score = emoji.search.indexOf(`,${value}`)\n      if (score == -1) continue\n\n      results.push(emoji)\n      scores[emoji.id] || (scores[emoji.id] = 0)\n      scores[emoji.id] += emoji.id == value ? 0 : score + 1\n    }\n\n    pool = results\n  }\n\n  if (results.length < 2) {\n    return results\n  }\n\n  results.sort((a, b) => {\n    const aScore = scores[a.id]\n    const bScore = scores[b.id]\n\n    if (aScore == bScore) {\n      return a.id.localeCompare(b.id)\n    }\n\n    return aScore - bScore\n  })\n\n  if (results.length > maxResults) {\n    results = results.slice(0, maxResults)\n  }\n\n  return results\n}\n\nexport default { search, get, reset, SHORTCODES_REGEX }\n", "// @ts-nocheck\nimport i18n_en from '@emoji-mart/data/i18n/en.json'\nimport PickerProps from './components/Picker/PickerProps'\nimport {\n  FrequentlyUsed,\n  NativeSupport,\n  SafeFlags,\n  SearchIndex,\n} from './helpers'\n\nexport let I18n = null\nexport let Data = null\n\nconst fetchCache = {}\nasync function fetchJSON(src) {\n  if (fetchCache[src]) {\n    return fetchCache[src]\n  }\n\n  const response = await fetch(src)\n  const json = await response.json()\n\n  fetchCache[src] = json\n  return json\n}\n\nlet promise: Promise<void> | null = null\nlet initiated = false\nlet initCallback = null\nlet initialized = false\n\nexport function init(options, { caller } = {}) {\n  promise ||\n    (promise = new Promise((resolve) => {\n      initCallback = resolve\n    }))\n\n  if (options) {\n    _init(options)\n  } else if (caller && !initialized) {\n    console.warn(\n      `\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`,\n    )\n  }\n\n  return promise\n}\n\nasync function _init(props) {\n  initialized = true\n\n  let { emojiVersion, set, locale } = props\n  emojiVersion || (emojiVersion = PickerProps.emojiVersion.value)\n  set || (set = PickerProps.set.value)\n  locale || (locale = PickerProps.locale.value)\n\n  if (!Data) {\n    Data =\n      (typeof props.data === 'function' ? await props.data() : props.data) ||\n      (await fetchJSON(\n        `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`,\n      ))\n\n    Data.emoticons = {}\n    Data.natives = {}\n\n    Data.categories.unshift({\n      id: 'frequent',\n      emojis: [],\n    })\n\n    for (const alias in Data.aliases) {\n      const emojiId = Data.aliases[alias]\n      const emoji = Data.emojis[emojiId]\n      if (!emoji) continue\n\n      emoji.aliases || (emoji.aliases = [])\n      emoji.aliases.push(alias)\n    }\n\n    Data.originalCategories = Data.categories\n  } else {\n    Data.categories = Data.categories.filter((c) => {\n      const isCustom = !!c.name\n      if (!isCustom) return true\n\n      return false\n    })\n  }\n\n  I18n =\n    (typeof props.i18n === 'function' ? await props.i18n() : props.i18n) ||\n    (locale == 'en'\n      ? i18n_en\n      : await fetchJSON(\n          `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`,\n        ))\n\n  if (props.custom) {\n    for (let i in props.custom) {\n      i = parseInt(i)\n      const category = props.custom[i]\n      const prevCategory = props.custom[i - 1]\n\n      if (!category.emojis || !category.emojis.length) continue\n\n      category.id || (category.id = `custom_${i + 1}`)\n      category.name || (category.name = I18n.categories.custom)\n\n      if (prevCategory && !category.icon) {\n        category.target = prevCategory.target || prevCategory\n      }\n\n      Data.categories.push(category)\n\n      for (const emoji of category.emojis) {\n        Data.emojis[emoji.id] = emoji\n      }\n    }\n  }\n\n  if (props.categories) {\n    Data.categories = Data.originalCategories\n      .filter((c) => {\n        return props.categories.indexOf(c.id) != -1\n      })\n      .sort((c1, c2) => {\n        const i1 = props.categories.indexOf(c1.id)\n        const i2 = props.categories.indexOf(c2.id)\n\n        return i1 - i2\n      })\n  }\n\n  let latestVersionSupport = null\n  let noCountryFlags = null\n  if (set == 'native') {\n    latestVersionSupport = NativeSupport.latestVersion()\n    noCountryFlags = props.noCountryFlags || NativeSupport.noCountryFlags()\n  }\n\n  let categoryIndex = Data.categories.length\n  let resetSearchIndex = false\n  while (categoryIndex--) {\n    const category = Data.categories[categoryIndex]\n\n    if (category.id == 'frequent') {\n      let { maxFrequentRows, perLine } = props\n\n      maxFrequentRows =\n        maxFrequentRows >= 0\n          ? maxFrequentRows\n          : PickerProps.maxFrequentRows.value\n      perLine || (perLine = PickerProps.perLine.value)\n\n      category.emojis = FrequentlyUsed.get({ maxFrequentRows, perLine })\n    }\n\n    if (!category.emojis || !category.emojis.length) {\n      Data.categories.splice(categoryIndex, 1)\n      continue\n    }\n\n    const { categoryIcons } = props\n    if (categoryIcons) {\n      const icon = categoryIcons[category.id]\n      if (icon && !category.icon) {\n        category.icon = icon\n      }\n    }\n\n    let emojiIndex = category.emojis.length\n    while (emojiIndex--) {\n      const emojiId = category.emojis[emojiIndex]\n      const emoji = emojiId.id ? emojiId : Data.emojis[emojiId]\n\n      const ignore = () => {\n        category.emojis.splice(emojiIndex, 1)\n      }\n\n      if (\n        !emoji ||\n        (props.exceptEmojis && props.exceptEmojis.includes(emoji.id))\n      ) {\n        ignore()\n        continue\n      }\n\n      if (latestVersionSupport && emoji.version > latestVersionSupport) {\n        ignore()\n        continue\n      }\n\n      if (noCountryFlags && category.id == 'flags') {\n        if (!SafeFlags.includes(emoji.id)) {\n          ignore()\n          continue\n        }\n      }\n\n      if (!emoji.search) {\n        resetSearchIndex = true\n        emoji.search =\n          ',' +\n          [\n            [emoji.id, false],\n            [emoji.name, true],\n            [emoji.keywords, false],\n            [emoji.emoticons, false],\n          ]\n            .map(([strings, split]) => {\n              if (!strings) return\n              return (Array.isArray(strings) ? strings : [strings])\n                .map((string) => {\n                  return (split ? string.split(/[-|_|\\s]+/) : [string]).map(\n                    (s) => s.toLowerCase(),\n                  )\n                })\n                .flat()\n            })\n            .flat()\n            .filter((a) => a && a.trim())\n            .join(',')\n\n        if (emoji.emoticons) {\n          for (const emoticon of emoji.emoticons) {\n            if (Data.emoticons[emoticon]) continue\n            Data.emoticons[emoticon] = emoji.id\n          }\n        }\n\n        let skinIndex = 0\n        for (const skin of emoji.skins) {\n          if (!skin) continue\n          skinIndex++\n\n          const { native } = skin\n          if (native) {\n            Data.natives[native] = emoji.id\n            emoji.search += `,${native}`\n          }\n\n          const skinShortcodes =\n            skinIndex == 1 ? '' : `:skin-tone-${skinIndex}:`\n          skin.shortcodes = `:${emoji.id}:${skinShortcodes}`\n        }\n      }\n    }\n  }\n\n  if (resetSearchIndex) {\n    SearchIndex.reset()\n  }\n\n  initCallback()\n}\n\nexport function getProps(props, defaultProps, element) {\n  props || (props = {})\n\n  const _props = {}\n  for (let k in defaultProps) {\n    _props[k] = getProp(k, props, defaultProps, element)\n  }\n\n  return _props\n}\n\nexport function getProp(propName, props, defaultProps, element) {\n  const defaults = defaultProps[propName]\n  let value =\n    (element && element.getAttribute(propName)) ||\n    (props[propName] != null && props[propName] != undefined\n      ? props[propName]\n      : null)\n\n  if (!defaults) {\n    return value\n  }\n\n  if (\n    value != null &&\n    defaults.value &&\n    typeof defaults.value != typeof value\n  ) {\n    if (typeof defaults.value == 'boolean') {\n      value = value == 'false' ? false : true\n    } else {\n      value = defaults.value.constructor(value)\n    }\n  }\n\n  if (defaults.transform && value) {\n    value = defaults.transform(value)\n  }\n\n  if (\n    value == null ||\n    (defaults.choices && defaults.choices.indexOf(value) == -1)\n  ) {\n    value = defaults.value\n  }\n\n  return value\n}\n", "{\n  \"search\": \"Search\",\n  \"search_no_results_1\": \"Oh no!\",\n  \"search_no_results_2\": \"That emoji couldn’t be found\",\n  \"pick\": \"Pick an emoji…\",\n  \"add_custom\": \"Add custom emoji\",\n  \"categories\": {\n    \"activity\": \"Activity\",\n    \"custom\": \"Custom\",\n    \"flags\": \"Flags\",\n    \"foods\": \"Food & Drink\",\n    \"frequent\": \"Frequently used\",\n    \"nature\": \"Animals & Nature\",\n    \"objects\": \"Objects\",\n    \"people\": \"Smileys & People\",\n    \"places\": \"Travel & Places\",\n    \"search\": \"Search Results\",\n    \"symbols\": \"Symbols\"\n  },\n  \"skins\": {\n    \"choose\": \"Choose default skin tone\",\n    \"1\": \"Default\",\n    \"2\": \"Light\",\n    \"3\": \"Medium-Light\",\n    \"4\": \"Medium\",\n    \"5\": \"Medium-Dark\",\n    \"6\": \"Dark\"\n  }\n}\n", "export default {\n  autoFocus: {\n    value: false,\n  },\n  dynamicWidth: {\n    value: false,\n  },\n  emojiButtonColors: {\n    value: null,\n  },\n  emojiButtonRadius: {\n    value: '100%',\n  },\n  emojiButtonSize: {\n    value: 36,\n  },\n  emojiSize: {\n    value: 24,\n  },\n  emojiVersion: {\n    value: 15,\n    choices: [1, 2, 3, 4, 5, 11, 12, 12.1, 13, 13.1, 14, 15],\n  },\n  exceptEmojis: {\n    value: [],\n  },\n  icons: {\n    value: 'auto',\n    choices: ['auto', 'outline', 'solid'],\n  },\n  locale: {\n    value: 'en',\n    choices: [\n      'en',\n      'ar',\n      'be',\n      'cs',\n      'de',\n      'es',\n      'fa',\n      'fi',\n      'fr',\n      'hi',\n      'it',\n      'ja',\n      'ko',\n      'nl',\n      'pl',\n      'pt',\n      'ru',\n      'sa',\n      'tr',\n      'uk',\n      'vi',\n      'zh',\n    ],\n  },\n  maxFrequentRows: {\n    value: 4,\n  },\n  navPosition: {\n    value: 'top',\n    choices: ['top', 'bottom', 'none'],\n  },\n  noCountryFlags: {\n    value: false,\n  },\n  noResultsEmoji: {\n    value: null,\n  },\n  perLine: {\n    value: 9,\n  },\n  previewEmoji: {\n    value: null,\n  },\n  previewPosition: {\n    value: 'bottom',\n    choices: ['top', 'bottom', 'none'],\n  },\n  searchPosition: {\n    value: 'sticky',\n    choices: ['sticky', 'static', 'none'],\n  },\n  set: {\n    value: 'native',\n    choices: ['native', 'apple', 'facebook', 'google', 'twitter'],\n  },\n  skin: {\n    value: 1,\n    choices: [1, 2, 3, 4, 5, 6],\n  },\n  skinTonePosition: {\n    value: 'preview',\n    choices: ['preview', 'search', 'none'],\n  },\n  theme: {\n    value: 'auto',\n    choices: ['auto', 'light', 'dark'],\n  },\n\n  // Data\n  categories: null,\n  categoryIcons: null,\n  custom: null,\n  data: null,\n  i18n: null,\n\n  // Callbacks\n  getImageURL: null,\n  getSpritesheetURL: null,\n  onAddCustomEmoji: null,\n  onClickOutside: null,\n  onEmojiSelect: null,\n\n  // Deprecated\n  stickySearch: {\n    deprecated: true,\n    value: true,\n  },\n}\n", "const categories = {\n  activity: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\" />\n      </svg>\n    ),\n  },\n\n  custom: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\">\n      <path d=\"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\" />\n    </svg>\n  ),\n\n  flags: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\" />\n      </svg>\n    ),\n  },\n\n  foods: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\" />\n      </svg>\n    ),\n  },\n\n  frequent: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\" />\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\" />\n      </svg>\n    ),\n  },\n\n  nature: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\" />\n        <path d=\"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\">\n        <path d=\"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\" />\n      </svg>\n    ),\n  },\n\n  objects: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\" />\n        <path d=\"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\">\n        <path d=\"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\" />\n      </svg>\n    ),\n  },\n\n  people: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n        <path d=\"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\" />\n      </svg>\n    ),\n  },\n\n  places: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\" />\n        <path d=\"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\" />\n      </svg>\n    ),\n  },\n\n  symbols: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\" />\n      </svg>\n    ),\n  },\n}\n\nconst search = {\n  loupe: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\" />\n    </svg>\n  ),\n\n  delete: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\" />\n    </svg>\n  ),\n}\n\nexport default { categories, search }\n", "export { default as Emoji } from './Emoji'\nexport { default as EmojiElement } from './EmojiElement'\n", "import { Data } from '../../config'\nimport { SearchIndex } from '../../helpers'\n\nexport default function Emoji(props) {\n  let { id, skin, emoji } = props\n\n  if (props.shortcodes) {\n    const matches = props.shortcodes.match(SearchIndex.SHORTCODES_REGEX)\n\n    if (matches) {\n      id = matches[1]\n\n      if (matches[2]) {\n        skin = matches[2]\n      }\n    }\n  }\n\n  emoji || (emoji = SearchIndex.get(id || props.native))\n  if (!emoji) return props.fallback\n\n  const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n\n  const imageSrc =\n    emojiSkin.src ||\n    (props.set != 'native' && !props.spritesheet\n      ? typeof props.getImageURL === 'function'\n        ? props.getImageURL(props.set, emojiSkin.unified)\n        : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png`\n      : undefined)\n\n  const spritesheetSrc =\n    typeof props.getSpritesheetURL === 'function'\n      ? props.getSpritesheetURL(props.set)\n      : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`\n\n  return (\n    <span class=\"emoji-mart-emoji\" data-emoji-set={props.set}>\n      {imageSrc ? (\n        <img\n          style={{\n            maxWidth: props.size || '1em',\n            maxHeight: props.size || '1em',\n            display: 'inline-block',\n          }}\n          alt={emojiSkin.native || emojiSkin.shortcodes}\n          src={imageSrc}\n        />\n      ) : props.set == 'native' ? (\n        <span\n          style={{\n            fontSize: props.size,\n            fontFamily:\n              '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"',\n          }}\n        >\n          {emojiSkin.native}\n        </span>\n      ) : (\n        <span\n          style={{\n            display: 'block',\n            width: props.size,\n            height: props.size,\n            backgroundImage: `url(${spritesheetSrc})`,\n            backgroundSize: `${100 * Data.sheet.cols}% ${\n              100 * Data.sheet.rows\n            }%`,\n            backgroundPosition: `${\n              (100 / (Data.sheet.cols - 1)) * emojiSkin.x\n            }% ${(100 / (Data.sheet.rows - 1)) * emojiSkin.y}%`,\n          }}\n        ></span>\n      )}\n    </span>\n  )\n}\n", "import { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { HTMLElement } from '../HTMLElement'\nimport { Emoji } from '.'\nimport EmojiProps from './EmojiProps'\n\nexport default class EmojiElement extends HTMLElement {\n  static Props = EmojiProps\n\n  constructor(props) {\n    super(props)\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, EmojiProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init()\n    if (this.disconnected) return\n\n    render(<Emoji {...props} />, this)\n  }\n}\n\nif (typeof customElements !== 'undefined' && !customElements.get('em-emoji')) {\n  customElements.define('em-emoji', EmojiElement)\n}\n", "export { default as HTMLElement } from './HTMLElement'\nexport { default as ShadowElement } from './ShadowElement'\n", "// @ts-nocheck\nimport { getProp } from '../../config'\n\nconst WindowHTMLElement =\n  typeof window !== 'undefined' && window.HTMLElement\n    ? window.HTMLElement\n    : Object\n\nexport default class HTMLElement extends WindowHTMLElement {\n  static get observedAttributes() {\n    return Object.keys(this.Props)\n  }\n\n  constructor(props = {}) {\n    super()\n    this.props = props\n\n    if (props.parent || props.ref) {\n      let ref = null\n      const parent = props.parent || (ref = props.ref && props.ref.current)\n\n      if (ref) ref.innerHTML = ''\n      if (parent) parent.appendChild(this)\n    }\n  }\n\n  update(props = {}) {\n    for (let k in props) {\n      this.attributeChangedCallback(k, null, props[k])\n    }\n  }\n\n  attributeChangedCallback(attr, _, newValue) {\n    if (!this.component) return\n\n    const value = getProp(\n      attr,\n      { [attr]: newValue },\n      this.constructor.Props,\n      this,\n    )\n\n    if (this.component.componentWillReceiveProps) {\n      this.component.componentWillReceiveProps({ [attr]: value })\n    } else {\n      this.component.props[attr] = value\n      this.component.forceUpdate()\n    }\n  }\n\n  disconnectedCallback() {\n    this.disconnected = true\n\n    if (this.component && this.component.unregister) {\n      this.component.unregister()\n    }\n  }\n}\n", "// @ts-nocheck\nimport { HTMLElement } from '.'\n\nexport default class ShadowElement extends HTMLElement {\n  constructor(props, { styles } = {}) {\n    super(props)\n\n    this.setShadow()\n    this.injectStyles(styles)\n  }\n\n  setShadow() {\n    this.attachShadow({ mode: 'open' })\n  }\n\n  injectStyles(styles) {\n    if (!styles) return\n\n    const style = document.createElement('style')\n    style.textContent = styles\n\n    this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild)\n  }\n}\n", "import PickerProps from '../Picker/PickerProps'\n\nexport default {\n  fallback: '',\n  id: '',\n  native: '',\n  shortcodes: '',\n  size: {\n    value: '',\n    transform: (value) => {\n      // If the value is a number, then we assume it’s a pixel value.\n      if (!/\\D/.test(value)) {\n        return `${value}px`\n      }\n\n      return value\n    },\n  },\n\n  // Shared\n  set: PickerProps.set,\n  skin: PickerProps.skin,\n}\n", "export { default as Navigation } from './Navigation'\n", "// @ts-nocheck\nimport { PureComponent } from 'preact/compat'\nimport { Data, I18n } from '../../config'\nimport Icons from '../../icons'\n\nconst THEME_ICONS = {\n  light: 'outline',\n  dark: 'solid',\n}\n\nexport default class Navigation extends PureComponent {\n  constructor() {\n    super()\n\n    this.categories = Data.categories.filter((category) => {\n      return !category.target\n    })\n\n    this.state = {\n      categoryId: this.categories[0].id,\n    }\n  }\n\n  renderIcon(category) {\n    const { icon } = category\n\n    if (icon) {\n      if (icon.svg) {\n        return (\n          <span\n            class=\"flex\"\n            dangerouslySetInnerHTML={{ __html: icon.svg }}\n          ></span>\n        )\n      }\n\n      if (icon.src) {\n        return <img src={icon.src} />\n      }\n    }\n\n    const categoryIcons =\n      Icons.categories[category.id] || Icons.categories.custom\n\n    const style =\n      this.props.icons == 'auto'\n        ? THEME_ICONS[this.props.theme]\n        : this.props.icons\n\n    return categoryIcons[style] || categoryIcons\n  }\n\n  render() {\n    let selectedCategoryIndex = null\n\n    return (\n      <nav\n        id=\"nav\"\n        class=\"padding\"\n        data-position={this.props.position}\n        dir={this.props.dir}\n      >\n        <div class=\"flex relative\">\n          {this.categories.map((category, i) => {\n            const title = category.name || I18n.categories[category.id]\n            const selected =\n              !this.props.unfocused && category.id == this.state.categoryId\n\n            if (selected) {\n              selectedCategoryIndex = i\n            }\n\n            return (\n              <button\n                aria-label={title}\n                aria-selected={selected || undefined}\n                title={title}\n                type=\"button\"\n                class=\"flex flex-grow flex-center\"\n                onMouseDown={(e) => e.preventDefault()}\n                onClick={() => {\n                  this.props.onClick({ category, i })\n                }}\n              >\n                {this.renderIcon(category)}\n              </button>\n            )\n          })}\n\n          <div\n            class=\"bar\"\n            style={{\n              width: `${100 / this.categories.length}%`,\n              opacity: selectedCategoryIndex == null ? 0 : 1,\n              transform:\n                this.props.dir === 'rtl'\n                  ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)`\n                  : `translateX(${selectedCategoryIndex * 100}%)`,\n            }}\n          ></div>\n        </div>\n      </nav>\n    )\n  }\n}\n", "import{useState as n,useReducer as t,useEffect as e,useLayoutEffect as r,useRef as u,useImperative<PERSON>andle as o,useMemo as i,use<PERSON><PERSON>back as l,useContext as c,useDebugValue as f}from\"preact/hooks\";export*from\"preact/hooks\";import{Component as a,createElement as s,options as h,toChildArray as d,Fragment as v,render as p,hydrate as m,cloneElement as y,createRef as b,createContext as _}from\"preact\";export{createElement,createContext,createRef,Fragment,Component}from\"preact\";function S(n,t){for(var e in t)n[e]=t[e];return n}function C(n,t){for(var e in n)if(\"__source\"!==e&&!(e in t))return!0;for(var r in t)if(\"__source\"!==r&&n[r]!==t[r])return!0;return!1}function E(n){this.props=n}function g(n,t){function e(n){var e=this.props.ref,r=e==n.ref;return!r&&e&&(e.call?e(null):e.current=null),t?!t(this.props,n)||!r:C(this.props,n)}function r(t){return this.shouldComponentUpdate=e,s(n,t)}return r.displayName=\"Memo(\"+(n.displayName||n.name)+\")\",r.prototype.isReactComponent=!0,r.__f=!0,r}(E.prototype=new a).isPureReactComponent=!0,E.prototype.shouldComponentUpdate=function(n,t){return C(this.props,n)||C(this.state,t)};var w=h.__b;h.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),w&&w(n)};var R=\"undefined\"!=typeof Symbol&&Symbol.for&&Symbol.for(\"react.forward_ref\")||3911;function x(n){function t(t,e){var r=S({},t);return delete r.ref,n(r,(e=t.ref||e)&&(\"object\"!=typeof e||\"current\"in e)?e:null)}return t.$$typeof=R,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName=\"ForwardRef(\"+(n.displayName||n.name)+\")\",t}var N=function(n,t){return null==n?null:d(d(n).map(t))},k={map:N,forEach:N,count:function(n){return n?d(n).length:0},only:function(n){var t=d(n);if(1!==t.length)throw\"Children.only\";return t[0]},toArray:d},A=h.__e;h.__e=function(n,t,e){if(n.then)for(var r,u=t;u=u.__;)if((r=u.__c)&&r.__c)return null==t.__e&&(t.__e=e.__e,t.__k=e.__k),r.__c(n,t);A(n,t,e)};var O=h.unmount;function L(){this.__u=0,this.t=null,this.__b=null}function U(n){var t=n.__.__c;return t&&t.__e&&t.__e(n)}function F(n){var t,e,r;function u(u){if(t||(t=n()).then(function(n){e=n.default||n},function(n){r=n}),r)throw r;if(!e)throw t;return s(e,u)}return u.displayName=\"Lazy\",u.__f=!0,u}function M(){this.u=null,this.o=null}h.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&!0===n.__h&&(n.type=null),O&&O(n)},(L.prototype=new a).__c=function(n,t){var e=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(e);var u=U(r.__v),o=!1,i=function(){o||(o=!0,e.__R=null,u?u(l):l())};e.__R=i;var l=function(){if(!--r.__u){if(r.state.__e){var n=r.state.__e;r.__v.__k[0]=function n(t,e,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return n(t,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(n,n.__c.__P,n.__c.__O)}var t;for(r.setState({__e:r.__b=null});t=r.t.pop();)t.forceUpdate()}},c=!0===t.__h;r.__u++||c||r.setState({__e:r.__b=r.__v.__k[0]}),n.then(i,i)},L.prototype.componentWillUnmount=function(){this.t=[]},L.prototype.render=function(n,t){if(this.__b){if(this.__v.__k){var e=document.createElement(\"div\"),r=this.__v.__k[0].__c;this.__v.__k[0]=function n(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){\"function\"==typeof n.__c&&n.__c()}),t.__c.__H=null),null!=(t=S({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return n(t,e,r)})),t}(this.__b,e,r.__O=r.__P)}this.__b=null}var u=t.__e&&s(v,null,n.fallback);return u&&(u.__h=null),[s(v,null,t.__e?null:n.children),u]};var T=function(n,t,e){if(++e[1]===e[0]&&n.o.delete(t),n.props.revealOrder&&(\"t\"!==n.props.revealOrder[0]||!n.o.size))for(e=n.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.u=e=e[2]}};function D(n){return this.getChildContext=function(){return n.context},n.children}function I(n){var t=this,e=n.i;t.componentWillUnmount=function(){p(null,t.l),t.l=null,t.i=null},t.i&&t.i!==e&&t.componentWillUnmount(),n.__v?(t.l||(t.i=e,t.l={nodeType:1,parentNode:e,childNodes:[],appendChild:function(n){this.childNodes.push(n),t.i.appendChild(n)},insertBefore:function(n,e){this.childNodes.push(n),t.i.appendChild(n)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),t.i.removeChild(n)}}),p(s(D,{context:t.context},n.__v),t.l)):t.l&&t.componentWillUnmount()}function W(n,t){return s(I,{__v:n,i:t})}(M.prototype=new a).__e=function(n){var t=this,e=U(t.__v),r=t.o.get(n);return r[0]++,function(u){var o=function(){t.props.revealOrder?(r.push(u),T(t,n,r)):u()};e?e(o):o()}},M.prototype.render=function(n){this.u=null,this.o=new Map;var t=d(n.children);n.revealOrder&&\"b\"===n.revealOrder[0]&&t.reverse();for(var e=t.length;e--;)this.o.set(t[e],this.u=[1,0,this.u]);return n.children},M.prototype.componentDidUpdate=M.prototype.componentDidMount=function(){var n=this;this.o.forEach(function(t,e){T(n,e,t)})};var j=\"undefined\"!=typeof Symbol&&Symbol.for&&Symbol.for(\"react.element\")||60103,P=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,V=\"undefined\"!=typeof document,z=function(n){return(\"undefined\"!=typeof Symbol&&\"symbol\"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(n)};function B(n,t,e){return null==t.__k&&(t.textContent=\"\"),p(n,t),\"function\"==typeof e&&e(),n?n.__c:null}function $(n,t,e){return m(n,t),\"function\"==typeof e&&e(),n?n.__c:null}a.prototype.isReactComponent={},[\"componentWillMount\",\"componentWillReceiveProps\",\"componentWillUpdate\"].forEach(function(n){Object.defineProperty(a.prototype,n,{configurable:!0,get:function(){return this[\"UNSAFE_\"+n]},set:function(t){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:t})}})});var H=h.event;function Z(){}function Y(){return this.cancelBubble}function q(){return this.defaultPrevented}h.event=function(n){return H&&(n=H(n)),n.persist=Z,n.isPropagationStopped=Y,n.isDefaultPrevented=q,n.nativeEvent=n};var G,J={configurable:!0,get:function(){return this.class}},K=h.vnode;h.vnode=function(n){var t=n.type,e=n.props,r=e;if(\"string\"==typeof t){var u=-1===t.indexOf(\"-\");for(var o in r={},e){var i=e[o];V&&\"children\"===o&&\"noscript\"===t||\"value\"===o&&\"defaultValue\"in e&&null==i||(\"defaultValue\"===o&&\"value\"in e&&null==e.value?o=\"value\":\"download\"===o&&!0===i?i=\"\":/ondoubleclick/i.test(o)?o=\"ondblclick\":/^onchange(textarea|input)/i.test(o+t)&&!z(e.type)?o=\"oninput\":/^onfocus$/i.test(o)?o=\"onfocusin\":/^onblur$/i.test(o)?o=\"onfocusout\":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():u&&P.test(o)?o=o.replace(/[A-Z0-9]/,\"-$&\").toLowerCase():null===i&&(i=void 0),r[o]=i)}\"select\"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=d(e.children).forEach(function(n){n.props.selected=-1!=r.value.indexOf(n.props.value)})),\"select\"==t&&null!=r.defaultValue&&(r.value=d(e.children).forEach(function(n){n.props.selected=r.multiple?-1!=r.defaultValue.indexOf(n.props.value):r.defaultValue==n.props.value})),n.props=r,e.class!=e.className&&(J.enumerable=\"className\"in e,null!=e.className&&(r.class=e.className),Object.defineProperty(r,\"className\",J))}n.$$typeof=j,K&&K(n)};var Q=h.__r;h.__r=function(n){Q&&Q(n),G=n.__c};var X={ReactCurrentDispatcher:{current:{readContext:function(n){return G.__n[n.__c].props.value}}}},nn=\"17.0.2\";function tn(n){return s.bind(null,n)}function en(n){return!!n&&n.$$typeof===j}function rn(n){return en(n)?y.apply(null,arguments):n}function un(n){return!!n.__k&&(p(null,n),!0)}function on(n){return n&&(n.base||1===n.nodeType&&n)||null}var ln=function(n,t){return n(t)},cn=function(n,t){return n(t)},fn=v;export default{useState:n,useReducer:t,useEffect:e,useLayoutEffect:r,useRef:u,useImperativeHandle:o,useMemo:i,useCallback:l,useContext:c,useDebugValue:f,version:\"17.0.2\",Children:k,render:B,hydrate:$,unmountComponentAtNode:un,createPortal:W,createElement:s,createContext:_,createFactory:tn,cloneElement:rn,createRef:b,Fragment:v,isValidElement:en,findDOMNode:on,Component:a,PureComponent:E,memo:g,forwardRef:x,flushSync:cn,unstable_batchedUpdates:ln,StrictMode:v,Suspense:L,SuspenseList:M,lazy:F,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:X};export{nn as version,k as Children,B as render,$ as hydrate,un as unmountComponentAtNode,W as createPortal,tn as createFactory,rn as cloneElement,en as isValidElement,on as findDOMNode,E as PureComponent,g as memo,x as forwardRef,cn as flushSync,ln as unstable_batchedUpdates,fn as StrictMode,L as Suspense,M as SuspenseList,F as lazy,X as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED};\n//# sourceMappingURL=compat.module.js.map\n", "/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Check if two objects have a different shape\r\n * @param {object} a\r\n * @param {object} b\r\n * @returns {boolean}\r\n */\r\nexport function shallowDiffers(a, b) {\r\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\r\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\r\n\treturn false;\r\n}\r\n\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n", "import { Component } from 'preact';\r\nimport { shallowDiffers } from './util';\r\n\r\n/**\r\n * Component class with a predefined `shouldComponentUpdate` implementation\r\n */\r\nexport function PureComponent(p) {\r\n\tthis.props = p;\r\n}\r\nPureComponent.prototype = new Component();\r\n// Some third-party libraries check if this property is present\r\nPureComponent.prototype.isPureReactComponent = true;\r\nPureComponent.prototype.shouldComponentUpdate = function(props, state) {\r\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\r\n};\r\n", "import { createElement } from 'preact';\r\nimport { shallowDiffers } from './util';\r\n\r\n/**\r\n * Memoize a component, so that it only updates when the props actually have\r\n * changed. This was previously known as `React.pure`.\r\n * @param {import('./internal').FunctionComponent} c functional component\r\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\r\n * @returns {import('./internal').FunctionComponent}\r\n */\r\nexport function memo(c, comparer) {\r\n\tfunction shouldUpdate(nextProps) {\r\n\t\tlet ref = this.props.ref;\r\n\t\tlet updateRef = ref == nextProps.ref;\r\n\t\tif (!updateRef && ref) {\r\n\t\t\tref.call ? ref(null) : (ref.current = null);\r\n\t\t}\r\n\r\n\t\tif (!comparer) {\r\n\t\t\treturn shallowDiffers(this.props, nextProps);\r\n\t\t}\r\n\r\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\r\n\t}\r\n\r\n\tfunction Memoed(props) {\r\n\t\tthis.shouldComponentUpdate = shouldUpdate;\r\n\t\treturn createElement(c, props);\r\n\t}\r\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\r\n\tMemoed.prototype.isReactComponent = true;\r\n\tMemoed._forwarded = true;\r\n\treturn Memoed;\r\n}\r\n", "import { options } from 'preact';\r\nimport { assign } from './util';\r\n\r\nlet oldDiffHook = options._diff;\r\noptions._diff = vnode => {\r\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\r\n\t\tvnode.props.ref = vnode.ref;\r\n\t\tvnode.ref = null;\r\n\t}\r\n\tif (oldDiffHook) oldDiffHook(vnode);\r\n};\r\n\r\nexport const REACT_FORWARD_SYMBOL =\r\n\t(typeof Symbol != 'undefined' &&\r\n\t\tSymbol.for &&\r\n\t\tSymbol.for('react.forward_ref')) ||\r\n\t0xf47;\r\n\r\n/**\r\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\r\n * wrap components. Using `forwardRef` there is an easy way to get a reference\r\n * of the wrapped component instead of one of the wrapper itself.\r\n * @param {import('./index').ForwardFn} fn\r\n * @returns {import('./internal').FunctionComponent}\r\n */\r\nexport function forwardRef(fn) {\r\n\t// We always have ref in props.ref, except for\r\n\t// mobx-react. It will call this function directly\r\n\t// and always pass ref as the second argument.\r\n\tfunction Forwarded(props, ref) {\r\n\t\tlet clone = assign({}, props);\r\n\t\tdelete clone.ref;\r\n\t\tref = props.ref || ref;\r\n\t\treturn fn(\r\n\t\t\tclone,\r\n\t\t\t!ref || (typeof ref === 'object' && !('current' in ref)) ? null : ref\r\n\t\t);\r\n\t}\r\n\r\n\t// mobx-react checks for this being present\r\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\r\n\t// mobx-react heavily relies on implementation details.\r\n\t// It expects an object here with a `render` property,\r\n\t// and prototype.render will fail. Without this\r\n\t// mobx-react throws.\r\n\tForwarded.render = Forwarded;\r\n\r\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\r\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\r\n\treturn Forwarded;\r\n}\r\n", "import { toChildArray } from 'preact';\r\n\r\nconst mapFn = (children, fn) => {\r\n\tif (children == null) return null;\r\n\treturn toChildArray(toChildArray(children).map(fn));\r\n};\r\n\r\n// This API is completely unnecessary for Preact, so it's basically passthrough.\r\nexport const Children = {\r\n\tmap: mapFn,\r\n\tforEach: mapFn,\r\n\tcount(children) {\r\n\t\treturn children ? toChildArray(children).length : 0;\r\n\t},\r\n\tonly(children) {\r\n\t\tconst normalized = toChildArray(children);\r\n\t\tif (normalized.length !== 1) throw 'Children.only';\r\n\t\treturn normalized[0];\r\n\t},\r\n\ttoArray: toChildArray\r\n};\r\n", "import { Component, createElement, options, Fragment } from 'preact';\r\nimport { assign } from './util';\r\n\r\nconst oldCatchError = options._catchError;\r\noptions._catchError = function(error, newVNode, oldVNode) {\r\n\tif (error.then) {\r\n\t\t/** @type {import('./internal').Component} */\r\n\t\tlet component;\r\n\t\tlet vnode = newVNode;\r\n\r\n\t\tfor (; (vnode = vnode._parent); ) {\r\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\r\n\t\t\t\tif (newVNode._dom == null) {\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t}\r\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\r\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\toldCatchError(error, newVNode, oldVNode);\r\n};\r\n\r\nconst oldUnmount = options.unmount;\r\noptions.unmount = function(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tconst component = vnode._component;\r\n\tif (component && component._onResolve) {\r\n\t\tcomponent._onResolve();\r\n\t}\r\n\r\n\t// if the component is still hydrating\r\n\t// most likely it is because the component is suspended\r\n\t// we set the vnode.type as `null` so that it is not a typeof function\r\n\t// so the unmount will remove the vnode._dom\r\n\tif (component && vnode._hydrating === true) {\r\n\t\tvnode.type = null;\r\n\t}\r\n\r\n\tif (oldUnmount) oldUnmount(vnode);\r\n};\r\n\r\nfunction detachedClone(vnode, detachedParent, parentDom) {\r\n\tif (vnode) {\r\n\t\tif (vnode._component && vnode._component.__hooks) {\r\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\r\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\r\n\t\t\t});\r\n\r\n\t\t\tvnode._component.__hooks = null;\r\n\t\t}\r\n\r\n\t\tvnode = assign({}, vnode);\r\n\t\tif (vnode._component != null) {\r\n\t\t\tif (vnode._component._parentDom === parentDom) {\r\n\t\t\t\tvnode._component._parentDom = detachedParent;\r\n\t\t\t}\r\n\t\t\tvnode._component = null;\r\n\t\t}\r\n\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\r\n\t\t\t);\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\nfunction removeOriginal(vnode, detachedParent, originalParent) {\r\n\tif (vnode) {\r\n\t\tvnode._original = null;\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\r\n\t\t\t);\r\n\r\n\t\tif (vnode._component) {\r\n\t\t\tif (vnode._component._parentDom === detachedParent) {\r\n\t\t\t\tif (vnode._dom) {\r\n\t\t\t\t\toriginalParent.insertBefore(vnode._dom, vnode._nextDom);\r\n\t\t\t\t}\r\n\t\t\t\tvnode._component._force = true;\r\n\t\t\t\tvnode._component._parentDom = originalParent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\n// having custom inheritance instead of a class here saves a lot of bytes\r\nexport function Suspense() {\r\n\t// we do not call super here to golf some bytes...\r\n\tthis._pendingSuspensionCount = 0;\r\n\tthis._suspenders = null;\r\n\tthis._detachOnNextRender = null;\r\n}\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspense.prototype = new Component();\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {Promise} promise The thrown promise\r\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\r\n */\r\nSuspense.prototype._childDidSuspend = function(promise, suspendingVNode) {\r\n\tconst suspendingComponent = suspendingVNode._component;\r\n\r\n\t/** @type {import('./internal').SuspenseComponent} */\r\n\tconst c = this;\r\n\r\n\tif (c._suspenders == null) {\r\n\t\tc._suspenders = [];\r\n\t}\r\n\tc._suspenders.push(suspendingComponent);\r\n\r\n\tconst resolve = suspended(c._vnode);\r\n\r\n\tlet resolved = false;\r\n\tconst onResolved = () => {\r\n\t\tif (resolved) return;\r\n\r\n\t\tresolved = true;\r\n\t\tsuspendingComponent._onResolve = null;\r\n\r\n\t\tif (resolve) {\r\n\t\t\tresolve(onSuspensionComplete);\r\n\t\t} else {\r\n\t\t\tonSuspensionComplete();\r\n\t\t}\r\n\t};\r\n\r\n\tsuspendingComponent._onResolve = onResolved;\r\n\r\n\tconst onSuspensionComplete = () => {\r\n\t\tif (!--c._pendingSuspensionCount) {\r\n\t\t\t// If the suspension was during hydration we don't need to restore the\r\n\t\t\t// suspended children into the _children array\r\n\t\t\tif (c.state._suspended) {\r\n\t\t\t\tconst suspendedVNode = c.state._suspended;\r\n\t\t\t\tc._vnode._children[0] = removeOriginal(\r\n\t\t\t\t\tsuspendedVNode,\r\n\t\t\t\t\tsuspendedVNode._component._parentDom,\r\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\r\n\r\n\t\t\tlet suspended;\r\n\t\t\twhile ((suspended = c._suspenders.pop())) {\r\n\t\t\t\tsuspended.forceUpdate();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * We do not set `suspended: true` during hydration because we want the actual markup\r\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\r\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\r\n\t */\r\n\tconst wasHydrating = suspendingVNode._hydrating === true;\r\n\tif (!c._pendingSuspensionCount++ && !wasHydrating) {\r\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\r\n\t}\r\n\tpromise.then(onResolved, onResolved);\r\n};\r\n\r\nSuspense.prototype.componentWillUnmount = function() {\r\n\tthis._suspenders = [];\r\n};\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\r\n * @param {import('./internal').SuspenseState} state\r\n */\r\nSuspense.prototype.render = function(props, state) {\r\n\tif (this._detachOnNextRender) {\r\n\t\t// When the Suspense's _vnode was created by a call to createVNode\r\n\t\t// (i.e. due to a setState further up in the tree)\r\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\r\n\t\tif (this._vnode._children) {\r\n\t\t\tconst detachedParent = document.createElement('div');\r\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\r\n\t\t\tthis._vnode._children[0] = detachedClone(\r\n\t\t\t\tthis._detachOnNextRender,\r\n\t\t\t\tdetachedParent,\r\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tthis._detachOnNextRender = null;\r\n\t}\r\n\r\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\r\n\t/** @type {import('./internal').VNode} */\r\n\tconst fallback =\r\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\r\n\tif (fallback) fallback._hydrating = null;\r\n\r\n\treturn [\r\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\r\n\t\tfallback\r\n\t];\r\n};\r\n\r\n/**\r\n * Checks and calls the parent component's _suspended method, passing in the\r\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\r\n * that one of its children/descendants suspended.\r\n *\r\n * The parent MAY return a callback. The callback will get called when the\r\n * suspension resolves, notifying the parent of the fact.\r\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\r\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\r\n * This is a way for the parent to delay unsuspending.\r\n *\r\n * If the parent does not return a callback then the resolved vnode\r\n * gets unsuspended immediately when it resolves.\r\n *\r\n * @param {import('./internal').VNode} vnode\r\n * @returns {((unsuspend: () => void) => void)?}\r\n */\r\nexport function suspended(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tlet component = vnode._parent._component;\r\n\treturn component && component._suspended && component._suspended(vnode);\r\n}\r\n\r\nexport function lazy(loader) {\r\n\tlet prom;\r\n\tlet component;\r\n\tlet error;\r\n\r\n\tfunction Lazy(props) {\r\n\t\tif (!prom) {\r\n\t\t\tprom = loader();\r\n\t\t\tprom.then(\r\n\t\t\t\texports => {\r\n\t\t\t\t\tcomponent = exports.default || exports;\r\n\t\t\t\t},\r\n\t\t\t\te => {\r\n\t\t\t\t\terror = e;\r\n\t\t\t\t}\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error;\r\n\t\t}\r\n\r\n\t\tif (!component) {\r\n\t\t\tthrow prom;\r\n\t\t}\r\n\r\n\t\treturn createElement(component, props);\r\n\t}\r\n\r\n\tLazy.displayName = 'Lazy';\r\n\tLazy._forwarded = true;\r\n\treturn Lazy;\r\n}\r\n", "import { Component, toChildArray } from 'preact';\r\nimport { suspended } from './suspense.js';\r\n\r\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\r\nconst SUSPENDED_COUNT = 0;\r\nconst RESOLVED_COUNT = 1;\r\nconst NEXT_NODE = 2;\r\n\r\n// Having custom inheritance instead of a class here saves a lot of bytes.\r\nexport function SuspenseList() {\r\n\tthis._next = null;\r\n\tthis._map = null;\r\n}\r\n\r\n// Mark one of child's earlier suspensions as resolved.\r\n// Some pending callbacks may become callable due to this\r\n// (e.g. the last suspended descendant gets resolved when\r\n// revealOrder === 'together'). Process those callbacks as well.\r\nconst resolve = (list, child, node) => {\r\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\r\n\t\t// The number a child (or any of its descendants) has been suspended\r\n\t\t// matches the number of times it's been resolved. Therefore we\r\n\t\t// mark the child as completely resolved by deleting it from ._map.\r\n\t\t// This is used to figure out when *all* children have been completely\r\n\t\t// resolved when revealOrder is 'together'.\r\n\t\tlist._map.delete(child);\r\n\t}\r\n\r\n\t// If revealOrder is falsy then we can do an early exit, as the\r\n\t// callbacks won't get queued in the node anyway.\r\n\t// If revealOrder is 'together' then also do an early exit\r\n\t// if all suspended descendants have not yet been resolved.\r\n\tif (\r\n\t\t!list.props.revealOrder ||\r\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\r\n\t) {\r\n\t\treturn;\r\n\t}\r\n\r\n\t// Walk the currently suspended children in order, calling their\r\n\t// stored callbacks on the way. Stop if we encounter a child that\r\n\t// has not been completely resolved yet.\r\n\tnode = list._next;\r\n\twhile (node) {\r\n\t\twhile (node.length > 3) {\r\n\t\t\tnode.pop()();\r\n\t\t}\r\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\r\n\t\t\tbreak;\r\n\t\t}\r\n\t\tlist._next = node = node[NEXT_NODE];\r\n\t}\r\n};\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspenseList.prototype = new Component();\r\n\r\nSuspenseList.prototype._suspended = function(child) {\r\n\tconst list = this;\r\n\tconst delegated = suspended(list._vnode);\r\n\r\n\tlet node = list._map.get(child);\r\n\tnode[SUSPENDED_COUNT]++;\r\n\r\n\treturn unsuspend => {\r\n\t\tconst wrappedUnsuspend = () => {\r\n\t\t\tif (!list.props.revealOrder) {\r\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\r\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\r\n\t\t\t\tunsuspend();\r\n\t\t\t} else {\r\n\t\t\t\tnode.push(unsuspend);\r\n\t\t\t\tresolve(list, child, node);\r\n\t\t\t}\r\n\t\t};\r\n\t\tif (delegated) {\r\n\t\t\tdelegated(wrappedUnsuspend);\r\n\t\t} else {\r\n\t\t\twrappedUnsuspend();\r\n\t\t}\r\n\t};\r\n};\r\n\r\nSuspenseList.prototype.render = function(props) {\r\n\tthis._next = null;\r\n\tthis._map = new Map();\r\n\r\n\tconst children = toChildArray(props.children);\r\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\r\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\r\n\t\t// then flip the child list around so that the last child will be\r\n\t\t// the first in the linked list.\r\n\t\tchildren.reverse();\r\n\t}\r\n\t// Build the linked list. Iterate through the children in reverse order\r\n\t// so that `_next` points to the first linked list node to be resolved.\r\n\tfor (let i = children.length; i--; ) {\r\n\t\t// Create a new linked list node as an array of form:\r\n\t\t// \t[suspended_count, resolved_count, next_node]\r\n\t\t// where suspended_count and resolved_count are numeric counters for\r\n\t\t// keeping track how many times a node has been suspended and resolved.\r\n\t\t//\r\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\r\n\t\t// processing callbacks until componentDidMount has been called. In a sense\r\n\t\t// node is suspended at least until componentDidMount gets called!\r\n\t\t//\r\n\t\t// Pending callbacks are added to the end of the node:\r\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\r\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\r\n\t}\r\n\treturn props.children;\r\n};\r\n\r\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function() {\r\n\t// Iterate through all children after mounting for two reasons:\r\n\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\r\n\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\r\n\t//    The nodes can now be completely consumed from the linked list.\r\n\t// 2. Handle nodes that might have gotten resolved between render and\r\n\t//    componentDidMount.\r\n\tthis._map.forEach((node, child) => {\r\n\t\tresolve(this, child, node);\r\n\t});\r\n};\r\n", "import { createElement, render } from 'preact';\r\n\r\n/**\r\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\r\n */\r\nfunction ContextProvider(props) {\r\n\tthis.getChildContext = () => props.context;\r\n\treturn props.children;\r\n}\r\n\r\n/**\r\n * Portal component\r\n * @this {import('./internal').Component}\r\n * @param {object | null | undefined} props\r\n *\r\n * TODO: use createRoot() instead of fake root\r\n */\r\nfunction Portal(props) {\r\n\tconst _this = this;\r\n\tlet container = props._container;\r\n\r\n\t_this.componentWillUnmount = function() {\r\n\t\trender(null, _this._temp);\r\n\t\t_this._temp = null;\r\n\t\t_this._container = null;\r\n\t};\r\n\r\n\t// When we change container we should clear our old container and\r\n\t// indicate a new mount.\r\n\tif (_this._container && _this._container !== container) {\r\n\t\t_this.componentWillUnmount();\r\n\t}\r\n\r\n\t// When props.vnode is undefined/false/null we are dealing with some kind of\r\n\t// conditional vnode. This should not trigger a render.\r\n\tif (props._vnode) {\r\n\t\tif (!_this._temp) {\r\n\t\t\t_this._container = container;\r\n\r\n\t\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\r\n\t\t\t_this._temp = {\r\n\t\t\t\tnodeType: 1,\r\n\t\t\t\tparentNode: container,\r\n\t\t\t\tchildNodes: [],\r\n\t\t\t\tappendChild(child) {\r\n\t\t\t\t\tthis.childNodes.push(child);\r\n\t\t\t\t\t_this._container.appendChild(child);\r\n\t\t\t\t},\r\n\t\t\t\tinsertBefore(child, before) {\r\n\t\t\t\t\tthis.childNodes.push(child);\r\n\t\t\t\t\t_this._container.appendChild(child);\r\n\t\t\t\t},\r\n\t\t\t\tremoveChild(child) {\r\n\t\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\r\n\t\t\t\t\t_this._container.removeChild(child);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Render our wrapping element into temp.\r\n\t\trender(\r\n\t\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\r\n\t\t\t_this._temp\r\n\t\t);\r\n\t}\r\n\t// When we come from a conditional render, on a mounted\r\n\t// portal we should clear the DOM.\r\n\telse if (_this._temp) {\r\n\t\t_this.componentWillUnmount();\r\n\t}\r\n}\r\n\r\n/**\r\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\r\n * @param {import('./internal').VNode} vnode The vnode to render\r\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\r\n */\r\nexport function createPortal(vnode, container) {\r\n\treturn createElement(Portal, { _vnode: vnode, _container: container });\r\n}\r\n", "import {\r\n\trender as preactRender,\r\n\thydrate as preactHydrate,\r\n\toptions,\r\n\ttoChildArray,\r\n\tComponent\r\n} from 'preact';\r\n\r\nexport const REACT_ELEMENT_TYPE =\r\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\r\n\t0xeac7;\r\n\r\nconst CAMEL_PROPS = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\r\n\r\nconst IS_DOM = typeof document !== 'undefined';\r\n\r\n// Input types for which onchange should not be converted to oninput.\r\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\r\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\r\nconst onChangeInputType = type =>\r\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\r\n\t\t? /fil|che|rad/i\r\n\t\t: /fil|che|ra/i\r\n\t).test(type);\r\n\r\n// Some libraries like `react-virtualized` explicitly check for this.\r\nComponent.prototype.isReactComponent = {};\r\n\r\n// `UNSAFE_*` lifecycle hooks\r\n// Preact only ever invokes the unprefixed methods.\r\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\r\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\r\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\r\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\r\n// See https://github.com/preactjs/preact/issues/1941\r\n[\r\n\t'componentWillMount',\r\n\t'componentWillReceiveProps',\r\n\t'componentWillUpdate'\r\n].forEach(key => {\r\n\tObject.defineProperty(Component.prototype, key, {\r\n\t\tconfigurable: true,\r\n\t\tget() {\r\n\t\t\treturn this['UNSAFE_' + key];\r\n\t\t},\r\n\t\tset(v) {\r\n\t\t\tObject.defineProperty(this, key, {\r\n\t\t\t\tconfigurable: true,\r\n\t\t\t\twritable: true,\r\n\t\t\t\tvalue: v\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n});\r\n\r\n/**\r\n * Proxy render() since React returns a Component reference.\r\n * @param {import('./internal').VNode} vnode VNode tree to render\r\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\r\n * @param {() => void} [callback] Optional callback that will be called after rendering\r\n * @returns {import('./internal').Component | null} The root component reference or null\r\n */\r\nexport function render(vnode, parent, callback) {\r\n\t// React destroys any existing DOM nodes, see #1727\r\n\t// ...but only on the first render, see #1828\r\n\tif (parent._children == null) {\r\n\t\tparent.textContent = '';\r\n\t}\r\n\r\n\tpreactRender(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nexport function hydrate(vnode, parent, callback) {\r\n\tpreactHydrate(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nlet oldEventHook = options.event;\r\noptions.event = e => {\r\n\tif (oldEventHook) e = oldEventHook(e);\r\n\te.persist = empty;\r\n\te.isPropagationStopped = isPropagationStopped;\r\n\te.isDefaultPrevented = isDefaultPrevented;\r\n\treturn (e.nativeEvent = e);\r\n};\r\n\r\nfunction empty() {}\r\n\r\nfunction isPropagationStopped() {\r\n\treturn this.cancelBubble;\r\n}\r\n\r\nfunction isDefaultPrevented() {\r\n\treturn this.defaultPrevented;\r\n}\r\n\r\nlet classNameDescriptor = {\r\n\tconfigurable: true,\r\n\tget() {\r\n\t\treturn this.class;\r\n\t}\r\n};\r\n\r\nlet oldVNodeHook = options.vnode;\r\noptions.vnode = vnode => {\r\n\tlet type = vnode.type;\r\n\tlet props = vnode.props;\r\n\tlet normalizedProps = props;\r\n\r\n\t// only normalize props on Element nodes\r\n\tif (typeof type === 'string') {\r\n\t\tconst nonCustomElement = type.indexOf('-') === -1;\r\n\t\tnormalizedProps = {};\r\n\r\n\t\tfor (let i in props) {\r\n\t\t\tlet value = props[i];\r\n\r\n\t\t\tif (IS_DOM && i === 'children' && type === 'noscript') {\r\n\t\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\telse if (i === 'value' && 'defaultValue' in props && value == null) {\r\n\t\t\t\t// Skip applying value if it is null/undefined and we already set\r\n\t\t\t\t// a default value\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (\r\n\t\t\t\ti === 'defaultValue' &&\r\n\t\t\t\t'value' in props &&\r\n\t\t\t\tprops.value == null\r\n\t\t\t) {\r\n\t\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\r\n\t\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\r\n\t\t\t\ti = 'value';\r\n\t\t\t} else if (i === 'download' && value === true) {\r\n\t\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\r\n\t\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\r\n\t\t\t\t// converts it to an empty string instead, otherwise the attribute\r\n\t\t\t\t// value will be used as the file name and the file will be called\r\n\t\t\t\t// \"true\" upon downloading it.\r\n\t\t\t\tvalue = '';\r\n\t\t\t} else if (/ondoubleclick/i.test(i)) {\r\n\t\t\t\ti = 'ondblclick';\r\n\t\t\t} else if (\r\n\t\t\t\t/^onchange(textarea|input)/i.test(i + type) &&\r\n\t\t\t\t!onChangeInputType(props.type)\r\n\t\t\t) {\r\n\t\t\t\ti = 'oninput';\r\n\t\t\t} else if (/^onfocus$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusin';\r\n\t\t\t} else if (/^onblur$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusout';\r\n\t\t\t} else if (/^on(Ani|Tra|Tou|BeforeInp)/.test(i)) {\r\n\t\t\t\ti = i.toLowerCase();\r\n\t\t\t} else if (nonCustomElement && CAMEL_PROPS.test(i)) {\r\n\t\t\t\ti = i.replace(/[A-Z0-9]/, '-$&').toLowerCase();\r\n\t\t\t} else if (value === null) {\r\n\t\t\t\tvalue = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tnormalizedProps[i] = value;\r\n\t\t}\r\n\r\n\t\t// Add support for array select values: <select multiple value={[]} />\r\n\t\tif (\r\n\t\t\ttype == 'select' &&\r\n\t\t\tnormalizedProps.multiple &&\r\n\t\t\tArray.isArray(normalizedProps.value)\r\n\t\t) {\r\n\t\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tchild.props.selected =\r\n\t\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Adding support for defaultValue in select tag\r\n\t\tif (type == 'select' && normalizedProps.defaultValue != null) {\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tif (normalizedProps.multiple) {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tvnode.props = normalizedProps;\r\n\r\n\t\tif (props.class != props.className) {\r\n\t\t\tclassNameDescriptor.enumerable = 'className' in props;\r\n\t\t\tif (props.className != null) normalizedProps.class = props.className;\r\n\t\t\tObject.defineProperty(normalizedProps, 'className', classNameDescriptor);\r\n\t\t}\r\n\t}\r\n\r\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\r\n\r\n\tif (oldVNodeHook) oldVNodeHook(vnode);\r\n};\r\n\r\n// Only needed for react-relay\r\nlet currentComponent;\r\nconst oldBeforeRender = options._render;\r\noptions._render = function(vnode) {\r\n\tif (oldBeforeRender) {\r\n\t\toldBeforeRender(vnode);\r\n\t}\r\n\tcurrentComponent = vnode._component;\r\n};\r\n\r\n// This is a very very private internal function for React it\r\n// is used to sort-of do runtime dependency injection. So far\r\n// only `react-relay` makes use of it. It uses it to read the\r\n// context value.\r\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\r\n\tReactCurrentDispatcher: {\r\n\t\tcurrent: {\r\n\t\t\treadContext(context) {\r\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n", "import {\r\n\tcreateElement,\r\n\trender as preactRender,\r\n\tcloneElement as preactCloneElement,\r\n\tcreateRef,\r\n\tComponent,\r\n\tcreateContext,\r\n\tFragment\r\n} from 'preact';\r\nimport {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue\r\n} from 'preact/hooks';\r\nimport { PureComponent } from './PureComponent';\r\nimport { memo } from './memo';\r\nimport { forwardRef } from './forwardRef';\r\nimport { Children } from './Children';\r\nimport { Suspense, lazy } from './suspense';\r\nimport { SuspenseList } from './suspense-list';\r\nimport { createPortal } from './portals';\r\nimport {\r\n\thydrate,\r\n\trender,\r\n\tREACT_ELEMENT_TYPE,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n} from './render';\r\n\r\nconst version = '17.0.2'; // trick libraries to think we are react\r\n\r\n/**\r\n * Legacy version of createElement.\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\r\n */\r\nfunction createFactory(type) {\r\n\treturn createElement.bind(null, type);\r\n}\r\n\r\n/**\r\n * Check if the passed element is a valid (p)react node.\r\n * @param {*} element The element to check\r\n * @returns {boolean}\r\n */\r\nfunction isValidElement(element) {\r\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\r\n}\r\n\r\n/**\r\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\r\n * all vnode normalizations.\r\n * @param {import('./internal').VNode} element The vnode to clone\r\n * @param {object} props Props to add when cloning\r\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\r\n */\r\nfunction cloneElement(element) {\r\n\tif (!isValidElement(element)) return element;\r\n\treturn preactCloneElement.apply(null, arguments);\r\n}\r\n\r\n/**\r\n * Remove a component tree from the DOM, including state and event handlers.\r\n * @param {import('./internal').PreactElement} container\r\n * @returns {boolean}\r\n */\r\nfunction unmountComponentAtNode(container) {\r\n\tif (container._children) {\r\n\t\tpreactRender(null, container);\r\n\t\treturn true;\r\n\t}\r\n\treturn false;\r\n}\r\n\r\n/**\r\n * Get the matching DOM node for a component\r\n * @param {import('./internal').Component} component\r\n * @returns {import('./internal').PreactElement | null}\r\n */\r\nfunction findDOMNode(component) {\r\n\treturn (\r\n\t\t(component &&\r\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\r\n\t\tnull\r\n\t);\r\n}\r\n\r\n/**\r\n * Deprecated way to control batched rendering inside the reconciler, but we\r\n * already schedule in batches inside our rendering code\r\n * @template Arg\r\n * @param {(arg: Arg) => void} callback function that triggers the updated\r\n * @param {Arg} [arg] Optional argument that can be passed to the callback\r\n */\r\n// eslint-disable-next-line camelcase\r\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\r\n * implmented here as a no-op.\r\n * @template Arg\r\n * @template Result\r\n * @param {(arg: Arg) => Result} callback function that runs before the flush\r\n * @param {Arg} [arg] Optional arugment that can be passed to the callback\r\n * @returns\r\n */\r\nconst flushSync = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\r\n * that just renders its children without imposing any restrictions.\r\n */\r\nconst StrictMode = Fragment;\r\n\r\nexport * from 'preact/hooks';\r\nexport {\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\t// eslint-disable-next-line camelcase\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n\r\n// React copies the named exports to the default one.\r\nexport default {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue,\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n", "import{options as n}from\"preact\";var t,u,r,o=0,i=[],c=n.__b,f=n.__r,e=n.diffed,a=n.__c,v=n.unmount;function m(t,r){n.__h&&n.__h(u,t,o||r),o=0;var i=u.__H||(u.__H={__:[],__h:[]});return t>=i.__.length&&i.__.push({}),i.__[t]}function l(n){return o=1,p(w,n)}function p(n,r,o){var i=m(t++,2);return i.t=n,i.__c||(i.__=[o?o(r):w(void 0,r),function(n){var t=i.t(i.__[0],n);i.__[0]!==t&&(i.__=[t,i.__[1]],i.__c.setState({}))}],i.__c=u),i.__}function y(r,o){var i=m(t++,3);!n.__s&&k(i.__H,o)&&(i.__=r,i.__H=o,u.__H.__h.push(i))}function h(r,o){var i=m(t++,4);!n.__s&&k(i.__H,o)&&(i.__=r,i.__H=o,u.__h.push(i))}function s(n){return o=5,d(function(){return{current:n}},[])}function _(n,t,u){o=6,h(function(){\"function\"==typeof n?n(t()):n&&(n.current=t())},null==u?u:u.concat(n))}function d(n,u){var r=m(t++,7);return k(r.__H,u)&&(r.__=n(),r.__H=u,r.__h=n),r.__}function A(n,t){return o=8,d(function(){return n},t)}function F(n){var r=u.context[n.__c],o=m(t++,9);return o.c=n,r?(null==o.__&&(o.__=!0,r.sub(u)),r.props.value):n.__}function T(t,u){n.useDebugValue&&n.useDebugValue(u?u(t):t)}function q(n){var r=m(t++,10),o=l();return r.__=n,u.componentDidCatch||(u.componentDidCatch=function(n){r.__&&r.__(n),o[1](n)}),[o[0],function(){o[1](void 0)}]}function x(){var t;for(i.sort(function(n,t){return n.__v.__b-t.__v.__b});t=i.pop();)if(t.__P)try{t.__H.__h.forEach(g),t.__H.__h.forEach(j),t.__H.__h=[]}catch(u){t.__H.__h=[],n.__e(u,t.__v)}}n.__b=function(n){u=null,c&&c(n)},n.__r=function(n){f&&f(n),t=0;var r=(u=n.__c).__H;r&&(r.__h.forEach(g),r.__h.forEach(j),r.__h=[])},n.diffed=function(t){e&&e(t);var o=t.__c;o&&o.__H&&o.__H.__h.length&&(1!==i.push(o)&&r===n.requestAnimationFrame||((r=n.requestAnimationFrame)||function(n){var t,u=function(){clearTimeout(r),b&&cancelAnimationFrame(t),setTimeout(n)},r=setTimeout(u,100);b&&(t=requestAnimationFrame(u))})(x)),u=null},n.__c=function(t,u){u.some(function(t){try{t.__h.forEach(g),t.__h=t.__h.filter(function(n){return!n.__||j(n)})}catch(r){u.some(function(n){n.__h&&(n.__h=[])}),u=[],n.__e(r,t.__v)}}),a&&a(t,u)},n.unmount=function(t){v&&v(t);var u,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{g(n)}catch(n){u=n}}),u&&n.__e(u,r.__v))};var b=\"function\"==typeof requestAnimationFrame;function g(n){var t=u,r=n.__c;\"function\"==typeof r&&(n.__c=void 0,r()),u=t}function j(n){var t=u;n.__c=n.__(),u=t}function k(n,t){return!n||n.length!==t.length||t.some(function(t,u){return t!==n[u]})}function w(n,t){return\"function\"==typeof t?t(n):t}export{l as useState,p as useReducer,y as useEffect,h as useLayoutEffect,s as useRef,_ as useImperativeHandle,d as useMemo,A as useCallback,F as useContext,T as useDebugValue,q as useErrorBoundary};\n//# sourceMappingURL=hooks.module.js.map\n", "import { options } from 'preact';\r\n\r\n/** @type {number} */\r\nlet currentIndex;\r\n\r\n/** @type {import('./internal').Component} */\r\nlet currentComponent;\r\n\r\n/** @type {number} */\r\nlet currentHook = 0;\r\n\r\n/** @type {Array<import('./internal').Component>} */\r\nlet afterPaintEffects = [];\r\n\r\nlet oldBeforeDiff = options._diff;\r\nlet oldBeforeRender = options._render;\r\nlet oldAfterDiff = options.diffed;\r\nlet oldCommit = options._commit;\r\nlet oldBeforeUnmount = options.unmount;\r\n\r\nconst RAF_TIMEOUT = 100;\r\nlet prevRaf;\r\n\r\noptions._diff = vnode => {\r\n\tcurrentComponent = null;\r\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\r\n};\r\n\r\noptions._render = vnode => {\r\n\tif (oldBeforeRender) oldBeforeRender(vnode);\r\n\r\n\tcurrentComponent = vnode._component;\r\n\tcurrentIndex = 0;\r\n\r\n\tconst hooks = currentComponent.__hooks;\r\n\tif (hooks) {\r\n\t\thooks._pendingEffects.forEach(invokeCleanup);\r\n\t\thooks._pendingEffects.forEach(invokeEffect);\r\n\t\thooks._pendingEffects = [];\r\n\t}\r\n};\r\n\r\noptions.diffed = vnode => {\r\n\tif (oldAfterDiff) oldAfterDiff(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks && c.__hooks._pendingEffects.length) {\r\n\t\tafterPaint(afterPaintEffects.push(c));\r\n\t}\r\n\tcurrentComponent = null;\r\n};\r\n\r\noptions._commit = (vnode, commitQueue) => {\r\n\tcommitQueue.some(component => {\r\n\t\ttry {\r\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\r\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\r\n\t\t\t\tcb._value ? invokeEffect(cb) : true\r\n\t\t\t);\r\n\t\t} catch (e) {\r\n\t\t\tcommitQueue.some(c => {\r\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\r\n\t\t\t});\r\n\t\t\tcommitQueue = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t});\r\n\r\n\tif (oldCommit) oldCommit(vnode, commitQueue);\r\n};\r\n\r\noptions.unmount = vnode => {\r\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks) {\r\n\t\tlet hasErrored;\r\n\t\tc.__hooks._list.forEach(s => {\r\n\t\t\ttry {\r\n\t\t\t\tinvokeCleanup(s);\r\n\t\t\t} catch (e) {\r\n\t\t\t\thasErrored = e;\r\n\t\t\t}\r\n\t\t});\r\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\r\n\t}\r\n};\r\n\r\n/**\r\n * Get a hook's state from the currentComponent\r\n * @param {number} index The index of the hook to get\r\n * @param {number} type The index of the hook to get\r\n * @returns {any}\r\n */\r\nfunction getHookState(index, type) {\r\n\tif (options._hook) {\r\n\t\toptions._hook(currentComponent, index, currentHook || type);\r\n\t}\r\n\tcurrentHook = 0;\r\n\r\n\t// Largely inspired by:\r\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\r\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\r\n\t// Other implementations to look at:\r\n\t// * https://codesandbox.io/s/mnox05qp8\r\n\tconst hooks =\r\n\t\tcurrentComponent.__hooks ||\r\n\t\t(currentComponent.__hooks = {\r\n\t\t\t_list: [],\r\n\t\t\t_pendingEffects: []\r\n\t\t});\r\n\r\n\tif (index >= hooks._list.length) {\r\n\t\thooks._list.push({});\r\n\t}\r\n\treturn hooks._list[index];\r\n}\r\n\r\n/**\r\n * @param {import('./index').StateUpdater<any>} [initialState]\r\n */\r\nexport function useState(initialState) {\r\n\tcurrentHook = 1;\r\n\treturn useReducer(invokeOrReturn, initialState);\r\n}\r\n\r\n/**\r\n * @param {import('./index').Reducer<any, any>} reducer\r\n * @param {import('./index').StateUpdater<any>} initialState\r\n * @param {(initialState: any) => void} [init]\r\n * @returns {[ any, (state: any) => void ]}\r\n */\r\nexport function useReducer(reducer, initialState, init) {\r\n\t/** @type {import('./internal').ReducerHookState} */\r\n\tconst hookState = getHookState(currentIndex++, 2);\r\n\thookState._reducer = reducer;\r\n\tif (!hookState._component) {\r\n\t\thookState._value = [\r\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\r\n\r\n\t\t\taction => {\r\n\t\t\t\tconst nextValue = hookState._reducer(hookState._value[0], action);\r\n\t\t\t\tif (hookState._value[0] !== nextValue) {\r\n\t\t\t\t\thookState._value = [nextValue, hookState._value[1]];\r\n\t\t\t\t\thookState._component.setState({});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\thookState._component = currentComponent;\r\n\t}\r\n\r\n\treturn hookState._value;\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 3);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useLayoutEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 4);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent._renderCallbacks.push(state);\r\n\t}\r\n}\r\n\r\nexport function useRef(initialValue) {\r\n\tcurrentHook = 5;\r\n\treturn useMemo(() => ({ current: initialValue }), []);\r\n}\r\n\r\n/**\r\n * @param {object} ref\r\n * @param {() => object} createHandle\r\n * @param {any[]} args\r\n */\r\nexport function useImperativeHandle(ref, createHandle, args) {\r\n\tcurrentHook = 6;\r\n\tuseLayoutEffect(\r\n\t\t() => {\r\n\t\t\tif (typeof ref == 'function') ref(createHandle());\r\n\t\t\telse if (ref) ref.current = createHandle();\r\n\t\t},\r\n\t\targs == null ? args : args.concat(ref)\r\n\t);\r\n}\r\n\r\n/**\r\n * @param {() => any} factory\r\n * @param {any[]} args\r\n */\r\nexport function useMemo(factory, args) {\r\n\t/** @type {import('./internal').MemoHookState} */\r\n\tconst state = getHookState(currentIndex++, 7);\r\n\tif (argsChanged(state._args, args)) {\r\n\t\tstate._value = factory();\r\n\t\tstate._args = args;\r\n\t\tstate._factory = factory;\r\n\t}\r\n\r\n\treturn state._value;\r\n}\r\n\r\n/**\r\n * @param {() => void} callback\r\n * @param {any[]} args\r\n */\r\nexport function useCallback(callback, args) {\r\n\tcurrentHook = 8;\r\n\treturn useMemo(() => callback, args);\r\n}\r\n\r\n/**\r\n * @param {import('./internal').PreactContext} context\r\n */\r\nexport function useContext(context) {\r\n\tconst provider = currentComponent.context[context._id];\r\n\t// We could skip this call here, but than we'd not call\r\n\t// `options._hook`. We need to do that in order to make\r\n\t// the devtools aware of this hook.\r\n\t/** @type {import('./internal').ContextHookState} */\r\n\tconst state = getHookState(currentIndex++, 9);\r\n\t// The devtools needs access to the context object to\r\n\t// be able to pull of the default value when no provider\r\n\t// is present in the tree.\r\n\tstate._context = context;\r\n\tif (!provider) return context._defaultValue;\r\n\t// This is probably not safe to convert to \"!\"\r\n\tif (state._value == null) {\r\n\t\tstate._value = true;\r\n\t\tprovider.sub(currentComponent);\r\n\t}\r\n\treturn provider.props.value;\r\n}\r\n\r\n/**\r\n * Display a custom label for a custom hook for the devtools panel\r\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\r\n */\r\nexport function useDebugValue(value, formatter) {\r\n\tif (options.useDebugValue) {\r\n\t\toptions.useDebugValue(formatter ? formatter(value) : value);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {(error: any) => void} cb\r\n */\r\nexport function useErrorBoundary(cb) {\r\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\r\n\tconst state = getHookState(currentIndex++, 10);\r\n\tconst errState = useState();\r\n\tstate._value = cb;\r\n\tif (!currentComponent.componentDidCatch) {\r\n\t\tcurrentComponent.componentDidCatch = err => {\r\n\t\t\tif (state._value) state._value(err);\r\n\t\t\terrState[1](err);\r\n\t\t};\r\n\t}\r\n\treturn [\r\n\t\terrState[0],\r\n\t\t() => {\r\n\t\t\terrState[1](undefined);\r\n\t\t}\r\n\t];\r\n}\r\n\r\n/**\r\n * After paint effects consumer.\r\n */\r\nfunction flushAfterPaintEffects() {\r\n\tlet component;\r\n\t// sort the queue by depth (outermost to innermost)\r\n\tafterPaintEffects.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\twhile (component = afterPaintEffects.pop()) {\r\n\t\tif (!component._parentDom) continue;\r\n\t\ttry {\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t} catch (e) {\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\r\n\r\n/**\r\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\r\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\r\n * the next browser frame.\r\n *\r\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\r\n * even if RAF doesn't fire (for example if the browser tab is not visible)\r\n *\r\n * @param {() => void} callback\r\n */\r\nfunction afterNextFrame(callback) {\r\n\tconst done = () => {\r\n\t\tclearTimeout(timeout);\r\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\r\n\t\tsetTimeout(callback);\r\n\t};\r\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\r\n\r\n\tlet raf;\r\n\tif (HAS_RAF) {\r\n\t\traf = requestAnimationFrame(done);\r\n\t}\r\n}\r\n\r\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\r\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\r\n// Perhaps this is not such a big deal.\r\n/**\r\n * Schedule afterPaintEffects flush after the browser paints\r\n * @param {number} newQueueLength\r\n */\r\nfunction afterPaint(newQueueLength) {\r\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\r\n\t\tprevRaf = options.requestAnimationFrame;\r\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeCleanup(hook) {\r\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\tlet cleanup = hook._cleanup;\r\n\tif (typeof cleanup == 'function') {\r\n\t\thook._cleanup = undefined;\r\n\t\tcleanup();\r\n\t}\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * Invoke a Hook's effect\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeEffect(hook) {\r\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\thook._cleanup = hook._value();\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * @param {any[]} oldArgs\r\n * @param {any[]} newArgs\r\n */\r\nfunction argsChanged(oldArgs, newArgs) {\r\n\treturn (\r\n\t\t!oldArgs ||\r\n\t\toldArgs.length !== newArgs.length ||\r\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\r\n\t);\r\n}\r\n\r\nfunction invokeOrReturn(arg, f) {\r\n\treturn typeof f == 'function' ? f(arg) : f;\r\n}\r\n", "export { default as PureInlineComponent } from './PureInlineComponent'\n", "import { PureComponent } from 'preact/compat'\n\nexport default class PureInlineComponent extends PureComponent {\n  shouldComponentUpdate(nextProps) {\n    for (let k in nextProps) {\n      if (k == 'children') continue\n\n      if (nextProps[k] != this.props[k]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n", "// @ts-nocheck\nimport { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { ShadowElement } from '../HTMLElement'\nimport { Picker, PickerStyles } from '.'\nimport PickerProps from './PickerProps'\n\nexport default class PickerElement extends ShadowElement {\n  static Props = PickerProps\n\n  constructor(props) {\n    super(props, { styles: PickerStyles })\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, PickerProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init(props)\n    if (this.disconnected) return\n\n    render(<Picker {...props} />, this.shadowRoot)\n  }\n}\n\nif (\n  typeof customElements !== 'undefined' &&\n  !customElements.get('em-emoji-picker')\n) {\n  customElements.define('em-emoji-picker', PickerElement)\n}\n", "module.exports = \"565112e37e99085c\";"], "names": ["vnodeId", "createVNode", "type", "props", "key", "__source", "__self", "ref", "i", "normalizedProps", "vnode", "undefined", "constructor", "defaultProps", "options", "slice", "isValidElement", "rerenderQueue", "defer", "prevDebounce", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "children", "arguments", "length", "call", "original", "createRef", "current", "Fragment", "Component", "context", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "debounceRendering", "queue", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "oldDom", "parentDom", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "newDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "reorderC<PERSON>dren", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "applyRef", "tmp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "nextDom", "sibDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "diffProps", "dom", "newProps", "oldProps", "hydrate", "setProperty", "setStyle", "style", "value", "test", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "_listeners", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "setAttribute", "removeAttribute", "event", "newVNode", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "newType", "contextType", "prototype", "render", "doR<PERSON>", "sub", "state", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "for<PERSON>ach", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "diffElementNodes", "diffed", "root", "cb", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "innerHTML", "checked", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "this", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement", "createContext", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "_props", "old", "splice", "error", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "then", "bind", "resolve", "setTimeout", "shallow<PERSON>iffers", "PureComponent", "p", "memo", "comparer", "shouldUpdate", "nextProps", "updateRef", "Memoed", "displayName", "isReactComponent", "isPureReactComponent", "oldDiffHook", "REACT_FORWARD_SYMBOL", "Symbol", "for", "forwardRef", "fn", "Forwarded", "clone", "$$typeof", "mapFn", "map", "Children", "count", "only", "normalized", "toArray", "oldCatchError", "oldUnmount", "Suspense", "_suspenders", "suspended", "lazy", "loader", "prom", "Lazy", "exports", "default", "SuspenseList", "_next", "_map", "promise", "suspendingVNode", "suspendingComponent", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "removeOriginal", "detachedParent", "originalParent", "pop", "wasHydrating", "detachedComponent", "detachedClone", "effect", "fallback", "list", "delete", "revealOrder", "size", "ContextProvider", "Portal", "_this", "container", "_container", "_temp", "before", "createPortal", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "REACT_ELEMENT_TYPE", "CAMEL_PROPS", "IS_DOM", "onChangeInputType", "parent", "textContent", "preactRender", "preactHydrate", "Object", "defineProperty", "configurable", "v", "writable", "oldEventHook", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "persist", "nativeEvent", "currentComponent", "classNameDescriptor", "class", "oldVNodeHook", "nonCustomElement", "multiple", "selected", "className", "enumerable", "oldBeforeRender", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readContext", "version", "createFactory", "element", "preactCloneElement", "apply", "unmountComponentAtNode", "findDOMNode", "unstable_batchedUpdates", "arg", "flushSync", "StrictMode", "useState", "useReducer", "useEffect", "useLayoutEffect", "useRef", "useImperativeHandle", "useMemo", "useCallback", "useContext", "useDebugValue", "currentIndex", "prevRaf", "currentHook", "afterPaintEffects", "oldBeforeDiff", "oldAfterDiff", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "getHookState", "index", "hooks", "initialState", "invokeOrReturn", "reducer", "init", "hookState", "_reducer", "action", "nextValue", "args", "args<PERSON><PERSON><PERSON>", "initialValue", "createHandle", "concat", "factory", "formatter", "useErrorBoundary", "errState", "err", "flushAfterPaintEffects", "invokeCleanup", "invokeEffect", "requestAnimationFrame", "raf", "done", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "filter", "hasErrored", "hook", "comp", "cleanup", "oldArgs", "newArgs", "f"], "mappings": ";;;;;;;;;;;;;;;AGAe,SAAA,yCAAyB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IACvD,IAAI,GAAG,IAAI,GAAG,EACZ,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;QAC9B,KAAK,EAAE,KAAK;QACZ,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;SAEH,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAGnB,OAAO,GAAG,CAAC;CACZ;AIbM,IC0BMe,uBAAAA,ECfPD,yCAAAA,ECRFd,uBAAAA,EA6FSgB,yCAAAA,EC0ETC,uBAAAA,EAQEC,uBAAAA,EAcFC,uBAAAA,EC9LOX,uBAAAA,ELFEY,uBAAAA,GAAY,CAAA,CAAA,EACZC,uBAAAA,GAAY,EAAA,EACZC,uBAAAA,GAAAA,mEAAqB,ACO3B;AAAA,SAASC,uBAAAA,CAAOC,EAAAA,EAAKrB,EAAAA,EAAAA;IAAAA,IAEtB,IAAIK,EAAAA,IAAKL,EAAAA,CAAOqB,EAAAA,CAAIhB,EAAAA,CAAAA,GAAKL,EAAAA,CAAMK,EAAAA,CAAAA,CAAAA;IAAAA,OACPgB,EAAAA,CAAAA;CASvB;AAAA,SAASC,uBAAAA,CAAWC,EAAAA,EAAAA;IAAAA,IACtBC,EAAAA,GAAaD,EAAAA,CAAKC,UAClBA;IAAAA,EAAAA,IAAYA,EAAAA,CAAWC,WAAAA,CAAYF,EAAAA,CAAAA;CEVxC;AAAA,SAAgBG,yCAAAA,CAAc3B,EAAAA,EAAMC,EAAAA,EAAO2B,EAAAA,EAAAA;IAAAA,IAEzC1B,EAAAA,EACAG,EAAAA,EACAC,EAAAA,EAHGC,EAAAA,GAAkB,CAAA,CAAA;IAAA,IAIjBD,EAAAA,IAAKL,EAAAA,CACA,KAAA,IAALK,EAAAA,GAAYJ,EAAAA,GAAMD,EAAAA,CAAMK,EAAAA,CAAAA,GACd,KAAA,IAALA,EAAAA,GAAYD,EAAAA,GAAMJ,EAAAA,CAAMK,EAAAA,CAAAA,GAC5BC,EAAAA,CAAgBD,EAAAA,CAAAA,GAAKL,EAAAA,CAAMK,EAAAA,CAAAA,CAAAA;IAAAA,IAG7BuB,SAAAA,CAAUC,MAAAA,GAAS,CAAA,IACtBvB,CAAAA,EAAAA,CAAgBqB,QAAAA,GACfC,SAAAA,CAAUC,MAAAA,GAAS,CAAA,GAAIjB,uBAAAA,CAAMkB,IAAAA,CAAKF,SAAAA,EAAW,CAAA,CAAA,GAAKD,EAAAA,CAAAA,EAKjC,UAAA,IAAA,OAAR5B,EAAAA,IAA2C,IAAA,IAArBA,EAAAA,CAAKW,YAAAA,EAAAA,IAChCL,EAAAA,IAAKN,EAAAA,CAAKW,YAAAA,CAAAA,KACaF,CAAAA,KAAvBF,EAAAA,CAAgBD,EAAAA,CAAAA,IACnBC,CAAAA,EAAAA,CAAgBD,EAAAA,CAAAA,GAAKN,EAAAA,CAAKW,YAAAA,CAAaL,EAAAA,CAAAA,CAAAA,CAAAA;IAAAA,OAKnCP,uBAAAA,CAAYC,EAAAA,EAAMO,EAAAA,EAAiBL,EAAAA,EAAKG,EAAAA,EAAK,IAAA,CAAA,CAAA;CAe9C;AAAA,SAASN,uBAAAA,CAAYC,EAAAA,EAAMC,EAAAA,EAAOC,EAAAA,EAAKG,EAAAA,EAAK2B,EAAAA,EAAAA;IAAAA,IAG5CxB,EAAAA,GAAQ;QACbR,IAAAA,EAAAA,EAAAA;QACAC,KAAAA,EAAAA,EAAAA;QACAC,GAAAA,EAAAA,EAAAA;QACAG,GAAAA,EAAAA,EAAAA;QAAAA,GAAAA,EACW,IAAA;QAAA,EAAA,EACF,IAAA;QAAA,GAAA,EACD,CAAA;QAAA,GAAA,EACF,IAAA;QAAA,GAAA,EAAA,KAKII,CAAAA;QAAAA,GAAAA,EACE,IAAA;QAAA,GAAA,EACA,IAAA;QACZC,WAAAA,EAAAA,KAAaD,CAAAA;QAAAA,GAAAA,EACU,IAAA,IAAZuB,EAAAA,GAAAA,EAAqBlC,uBAAAA,GAAUkC,EAAAA;KAAAA;IAAAA,OAI3B,IAAA,IAAZA,EAAAA,IAAqC,IAAA,IAAjBpB,yCAAAA,CAAQJ,KAAAA,IAAeI,yCAAAA,CAAQJ,KAAAA,CAAMA,EAAAA,CAAAA,EAEtDA,EAAAA,CAAAA;CAGR;AAAA,SAAgByB,yCAAAA,GAAAA;IAAAA,OACR;QAAEC,OAAAA,EAAS,IAAA;KAAA,CAAA;CAGZ;AAAA,SAASC,yCAAAA,CAASlC,EAAAA,EAAAA;IAAAA,OACjBA,EAAAA,CAAM2B,QAAAA,CAAAA;CC5EP;AAAA,SAASQ,yCAAAA,CAAUnC,EAAAA,EAAOoC,EAAAA,EAAAA;IAAAA,IAAAA,CAC3BpC,KAAAA,GAAQA,EAAAA,EAAAA,IAAAA,CACRoC,OAAAA,GAAUA,EAAAA;CAyET;AAAA,SAASC,uBAAAA,CAAc9B,EAAAA,EAAO+B,EAAAA,EAAAA;IAAAA,IAClB,IAAA,IAAdA,EAAAA,EAAAA,OAEI/B,EAAAA,CAAAA,EAAAA,GACJ8B,uBAAAA,CAAc9B,EAAAA,CAAAA,EAAAA,EAAeA,EAAAA,CAAAA,EAAAA,CAAAA,GAAAA,CAAwBgC,OAAAA,CAAQhC,EAAAA,CAAAA,GAAS,CAAA,CAAA,GACtE,IAAA,CAAA;IAAA,IAAA,IAGAiC,EAAAA,EACGF,EAAAA,GAAa/B,EAAAA,CAAAA,GAAAA,CAAgBsB,MAAAA,EAAQS,EAAAA,EAAAA,CAAAA,IAG5B,IAAA,IAFfE,CAAAA,EAAAA,GAAUjC,EAAAA,CAAAA,GAAAA,CAAgB+B,EAAAA,CAAAA,CAAAA,IAEa,IAAA,IAAhBE,EAAAA,CAAAA,GAAAA,EAAAA,OAIfA,EAAAA,CAAAA,GAAAA,CAAAA;IAAAA,OASmB,UAAA,IAAA,OAAdjC,EAAAA,CAAMR,IAAAA,GAAqBsC,uBAAAA,CAAc9B,EAAAA,CAAAA,GAAS,IAAA,CAAA;CAuCjE;AAAA,SAASkC,uBAAAA,CAAwBlC,EAAAA,EAAAA;IAAjC,IAGWF,EAAAA,EACJqC,EAAAA;IAAAA,IAHyB,IAAA,IAA1BnC,CAAAA,EAAAA,GAAQA,EAAAA,CAAAA,EAAAA,CAAAA,IAA8C,IAAA,IAApBA,EAAAA,CAAAA,GAAAA,EAA0B;QAAA,IAChEA,EAAAA,CAAAA,GAAAA,GAAaA,EAAAA,CAAAA,GAAAA,CAAiBoC,IAAAA,GAAO,IAAA,EAC5BtC,EAAAA,GAAI,CAAA,EAAGA,EAAAA,GAAIE,EAAAA,CAAAA,GAAAA,CAAgBsB,MAAAA,EAAQxB,EAAAA,EAAAA,CAAAA,IAE9B,IAAA,IADTqC,CAAAA,EAAAA,GAAQnC,EAAAA,CAAAA,GAAAA,CAAgBF,EAAAA,CAAAA,CAAAA,IACO,IAAA,IAAdqC,EAAAA,CAAAA,GAAAA,EAAoB;YACxCnC,EAAAA,CAAAA,GAAAA,GAAaA,EAAAA,CAAAA,GAAAA,CAAiBoC,IAAAA,GAAOD,EAAAA,CAAAA,GAAAA,CAAAA;YAAAA,MAAAA;SAAAA;QAAAA,OAKhCD,uBAAAA,CAAwBlC,EAAAA,CAAAA,CAAAA;KAAAA;CAoC1B;AAAA,SAASqC,uBAAAA,CAAcC,EAAAA,EAAAA;IAAAA,CAAAA,CAE1BA,EAAAA,CAAAA,GAAAA,IACAA,CAAAA,EAAAA,CAAAA,GAAAA,GAAAA,CAAW,CAAA,CAAA,IACZ/B,uBAAAA,CAAcgC,IAAAA,CAAKD,EAAAA,CAAAA,IAAAA,CAClBE,uBAAAA,CAAAA,GAAAA,EAAAA,IACF/B,uBAAAA,KAAiBL,yCAAAA,CAAQqC,iBAAAA,CAAAA,IAAAA,CAEzBhC,CAAAA,uBAAAA,GAAeL,yCAAAA,CAAQqC,iBAAAA,CAAAA,IACNjC,uBAAAA,CAAAA,CAAOgC,uBAAAA,CAAAA;CAK1B;AAAA,SAASA,uBAAAA,GAAAA;IAAAA,IAAAA,IACJE,EAAAA,EACIF,uBAAAA,CAAAA,GAAAA,GAAyBjC,uBAAAA,CAAce,MAAAA,EAC9CoB,EAAAA,GAAQnC,uBAAAA,CAAcoC,IAAAA,CAAK,SAACC,GAAAA,EAAGC,EAAAA,EAAAA;QAAAA,OAAMD,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAkBC,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA;KAAAA,CAAAA,EACvDtC,uBAAAA,GAAgB,EAAA,EAGhBmC,EAAAA,CAAMI,IAAAA,CAAK,SAAAR,GAAAA,EAAAA;QApGb,IAAyBS,EAAAA,EAMnBC,EAAAA,EACEC,EAAAA,EANHjD,EAAAA,EACHkD,EAAAA,EACAC,EAAAA,AAkGKb;QAAAA,GAAAA,CAAAA,GAAAA,IAnGLY,CAAAA,EAAAA,GADGlD,CAAAA,EAAAA,GADoB+C,CAAAA,EAAAA,GAqGQT,GAAAA,CAAAA,CAAAA,GAAAA,CAAAA,CAAAA,GAAAA,EAlG/Ba,CAAAA,EAAAA,GAAYJ,EAAAA,CAAAA,GAAAA,CAAAA,IAGRC,CAAAA,EAAAA,GAAc,EAAA,EACZC,CAAAA,EAAAA,GAAWpC,uBAAAA,CAAO,CAAA,CAAA,EAAIb,EAAAA,CAAAA,CAAAA,CAAAA,GAAAA,GACPA,EAAAA,CAAAA,GAAAA,GAAkB,CAAA,EAEvCoD,uBAAAA,CACCD,EAAAA,EACAnD,EAAAA,EACAiD,EAAAA,EACAF,EAAAA,CAAAA,GAAAA,EAAAA,KAC8B9C,CAAAA,KAA9BkD,EAAAA,CAAUE,eAAAA,EACU,IAAA,IAApBrD,EAAAA,CAAAA,GAAAA,GAA2B;YAACkD,EAAAA;SAAAA,GAAU,IAAA,EACtCF,EAAAA,EACU,IAAA,IAAVE,EAAAA,GAAiBpB,uBAAAA,CAAc9B,EAAAA,CAAAA,GAASkD,EAAAA,EACxClD,EAAAA,CAAAA,GAAAA,CAAAA,EAEDsD,uBAAAA,CAAWN,EAAAA,EAAahD,EAAAA,CAAAA,EAEpBA,EAAAA,CAAAA,GAAAA,IAAckD,EAAAA,IACjBhB,uBAAAA,CAAwBlC,EAAAA,CAAAA,CAAAA,CAAAA;KAAAA,CAAAA;CEtH3B;AAAA,SAAgBuD,uBAAAA,CACfJ,GAAAA,EACAK,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAE,EAAAA,EACAY,EAAAA,EAAAA;IAVD,IAYKhE,EAAAA,EAAGiE,EAAAA,EAAGd,EAAAA,EAAUe,EAAAA,EAAYC,EAAAA,EAAQC,EAAAA,EAAeC,EAAAA,EAInDC,EAAAA,GAAeV,EAAAA,IAAkBA,EAAAA,CAAAA,GAAAA,IAA6B/C,uBAAAA,EAE9D0D,EAAAA,GAAoBD,EAAAA,CAAY9C,MAAAA;IAAAA,IAEpCmC,EAAAA,CAAAA,GAAAA,GAA2B,EAAA,EACtB3D,EAAAA,GAAI,CAAA,EAAGA,EAAAA,GAAI0D,EAAAA,CAAalC,MAAAA,EAAQxB,EAAAA,EAAAA,CAAAA,IAgDlB,IAAA,IA5CjBkE,CAAAA,EAAAA,GAAaP,EAAAA,CAAAA,GAAAA,CAAyB3D,EAAAA,CAAAA,GADrB,IAAA,IAFlBkE,CAAAA,EAAAA,GAAaR,EAAAA,CAAa1D,EAAAA,CAAAA,CAAAA,IAEqB,SAAA,IAAA,OAAdkE,EAAAA,GACW,IAAA,GAMtB,QAAA,IAAA,OAAdA,EAAAA,IACc,QAAA,IAAA,OAAdA,EAAAA,IAEc,QAAA,IAAA,OAAdA,EAAAA,GAEoCzE,uBAAAA,CAC1C,IAAA,EACAyE,EAAAA,EACA,IAAA,EACA,IAAA,EACAA,EAAAA,CAAAA,GAESM,KAAAA,CAAMC,OAAAA,CAAQP,EAAAA,CAAAA,GACmBzE,uBAAAA,CAC1CoC,yCAAAA,EACA;QAAEP,QAAAA,EAAU4C,EAAAA;KAAAA,EACZ,IAAA,EACA,IAAA,EACA,IAAA,CAAA,GAESA,EAAAA,CAAAA,GAAAA,GAAoB,CAAA,GAKazE,uBAAAA,CAC1CyE,EAAAA,CAAWxE,IAAAA,EACXwE,EAAAA,CAAWvE,KAAAA,EACXuE,EAAAA,CAAWtE,GAAAA,EACX,IAAA,EACAsE,EAAAA,CAAAA,GAAAA,CAAAA,GAG0CA,EAAAA,CAAAA,EAAAA;QAAAA,IAS5CA,EAAAA,CAAAA,EAAAA,GAAqBP,EAAAA,EACrBO,EAAAA,CAAAA,GAAAA,GAAoBP,EAAAA,CAAAA,GAAAA,GAAwB,CAAA,EAS9B,IAAA,KAHdR,CAAAA,EAAAA,GAAWmB,EAAAA,CAAYtE,EAAAA,CAAAA,CAAAA,IAIrBmD,EAAAA,IACAe,EAAAA,CAAWtE,GAAAA,IAAOuD,EAAAA,CAASvD,GAAAA,IAC3BsE,EAAAA,CAAWxE,IAAAA,KAASyD,EAAAA,CAASzD,IAAAA,EAE9B4E,EAAAA,CAAYtE,EAAAA,CAAAA,GAAAA,KAAKG,CAAAA,CAAAA;aAAAA,IAIZ8D,EAAAA,GAAI,CAAA,EAAGA,EAAAA,GAAIM,EAAAA,EAAmBN,EAAAA,EAAAA,CAAK;YAAA,IACvCd,CAAAA,EAAAA,GAAWmB,EAAAA,CAAYL,EAAAA,CAAAA,CAAAA,IAKtBC,EAAAA,CAAWtE,GAAAA,IAAOuD,EAAAA,CAASvD,GAAAA,IAC3BsE,EAAAA,CAAWxE,IAAAA,KAASyD,EAAAA,CAASzD,IAAAA,EAC5B;gBACD4E,EAAAA,CAAYL,EAAAA,CAAAA,GAAAA,KAAK9D,CAAAA,CAAAA;gBAAAA,MAAAA;aAGlBgD;YAAAA,EAAAA,GAAW,IAAA;SAObG;QAAAA,uBAAAA,CACCD,GAAAA,EACAa,EAAAA,EALDf,EAAAA,GAAWA,EAAAA,IAAYvC,uBAAAA,EAOtBiD,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAE,EAAAA,EACAY,EAAAA,CAAAA,EAGDG,EAAAA,GAASD,EAAAA,CAAAA,GAAAA,EAEJD,CAAAA,EAAAA,GAAIC,EAAAA,CAAWnE,GAAAA,CAAAA,IAAQoD,EAAAA,CAASpD,GAAAA,IAAOkE,EAAAA,IACtCI,CAAAA,EAAAA,IAAMA,CAAAA,EAAAA,GAAO,EAAA,CAAA,EACdlB,EAAAA,CAASpD,GAAAA,IAAKsE,EAAAA,CAAK5B,IAAAA,CAAKU,EAAAA,CAASpD,GAAAA,EAAK,IAAA,EAAMmE,EAAAA,CAAAA,EAChDG,EAAAA,CAAK5B,IAAAA,CAAKwB,EAAAA,EAAGC,EAAAA,CAAAA,GAAAA,IAAyBC,EAAAA,EAAQD,EAAAA,CAAAA,CAAAA,EAGjC,IAAA,IAAVC,EAAAA,GACkB,CAAA,IAAA,IAAjBC,EAAAA,IACHA,CAAAA,EAAAA,GAAgBD,EAAAA,CAAAA,EAIU,UAAA,IAAA,OAAnBD,EAAAA,CAAWxE,IAAAA,IAClBwE,EAAAA,CAAAA,GAAAA,KAAyBf,EAAAA,CAAAA,GAAAA,GAEzBe,EAAAA,CAAAA,GAAAA,GAAsBd,EAAAA,GAASsB,uBAAAA,CAC9BR,EAAAA,EACAd,EAAAA,EACAC,GAAAA,CAAAA,GAGDD,EAAAA,GAASuB,uBAAAA,CACRtB,GAAAA,EACAa,EAAAA,EACAf,EAAAA,EACAmB,EAAAA,EACAH,EAAAA,EACAf,EAAAA,CAAAA,EAIgC,UAAA,IAAA,OAAvBO,EAAAA,CAAejE,IAAAA,IAQzBiE,CAAAA,EAAAA,CAAAA,GAAAA,GAA0BP,EAAAA,CAAAA,CAAAA,GAG3BA,EAAAA,IACAD,EAAAA,CAAAA,GAAAA,IAAiBC,EAAAA,IACjBA,EAAAA,CAAOjC,UAAAA,IAAckC,GAAAA,IAIrBD,CAAAA,EAAAA,GAASpB,uBAAAA,CAAcmB,EAAAA,CAAAA,CAAAA;KAAAA;IAAAA,IAIzBQ,EAAAA,CAAAA,GAAAA,GAAsBS,EAAAA,EAGjBpE,EAAAA,GAAIuE,EAAAA,EAAmBvE,EAAAA,EAAAA,EACL,IAAA,IAAlBsE,EAAAA,CAAYtE,EAAAA,CAAAA,IAEgB,CAAA,UAAA,IAAA,OAAvB2D,EAAAA,CAAejE,IAAAA,IACC,IAAA,IAAvB4E,EAAAA,CAAYtE,EAAAA,CAAAA,CAAAA,GAAAA,IACZsE,EAAAA,CAAYtE,EAAAA,CAAAA,CAAAA,GAAAA,IAAW2D,EAAAA,CAAAA,GAAAA,IAKvBA,CAAAA,EAAAA,CAAAA,GAAAA,GAA0B3B,uBAAAA,CAAc4B,EAAAA,EAAgB5D,EAAAA,GAAI,CAAA,CAAA,CAAA,EAG7D4E,uBAAAA,CAAQN,EAAAA,CAAYtE,EAAAA,CAAAA,EAAIsE,EAAAA,CAAYtE,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA;IAAAA,IAKlCqE,EAAAA,EAAAA,IACErE,EAAAA,GAAI,CAAA,EAAGA,EAAAA,GAAIqE,EAAAA,CAAK7C,MAAAA,EAAQxB,EAAAA,EAAAA,CAC5B6E,uBAAAA,CAASR,EAAAA,CAAKrE,EAAAA,CAAAA,EAAIqE,EAAAA,CAAAA,EAAOrE,EAAAA,CAAAA,EAAIqE,EAAAA,CAAAA,EAAOrE,EAAAA,CAAAA,CAAAA;CAKvC;AAAA,SAAS0E,uBAAAA,CAAgBR,GAAAA,EAAYd,GAAAA,EAAQC,EAAAA,EAAAA;IAAAA,IAA7C,IAKMnD,EAAAA,EAHDsC,EAAAA,GAAI0B,GAAAA,CAAAA,GAAAA,EACJY,EAAAA,GAAM,CAAA,EACHtC,EAAAA,IAAKsC,EAAAA,GAAMtC,EAAAA,CAAEhB,MAAAA,EAAQsD,EAAAA,EAAAA,CACvB5E,CAAAA,EAAAA,GAAQsC,EAAAA,CAAEsC,EAAAA,CAAAA,CAAAA,IAMb5E,CAAAA,EAAAA,CAAAA,EAAAA,GAAgBgE,GAAAA,EAGfd,GAAAA,GADwB,UAAA,IAAA,OAAdlD,EAAAA,CAAMR,IAAAA,GACPgF,uBAAAA,CAAgBxE,EAAAA,EAAOkD,GAAAA,EAAQC,EAAAA,CAAAA,GAE/BsB,uBAAAA,CACRtB,EAAAA,EACAnD,EAAAA,EACAA,EAAAA,EACAsC,EAAAA,EACAtC,EAAAA,CAAAA,GAAAA,EACAkD,GAAAA,CAAAA,CAAAA,CAAAA;IAAAA,OAMGA,GAAAA,CAAAA;CASD;AAAA,SAAS2B,yCAAAA,CAAazD,GAAAA,EAAU0D,GAAAA,EAAAA;IAAAA,OACtCA,GAAAA,GAAMA,GAAAA,IAAO,EAAA,EACG,IAAA,IAAZ1D,GAAAA,IAAuC,SAAA,IAAA,OAAZA,GAAAA,IACpBkD,CAAAA,KAAAA,CAAMC,OAAAA,CAAQnD,GAAAA,CAAAA,GACxBA,GAAAA,CAAS0B,IAAAA,CAAK,SAAAX,GAAAA,EAAAA;QACb0C,yCAAAA,CAAa1C,GAAAA,EAAO2C,GAAAA,CAAAA;KAAAA,CAAAA,GAGrBA,GAAAA,CAAIvC,IAAAA,CAAKnB,GAAAA,CAAAA,CAAAA,EAEH0D,GAAAA,CAAAA;CAGR;AAAA,SAASL,uBAAAA,CACRtB,GAAAA,EACAa,GAAAA,EACAf,EAAAA,EACAmB,EAAAA,EACAH,EAAAA,EACAf,EAAAA,EAAAA;IAND,IAQK6B,EAAAA,EAuBGC,EAAAA,EAAiBjB,EAAAA;IAAAA,IAAAA,KAtBI9D,CAAAA,KAAxB+D,GAAAA,CAAAA,GAAAA,EAIHe,EAAAA,GAAUf,GAAAA,CAAAA,GAAAA,EAMVA,GAAAA,CAAAA,GAAAA,GAAAA,KAAsB/D,CAAAA,CAAAA;SAChB,IACM,IAAA,IAAZgD,EAAAA,IACAgB,EAAAA,IAAUf,EAAAA,IACW,IAAA,IAArBe,EAAAA,CAAOhD,UAAAA,EAEPgE,CAAAA,EAAO,IAAc,IAAA,IAAV/B,EAAAA,IAAkBA,EAAAA,CAAOjC,UAAAA,KAAekC,GAAAA,EAClDA,GAAAA,CAAU+B,WAAAA,CAAYjB,EAAAA,CAAAA,EACtBc,EAAAA,GAAU,IAAA,CAAA;SACJ;QAAA,IAGDC,EAAAA,GAAS9B,EAAAA,EAAQa,EAAAA,GAAI,CAAA,EACxBiB,CAAAA,EAAAA,GAASA,EAAAA,CAAOG,WAAAA,CAAAA,IAAgBpB,EAAAA,GAAIK,EAAAA,CAAY9C,MAAAA,EACjDyC,EAAAA,IAAK,CAAA,CAAA,IAEDiB,EAAAA,IAAUf,EAAAA,EAAAA,MACPgB,CAAAA,CAGR9B;QAAAA,GAAAA,CAAUiC,YAAAA,CAAanB,EAAAA,EAAQf,EAAAA,CAAAA,EAC/B6B,EAAAA,GAAU7B,EAAAA;KAAAA;IAAAA,OAAAA,KAOIjD,CAAAA,KAAZ8E,EAAAA,GACMA,EAAAA,GAEAd,EAAAA,CAAOkB,WAAAA,CAAAA;CC9TX;AAAA,SAASE,uBAAAA,CAAUC,GAAAA,EAAKC,GAAAA,EAAUC,EAAAA,EAAU5B,EAAAA,EAAO6B,EAAAA,EAAAA;IAAAA,IACrD3F,EAAAA;IAAAA,IAECA,EAAAA,IAAK0F,EAAAA,CACC,UAAA,KAAN1F,EAAAA,IAA0B,KAAA,KAANA,EAAAA,IAAiBA,EAAAA,IAAKyF,GAAAA,IAC7CG,uBAAAA,CAAYJ,GAAAA,EAAKxF,EAAAA,EAAG,IAAA,EAAM0F,EAAAA,CAAS1F,EAAAA,CAAAA,EAAI8D,EAAAA,CAAAA,CAAAA;IAAAA,IAIpC9D,EAAAA,IAAKyF,GAAAA,CAENE,EAAAA,IAAiC,UAAA,IAAA,OAAfF,GAAAA,CAASzF,EAAAA,CAAAA,IACvB,UAAA,KAANA,EAAAA,IACM,KAAA,KAANA,EAAAA,IACM,OAAA,KAANA,EAAAA,IACM,SAAA,KAANA,EAAAA,IACA0F,EAAAA,CAAS1F,EAAAA,CAAAA,KAAOyF,GAAAA,CAASzF,EAAAA,CAAAA,IAEzB4F,uBAAAA,CAAYJ,GAAAA,EAAKxF,EAAAA,EAAGyF,GAAAA,CAASzF,EAAAA,CAAAA,EAAI0F,EAAAA,CAAS1F,EAAAA,CAAAA,EAAI8D,EAAAA,CAAAA;CAKjD;AAAA,SAAS+B,uBAAAA,CAASC,GAAAA,EAAOlG,GAAAA,EAAKmG,GAAAA,EAAAA;IACd,GAAA,KAAXnG,GAAAA,CAAI,CAAA,CAAA,GACPkG,GAAAA,CAAMF,WAAAA,CAAYhG,GAAAA,EAAKmG,GAAAA,CAAAA,GAEvBD,GAAAA,CAAMlG,GAAAA,CAAAA,GADa,IAAA,IAATmG,GAAAA,GACG,EAAA,GACa,QAAA,IAAA,OAATA,GAAAA,IAAqBjF,uBAAAA,CAAmBkF,IAAAA,CAAKpG,GAAAA,CAAAA,GACjDmG,GAAAA,GAEAA,GAAAA,GAAQ,IAAA;CAYhB;AAAA,SAASH,uBAAAA,CAAYJ,GAAAA,EAAKS,GAAAA,EAAMF,GAAAA,EAAOG,EAAAA,EAAUpC,EAAAA,EAAAA;IAAjD,IACFqC,EAAAA,AAEJC;IAAAA,CAAAA,EAAG,IAAa,OAAA,KAATH,GAAAA,EAAAA;QAAAA,IACc,QAAA,IAAA,OAATF,GAAAA,EACVP,GAAAA,CAAIM,KAAAA,CAAMO,OAAAA,GAAUN,GAAAA,CAAAA;aACd;YAAA,IACiB,QAAA,IAAA,OAAZG,EAAAA,IACVV,CAAAA,GAAAA,CAAIM,KAAAA,CAAMO,OAAAA,GAAUH,EAAAA,GAAW,EAAA,CAAA,EAG5BA,EAAAA,EAAAA,IACED,GAAAA,IAAQC,EAAAA,CACNH,GAAAA,IAASE,GAAAA,IAAQF,GAAAA,IACtBF,uBAAAA,CAASL,GAAAA,CAAIM,KAAAA,EAAOG,GAAAA,EAAM,EAAA,CAAA,CAAA;YAAA,IAKzBF,GAAAA,EAAAA,IACEE,GAAAA,IAAQF,GAAAA,CACPG,EAAAA,IAAYH,GAAAA,CAAME,GAAAA,CAAAA,KAAUC,EAAAA,CAASD,GAAAA,CAAAA,IACzCJ,uBAAAA,CAASL,GAAAA,CAAIM,KAAAA,EAAOG,GAAAA,EAAMF,GAAAA,CAAME,GAAAA,CAAAA,CAAAA;SAAAA;WAOhC,IAAgB,GAAA,KAAZA,GAAAA,CAAK,CAAA,CAAA,IAA0B,GAAA,KAAZA,GAAAA,CAAK,CAAA,CAAA,EAChCE,EAAAA,GAAaF,GAAAA,KAAUA,CAAAA,GAAAA,GAAOA,GAAAA,CAAKK,OAAAA,CAAAA,YAAoB,EAAA,CAAA,CAAA,EAGxBL,GAAAA,GAA3BA,GAAAA,CAAKM,WAAAA,EAAAA,IAAiBf,GAAAA,GAAYS,GAAAA,CAAKM,WAAAA,EAAAA,CAAchG,KAAAA,CAAM,CAAA,CAAA,GACnD0F,GAAAA,CAAK1F,KAAAA,CAAM,CAAA,CAAA,EAElBiF,GAAAA,CAAIgB,CAAAA,IAAYhB,CAAAA,GAAAA,CAAIgB,CAAAA,GAAa,CAAA,CAAA,CAAA,EACtChB,GAAAA,CAAIgB,CAAAA,CAAWP,GAAAA,GAAOE,EAAAA,CAAAA,GAAcJ,GAAAA,EAEhCA,GAAAA,GACEG,EAAAA,IAEJV,GAAAA,CAAIiB,gBAAAA,CAAiBR,GAAAA,EADLE,EAAAA,GAAaO,uBAAAA,GAAoBC,uBAAAA,EACbR,EAAAA,CAAAA,GAIrCX,GAAAA,CAAIoB,mBAAAA,CAAoBX,GAAAA,EADRE,EAAAA,GAAaO,uBAAAA,GAAoBC,uBAAAA,EACVR,EAAAA,CAAAA,CAAAA;SAElC,IAAa,yBAAA,KAATF,GAAAA,EAAoC;QAAA,IAC1CnC,EAAAA,EAIHmC,GAAAA,GAAOA,GAAAA,CAAKK,OAAAA,CAAAA,cAAsB,GAAA,CAAA,CAAKA,OAAAA,CAAAA,UAAkB,GAAA,CAAA,CAAA;aACnD,IACG,MAAA,KAATL,GAAAA,IACS,MAAA,KAATA,GAAAA,IACS,MAAA,KAATA,GAAAA,IAGS,UAAA,KAATA,GAAAA,IACS,UAAA,KAATA,GAAAA,IACAA,GAAAA,IAAQT,GAAAA,EAAAA,IAAAA;YAGPA,GAAAA,CAAIS,GAAAA,CAAAA,GAAiB,IAAA,IAATF,GAAAA,GAAgB,EAAA,GAAKA,GAAAA,CAAAA;YAAAA,MAE3BK,CAAAA,CAAAA;SACL,CAAA,OAAOS,CAAAA,EAAAA,CAAAA,CAUW;QAAA,UAAA,IAAA,OAAVd,GAAAA,IAGD,CAAA,IAAA,IAATA,GAAAA,IAAAA,CAAAA,CACW,CAAA,KAAVA,GAAAA,IAAgC,GAAA,KAAZE,GAAAA,CAAK,CAAA,CAAA,IAA0B,GAAA,KAAZA,GAAAA,CAAK,CAAA,CAAA,CAAA,GAE7CT,GAAAA,CAAIsB,YAAAA,CAAab,GAAAA,EAAMF,GAAAA,CAAAA,GAEvBP,GAAAA,CAAIuB,eAAAA,CAAgBd,GAAAA,CAAAA,CAAAA;KAAAA;CAUvB;AAAA,SAASU,uBAAAA,CAAWE,GAAAA,EAAAA;IAAAA,IAAAA,CACdL,CAAAA,CAAWK,GAAAA,CAAEnH,IAAAA,GAAAA,CAAO,CAAA,CAAA,CAAOY,yCAAAA,CAAQ0G,KAAAA,GAAQ1G,yCAAAA,CAAQ0G,KAAAA,CAAMH,GAAAA,CAAAA,GAAKA,GAAAA,CAAAA;CAGpE;AAAA,SAASH,uBAAAA,CAAkBG,GAAAA,EAAAA;IAAAA,IAAAA,CACrBL,CAAAA,CAAWK,GAAAA,CAAEnH,IAAAA,GAAAA,CAAO,CAAA,CAAA,CAAMY,yCAAAA,CAAQ0G,KAAAA,GAAQ1G,yCAAAA,CAAQ0G,KAAAA,CAAMH,GAAAA,CAAAA,GAAKA,GAAAA,CAAAA;CCpInE;AAAA,SAAgBvD,uBAAAA,CACfD,GAAAA,EACA4D,GAAAA,EACA9D,EAAAA,EACAU,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAE,EAAAA,EACAY,EAAAA,EAAAA;IATD,IAWKc,EAAAA,EAoBEtC,EAAAA,EAAG0E,EAAAA,EAAOxB,EAAAA,EAAUyB,EAAAA,EAAUC,EAAAA,EAAUC,EAAAA,EACxC5B,EAAAA,EAKA6B,EAAAA,EACAC,EAAAA,EAqIA7D,EAAAA,EA/JL8D,EAAAA,GAAUP,GAAAA,CAASvH,IAAAA;IAAAA,IAAAA,KAISS,CAAAA,KAAzB8G,GAAAA,CAAS7G,WAAAA,EAA2B,OAAO,IAAA,CAGpB;IAAA,IAAA,IAAvB+C,EAAAA,CAAAA,GAAAA,IACHa,CAAAA,EAAAA,GAAcb,EAAAA,CAAAA,GAAAA,EACdC,EAAAA,GAAS6D,GAAAA,CAAAA,GAAAA,GAAgB9D,EAAAA,CAAAA,GAAAA,EAEzB8D,GAAAA,CAAAA,GAAAA,GAAsB,IAAA,EACtBlD,EAAAA,GAAoB;QAACX,EAAAA;KAAAA,CAAAA,EAGjB0B,CAAAA,EAAAA,GAAMxE,yCAAAA,CAAAA,GAAAA,CAAAA,IAAgBwE,EAAAA,CAAImC,GAAAA,CAAAA,CAAAA;IAAAA,IAAAA;QAG9B9B,CAAAA,EAAO,IAAsB,UAAA,IAAA,OAAXqC,EAAAA,EAAuB;YAAA,IAEpC/B,EAAAA,GAAWwB,GAAAA,CAAStH,KAAAA,EAKpB2H,EAAAA,GADJxC,CAAAA,EAAAA,GAAM0C,EAAAA,CAAQC,WAAAA,CAAAA,IACQ5D,EAAAA,CAAciB,EAAAA,CAAAA,GAAAA,CAAAA,EAChCyC,EAAAA,GAAmBzC,EAAAA,GACpBwC,EAAAA,GACCA,EAAAA,CAAS3H,KAAAA,CAAMoG,KAAAA,GACfjB,EAAAA,CAAAA,EAAAA,GACDjB,EAAAA,EAGCV,EAAAA,CAAAA,GAAAA,GAEHkE,EAAAA,GADA7E,CAAAA,EAAAA,GAAIyE,GAAAA,CAAAA,GAAAA,GAAsB9D,EAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAAA,GAC0BX,EAAAA,CAAAA,GAAAA,GAGhD,CAAA,WAAA,IAAegF,EAAAA,IAAWA,EAAAA,CAAQE,SAAAA,CAAUC,MAAAA,GAE/CV,GAAAA,CAAAA,GAAAA,GAAsBzE,EAAAA,GAAI,IAAIgF,EAAAA,CAAQ/B,EAAAA,EAAU8B,EAAAA,CAAAA,GAGhDN,CAAAA,GAAAA,CAAAA,GAAAA,GAAsBzE,EAAAA,GAAI,IAAIV,yCAAAA,CAAU2D,EAAAA,EAAU8B,EAAAA,CAAAA,EAClD/E,EAAAA,CAAEpC,WAAAA,GAAcoH,EAAAA,EAChBhF,EAAAA,CAAEmF,MAAAA,GAASC,uBAAAA,CAAAA,EAERN,EAAAA,IAAUA,EAAAA,CAASO,GAAAA,CAAIrF,EAAAA,CAAAA,EAE3BA,EAAAA,CAAE7C,KAAAA,GAAQ8F,EAAAA,EACLjD,EAAAA,CAAEsF,KAAAA,IAAOtF,CAAAA,EAAAA,CAAEsF,KAAAA,GAAQ,CAAA,CAAA,CAAA,EACxBtF,EAAAA,CAAET,OAAAA,GAAUwF,EAAAA,EACZ/E,EAAAA,CAAAA,GAAAA,GAAmBqB,EAAAA,EACnBqD,EAAAA,GAAQ1E,EAAAA,CAAAA,GAAAA,GAAAA,CAAW,CAAA,EACnBA,EAAAA,CAAAA,GAAAA,GAAqB,EAAA,CAAA,EAIF,IAAA,IAAhBA,EAAAA,CAAAA,GAAAA,IACHA,CAAAA,EAAAA,CAAAA,GAAAA,GAAeA,EAAAA,CAAEsF,KAAAA,CAAAA,EAEsB,IAAA,IAApCN,EAAAA,CAAQO,wBAAAA,IACPvF,CAAAA,EAAAA,CAAAA,GAAAA,IAAgBA,EAAAA,CAAEsF,KAAAA,IACrBtF,CAAAA,EAAAA,CAAAA,GAAAA,GAAezB,uBAAAA,CAAO,CAAA,CAAA,EAAIyB,EAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAG3BzB,uBAAAA,CACCyB,EAAAA,CAAAA,GAAAA,EACAgF,EAAAA,CAAQO,wBAAAA,CAAyBtC,EAAAA,EAAUjD,EAAAA,CAAAA,GAAAA,CAAAA,CAAAA,CAAAA,EAI7CkD,EAAAA,GAAWlD,EAAAA,CAAE7C,KAAAA,EACbwH,EAAAA,GAAW3E,EAAAA,CAAEsF,KAAAA,EAGTZ,EAAAA,EAEkC,IAAA,IAApCM,EAAAA,CAAQO,wBAAAA,IACgB,IAAA,IAAxBvF,EAAAA,CAAEwF,kBAAAA,IAEFxF,EAAAA,CAAEwF,kBAAAA,EAAAA,EAGwB,IAAA,IAAvBxF,EAAAA,CAAEyF,iBAAAA,IACLzF,EAAAA,CAAAA,GAAAA,CAAmBC,IAAAA,CAAKD,EAAAA,CAAEyF,iBAAAA,CAAAA,CAAAA;iBAErB;gBAAA,IAE+B,IAAA,IAApCT,EAAAA,CAAQO,wBAAAA,IACRtC,EAAAA,KAAaC,EAAAA,IACkB,IAAA,IAA/BlD,EAAAA,CAAE0F,yBAAAA,IAEF1F,EAAAA,CAAE0F,yBAAAA,CAA0BzC,EAAAA,EAAU8B,EAAAA,CAAAA,EAAAA,CAIpC/E,EAAAA,CAAAA,GAAAA,IAC0B,IAAA,IAA3BA,EAAAA,CAAE2F,qBAAAA,IAAAA,CAKI,CAAA,KAJN3F,EAAAA,CAAE2F,qBAAAA,CACD1C,EAAAA,EACAjD,EAAAA,CAAAA,GAAAA,EACA+E,EAAAA,CAAAA,IAEFN,GAAAA,CAAAA,GAAAA,KAAuB9D,EAAAA,CAAAA,GAAAA,EACtB;oBACDX,EAAAA,CAAE7C,KAAAA,GAAQ8F,EAAAA,EACVjD,EAAAA,CAAEsF,KAAAA,GAAQtF,EAAAA,CAAAA,GAAAA,EAENyE,GAAAA,CAAAA,GAAAA,KAAuB9D,EAAAA,CAAAA,GAAAA,IAAoBX,CAAAA,EAAAA,CAAAA,GAAAA,GAAAA,CAAW,CAAA,CAAA,EAC1DA,EAAAA,CAAAA,GAAAA,GAAWyE,GAAAA,EACXA,GAAAA,CAAAA,GAAAA,GAAgB9D,EAAAA,CAAAA,GAAAA,EAChB8D,GAAAA,CAAAA,GAAAA,GAAqB9D,EAAAA,CAAAA,GAAAA,EACrB8D,GAAAA,CAAAA,GAAAA,CAAmBmB,OAAAA,CAAQ,SAAAlI,GAAAA,EAAAA;wBACtBA,GAAAA,IAAOA,CAAAA,GAAAA,CAAAA,EAAAA,GAAgB+G,GAAAA,CAAAA;qBAAAA,CAAAA,EAExBzE,EAAAA,CAAAA,GAAAA,CAAmBhB,MAAAA,IACtB0B,EAAAA,CAAYT,IAAAA,CAAKD,EAAAA,CAAAA,CAAAA;oBAAAA,MAGZ2C,CAAAA,CAAAA;iBAGsB;gBAAA,IAAA,IAAzB3C,EAAAA,CAAE6F,mBAAAA,IACL7F,EAAAA,CAAE6F,mBAAAA,CAAoB5C,EAAAA,EAAUjD,EAAAA,CAAAA,GAAAA,EAAc+E,EAAAA,CAAAA,EAGnB,IAAA,IAAxB/E,EAAAA,CAAE8F,kBAAAA,IACL9F,EAAAA,CAAAA,GAAAA,CAAmBC,IAAAA,CAAK,WAAA;oBACvBD,EAAAA,CAAE8F,kBAAAA,CAAmB5C,EAAAA,EAAUyB,EAAAA,EAAUC,EAAAA,CAAAA;iBAAAA,CAAAA;aAK5C5E;YAAAA,EAAAA,CAAET,OAAAA,GAAUwF,EAAAA,EACZ/E,EAAAA,CAAE7C,KAAAA,GAAQ8F,EAAAA,EACVjD,EAAAA,CAAEsF,KAAAA,GAAQtF,EAAAA,CAAAA,GAAAA,EAELsC,CAAAA,EAAAA,GAAMxE,yCAAAA,CAAAA,GAAAA,CAAAA,IAAkBwE,EAAAA,CAAImC,GAAAA,CAAAA,EAEjCzE,EAAAA,CAAAA,GAAAA,GAAAA,CAAW,CAAA,EACXA,EAAAA,CAAAA,GAAAA,GAAWyE,GAAAA,EACXzE,EAAAA,CAAAA,GAAAA,GAAea,GAAAA,EAEfyB,EAAAA,GAAMtC,EAAAA,CAAEmF,MAAAA,CAAOnF,EAAAA,CAAE7C,KAAAA,EAAO6C,EAAAA,CAAEsF,KAAAA,EAAOtF,EAAAA,CAAET,OAAAA,CAAAA,EAGnCS,EAAAA,CAAEsF,KAAAA,GAAQtF,EAAAA,CAAAA,GAAAA,EAEe,IAAA,IAArBA,EAAAA,CAAE+F,eAAAA,IACL1E,CAAAA,EAAAA,GAAgB9C,uBAAAA,CAAOA,uBAAAA,CAAO,CAAA,CAAA,EAAI8C,EAAAA,CAAAA,EAAgBrB,EAAAA,CAAE+F,eAAAA,EAAAA,CAAAA,CAAAA,EAGhDrB,EAAAA,IAAsC,IAAA,IAA7B1E,EAAAA,CAAEgG,uBAAAA,IACfpB,CAAAA,EAAAA,GAAW5E,EAAAA,CAAEgG,uBAAAA,CAAwB9C,EAAAA,EAAUyB,EAAAA,CAAAA,CAAAA,EAK5CzD,EAAAA,GADI,IAAA,IAAPoB,EAAAA,IAAeA,EAAAA,CAAIpF,IAAAA,KAASmC,yCAAAA,IAAuB,IAAA,IAAXiD,EAAAA,CAAIlF,GAAAA,GACLkF,EAAAA,CAAInF,KAAAA,CAAM2B,QAAAA,GAAWwD,EAAAA,EAE7DrB,uBAAAA,CACCJ,GAAAA,EACAmB,KAAAA,CAAMC,OAAAA,CAAQf,EAAAA,CAAAA,GAAgBA,EAAAA,GAAe;gBAACA,EAAAA;aAAAA,EAC9CuD,GAAAA,EACA9D,EAAAA,EACAU,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAE,EAAAA,EACAY,EAAAA,CAAAA,EAGDxB,EAAAA,CAAEF,IAAAA,GAAO2E,GAAAA,CAAAA,GAAAA,EAGTA,GAAAA,CAAAA,GAAAA,GAAsB,IAAA,EAElBzE,EAAAA,CAAAA,GAAAA,CAAmBhB,MAAAA,IACtB0B,EAAAA,CAAYT,IAAAA,CAAKD,EAAAA,CAAAA,EAGd6E,EAAAA,IACH7E,CAAAA,EAAAA,CAAAA,GAAAA,GAAkBA,EAAAA,CAAAA,EAAAA,GAAyB,IAAA,CAAA,EAG5CA,EAAAA,CAAAA,GAAAA,GAAAA,CAAW,CAAA;SAAA,MAEU,IAAA,IAArBuB,EAAAA,IACAkD,GAAAA,CAAAA,GAAAA,KAAuB9D,EAAAA,CAAAA,GAAAA,GAEvB8D,CAAAA,GAAAA,CAAAA,GAAAA,GAAqB9D,EAAAA,CAAAA,GAAAA,EACrB8D,GAAAA,CAAAA,GAAAA,GAAgB9D,EAAAA,CAAAA,GAAAA,CAAAA,GAEhB8D,GAAAA,CAAAA,GAAAA,GAAgBwB,uBAAAA,CACftF,EAAAA,CAAAA,GAAAA,EACA8D,GAAAA,EACA9D,EAAAA,EACAU,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAc,EAAAA,CAAAA,CAAAA;QAIGc,CAAAA,EAAAA,GAAMxE,yCAAAA,CAAQoI,MAAAA,CAAAA,IAAS5D,EAAAA,CAAImC,GAAAA,CAAAA;KAC/B,CAAA,OAAOJ,GAAAA,EAAAA;QACRI,GAAAA,CAAAA,GAAAA,GAAqB,IAAA,EAEjBjD,CAAAA,EAAAA,IAAoC,IAAA,IAArBD,EAAAA,CAAAA,IAClBkD,CAAAA,GAAAA,CAAAA,GAAAA,GAAgB7D,EAAAA,EAChB6D,GAAAA,CAAAA,GAAAA,GAAAA,CAAAA,CAAwBjD,EAAAA,EACxBD,EAAAA,CAAkBA,EAAAA,CAAkB7B,OAAAA,CAAQkB,EAAAA,CAAAA,CAAAA,GAAW,IAAA,CAAA,EAIxD9C,yCAAAA,CAAAA,GAAAA,CAAoBuG,GAAAA,EAAGI,GAAAA,EAAU9D,EAAAA,CAAAA;KAAAA;CAS5B;AAAA,SAASK,uBAAAA,CAAWN,GAAAA,EAAayF,GAAAA,EAAAA;IACnCrI,yCAAAA,CAAAA,GAAAA,IAAiBA,yCAAAA,CAAAA,GAAAA,CAAgBqI,GAAAA,EAAMzF,GAAAA,CAAAA,EAE3CA,GAAAA,CAAYF,IAAAA,CAAK,SAAAR,GAAAA,EAAAA;QAAAA,IAAAA;YAGfU,GAAAA,GAAcV,GAAAA,CAAAA,GAAAA,EACdA,GAAAA,CAAAA,GAAAA,GAAqB,EAAA,EACrBU,GAAAA,CAAYF,IAAAA,CAAK,SAAA4F,GAAAA,EAAAA;gBAEhBA,GAAAA,CAAGnH,IAAAA,CAAKe,GAAAA,CAAAA;aAAAA,CAAAA;SAER,CAAA,OAAOqE,GAAAA,EAAAA;YACRvG,yCAAAA,CAAAA,GAAAA,CAAoBuG,GAAAA,EAAGrE,GAAAA,CAAAA,GAAAA,CAAAA;SAAAA;KAAAA,CAAAA;CAmB1B;AAAA,SAASiG,uBAAAA,CACRjD,GAAAA,EACAyB,GAAAA,EACA9D,GAAAA,EACAU,GAAAA,EACAC,GAAAA,EACAC,EAAAA,EACAb,EAAAA,EACAc,EAAAA,EAAAA;IARD,IAoBS3B,EAAAA,EAsDHwG,EAAAA,EACAC,EAAAA,EAjEDpD,EAAAA,GAAWvC,GAAAA,CAASxD,KAAAA,EACpB8F,EAAAA,GAAWwB,GAAAA,CAAStH,KAAAA,EACpBoJ,EAAAA,GAAW9B,GAAAA,CAASvH,IAAAA,EACpBM,EAAAA,GAAI,CAAA;IAAA,IAGS,KAAA,KAAb+I,EAAAA,IAAoBjF,CAAAA,GAAAA,GAAAA,CAAQ,CAAA,CAAA,EAEP,IAAA,IAArBC,EAAAA,EAAAA;QAAAA,MACI/D,EAAAA,GAAI+D,EAAAA,CAAkBvC,MAAAA,EAAQxB,EAAAA,EAAAA,CAAAA,IAC9BqC,CAAAA,EAAAA,GAAQ0B,EAAAA,CAAkB/D,EAAAA,CAAAA,CAAAA,IAO/B,cAAA,IAAkBqC,EAAAA,IAAAA,CAAAA,CAAY0G,EAAAA,IAC7BA,CAAAA,EAAAA,GAAW1G,EAAAA,CAAM2G,SAAAA,KAAcD,EAAAA,GAA8B,CAAA,KAAnB1G,EAAAA,CAAM0G,QAAAA,CAAAA,EAChD;YACDvD,GAAAA,GAAMnD,EAAAA,EACN0B,EAAAA,CAAkB/D,EAAAA,CAAAA,GAAK,IAAA,CAAA;YAAA,MAAA;SAAA;KAAA;IAAA,IAMf,IAAA,IAAPwF,GAAAA,EAAa;QAAA,IACC,IAAA,KAAbuD,EAAAA,EAAAA,OAEIE,QAAAA,CAASC,cAAAA,CAAezD,EAAAA,CAAAA,CAI/BD;QAAAA,GAAAA,GADG1B,GAAAA,GACGmF,QAAAA,CAASE,eAAAA,CACd,4BAAA,EAEAJ,EAAAA,CAAAA,GAGKE,QAAAA,CAAS5H,aAAAA,CAEd0H,EAAAA,EACAtD,EAAAA,CAAS2D,EAAAA,IAAM3D,EAAAA,CAAAA,EAKjB1B,EAAAA,GAAoB,IAAA,EAEpBC,EAAAA,GAAAA,CAAc,CAAA;KAAA;IAAA,IAGE,IAAA,KAAb+E,EAAAA,EAECrD,EAAAA,KAAaD,EAAAA,IAAczB,EAAAA,IAAewB,GAAAA,CAAI6D,IAAAA,KAAS5D,EAAAA,IAC1DD,CAAAA,GAAAA,CAAI6D,IAAAA,GAAO5D,EAAAA,CAAAA,CAAAA;SAEN;QAAA,IAEN1B,EAAAA,GAAoBA,EAAAA,IAAqBxD,uBAAAA,CAAMkB,IAAAA,CAAK+D,GAAAA,CAAI8D,UAAAA,CAAAA,EAIpDT,EAAAA,GAFJnD,CAAAA,EAAAA,GAAWvC,GAAAA,CAASxD,KAAAA,IAASiB,uBAAAA,CAAAA,CAEN2I,uBAAAA,EACnBT,EAAAA,GAAUrD,EAAAA,CAAS8D,uBAAAA,EAAAA,CAIlBvF,EAAAA,EAAa;YAAA,IAGQ,IAAA,IAArBD,EAAAA,EAAAA,IACH2B,EAAAA,GAAW,CAAA,CAAA,EACN1F,EAAAA,GAAI,CAAA,EAAGA,EAAAA,GAAIwF,GAAAA,CAAIgE,UAAAA,CAAWhI,MAAAA,EAAQxB,EAAAA,EAAAA,CACtC0F,EAAAA,CAASF,GAAAA,CAAIgE,UAAAA,CAAWxJ,EAAAA,CAAAA,CAAGiG,IAAAA,CAAAA,GAAQT,GAAAA,CAAIgE,UAAAA,CAAWxJ,EAAAA,CAAAA,CAAG+F,KAAAA,CAAAA;YAInD+C,CAAAA,EAAAA,IAAWD,EAAAA,CAAAA,IAGZC,CAAAA,EAAAA,IACED,CAAAA,EAAAA,IAAWC,EAAAA,CAAAA,MAAAA,IAAkBD,EAAAA,CAAAA,MAAAA,IAC/BC,EAAAA,CAAAA,MAAAA,KAAmBtD,GAAAA,CAAIiE,SAAAA,CAAAA,IAExBjE,CAAAA,GAAAA,CAAIiE,SAAAA,GAAaX,EAAAA,IAAWA,EAAAA,CAAAA,MAAAA,IAAmB,EAAA,CAAA,CAAA;SAAA;QAAA,IAKlDvD,uBAAAA,CAAUC,GAAAA,EAAKC,EAAAA,EAAUC,EAAAA,EAAU5B,GAAAA,EAAOE,EAAAA,CAAAA,EAGtC8E,EAAAA,EACH7B,GAAAA,CAAAA,GAAAA,GAAqB,EAAA,CAAA;aAAA,IAErBjH,EAAAA,GAAIiH,GAAAA,CAAStH,KAAAA,CAAM2B,QAAAA,EACnBmC,uBAAAA,CACC+B,GAAAA,EACAhB,KAAAA,CAAMC,OAAAA,CAAQzE,EAAAA,CAAAA,GAAKA,EAAAA,GAAI;YAACA,EAAAA;SAAAA,EACxBiH,GAAAA,EACA9D,GAAAA,EACAU,GAAAA,EACAC,GAAAA,IAAsB,eAAA,KAAbiF,EAAAA,EACThF,EAAAA,EACAb,EAAAA,EACAa,EAAAA,GACGA,EAAAA,CAAkB,CAAA,CAAA,GAClBZ,GAAAA,CAAAA,GAAAA,IAAsBnB,uBAAAA,CAAcmB,GAAAA,EAAU,CAAA,CAAA,EACjDa,EAAAA,CAAAA,EAIwB,IAAA,IAArBD,EAAAA,EAAAA,IACE/D,EAAAA,GAAI+D,EAAAA,CAAkBvC,MAAAA,EAAQxB,EAAAA,EAAAA,EACN,IAAA,IAAxB+D,EAAAA,CAAkB/D,EAAAA,CAAAA,IAAYiB,uBAAAA,CAAW8C,EAAAA,CAAkB/D,EAAAA,CAAAA,CAAAA,CAM7DgE;QAAAA,EAAAA,IAEH,CAAA,OAAA,IAAWyB,EAAAA,IAAAA,KACctF,CAAAA,KAAxBH,CAAAA,EAAAA,GAAIyF,EAAAA,CAASM,KAAAA,CAAAA,IAKb/F,CAAAA,EAAAA,KAAM0F,EAAAA,CAASK,KAAAA,IACf/F,EAAAA,KAAMwF,GAAAA,CAAIO,KAAAA,IACI,UAAA,KAAbgD,EAAAA,IAAAA,CAA4B/I,EAAAA,CAAAA,IAE9B4F,uBAAAA,CAAYJ,GAAAA,EAAK,OAAA,EAASxF,EAAAA,EAAG0F,EAAAA,CAASK,KAAAA,EAAAA,CAAO,CAAA,CAAA,EAG7C,SAAA,IAAaN,EAAAA,IAAAA,KACctF,CAAAA,KAA1BH,CAAAA,EAAAA,GAAIyF,EAAAA,CAASiE,OAAAA,CAAAA,IACd1J,EAAAA,KAAMwF,GAAAA,CAAIkE,OAAAA,IAEV9D,uBAAAA,CAAYJ,GAAAA,EAAK,SAAA,EAAWxF,EAAAA,EAAG0F,EAAAA,CAASgE,OAAAA,EAAAA,CAAS,CAAA,CAAA,CAAA;KAAA;IAAA,OAK7ClE,GAAAA,CAAAA;CASR;AAAA,SAAgBX,uBAAAA,CAAS9E,GAAAA,EAAKgG,GAAAA,EAAO7F,GAAAA,EAAAA;IAAAA,IAAAA;QAEjB,UAAA,IAAA,OAAPH,GAAAA,GAAmBA,GAAAA,CAAIgG,GAAAA,CAAAA,GAC7BhG,GAAAA,CAAI6B,OAAAA,GAAUmE,GAAAA;KAClB,CAAA,OAAOc,GAAAA,EAAAA;QACRvG,yCAAAA,CAAAA,GAAAA,CAAoBuG,GAAAA,EAAG3G,GAAAA,CAAAA;KAAAA;CAYzB;AAAA,SAAgB0E,uBAAAA,CAAQ1E,GAAAA,EAAOyJ,GAAAA,EAAaC,GAAAA,EAAAA;IAA5C,IACKC,GAAAA,EAoBM7J,GAAAA;IAAAA,IAnBNM,yCAAAA,CAAQsE,OAAAA,IAAStE,yCAAAA,CAAQsE,OAAAA,CAAQ1E,GAAAA,CAAAA,EAEhC2J,CAAAA,GAAAA,GAAI3J,GAAAA,CAAMH,GAAAA,CAAAA,IACT8J,CAAAA,GAAAA,CAAEjI,OAAAA,IAAWiI,GAAAA,CAAEjI,OAAAA,KAAY1B,GAAAA,CAAAA,GAAAA,IAAY2E,uBAAAA,CAASgF,GAAAA,EAAG,IAAA,EAAMF,GAAAA,CAAAA,CAAAA,EAGjC,IAAA,IAAzBE,CAAAA,GAAAA,GAAI3J,GAAAA,CAAAA,GAAAA,CAAAA,EAA2B;QAAA,IAC/B2J,GAAAA,CAAEC,oBAAAA,EAAAA,IAAAA;YAEJD,GAAAA,CAAEC,oBAAAA,EAAAA;SACD,CAAA,OAAOjD,GAAAA,EAAAA;YACRvG,yCAAAA,CAAAA,GAAAA,CAAoBuG,GAAAA,EAAG8C,GAAAA,CAAAA;SAIzBE;QAAAA,GAAAA,CAAEvH,IAAAA,GAAOuH,GAAAA,CAAAA,GAAAA,GAAe,IAAA;KAAA;IAAA,IAGpBA,GAAAA,GAAI3J,GAAAA,CAAAA,GAAAA,EAAAA,IACCF,GAAAA,GAAI,CAAA,EAAGA,GAAAA,GAAI6J,GAAAA,CAAErI,MAAAA,EAAQxB,GAAAA,EAAAA,CACzB6J,GAAAA,CAAE7J,GAAAA,CAAAA,IACL4E,uBAAAA,CAAQiF,GAAAA,CAAE7J,GAAAA,CAAAA,EAAI2J,GAAAA,EAAkC,UAAA,IAAA,OAAdzJ,GAAAA,CAAMR,IAAAA,CAAAA,CAKtCkK;IAAAA,GAAAA,IAA4B,IAAA,IAAd1J,GAAAA,CAAAA,GAAAA,IAAoBe,uBAAAA,CAAWf,GAAAA,CAAAA,GAAAA,CAAAA,EAIlDA,GAAAA,CAAAA,GAAAA,GAAaA,GAAAA,CAAAA,GAAAA,GAAAA,KAAiBC,CAAAA;CAI/B;AAAA,SAASyH,uBAAAA,CAASjI,GAAAA,EAAOmI,CAAAA,EAAO/F,GAAAA,EAAAA;IAAAA,OACxBgI,IAAAA,CAAK3J,WAAAA,CAAYT,GAAAA,EAAOoC,GAAAA,CAAAA,CAAAA;CCpfhC;AAAA,SAAgB4F,yCAAAA,CAAOzH,GAAAA,EAAOmD,GAAAA,EAAW2G,GAAAA,EAAAA;IAAzC,IAMKhG,GAAAA,EAOAb,EAAAA,EAUAD,EAAAA,AAtBA5C;IAAAA,yCAAAA,CAAAA,EAAAA,IAAeA,yCAAAA,CAAAA,EAAAA,CAAcJ,GAAAA,EAAOmD,GAAAA,CAAAA,EAYpCF,EAAAA,GAPAa,CAAAA,GAAAA,GAAqC,UAAA,IAAA,OAAhBgG,GAAAA,CAAAA,GAQtB,IAAA,GACCA,GAAAA,IAAeA,GAAAA,CAAAA,GAAAA,IAA0B3G,GAAAA,CAAAA,GAAAA,EAQzCH,EAAAA,GAAc,EAAA,EAClBI,uBAAAA,CACCD,GAAAA,EARDnD,GAAAA,GAAAA,CAAAA,CACG8D,GAAAA,IAAegG,GAAAA,IACjB3G,GAAAA,CAAAA,CAAAA,GAAAA,GACahC,yCAAAA,CAAcQ,yCAAAA,EAAU,IAAA,EAAM;QAAC3B,GAAAA;KAAAA,CAAAA,EAS5CiD,EAAAA,IAAYvC,uBAAAA,EACZA,uBAAAA,EAAAA,KAC8BT,CAAAA,KAA9BkD,GAAAA,CAAUE,eAAAA,EAAAA,CACTS,GAAAA,IAAegG,GAAAA,GACb;QAACA,GAAAA;KAAAA,GACD7G,EAAAA,GACA,IAAA,GACAE,GAAAA,CAAU4G,UAAAA,GACV1J,uBAAAA,CAAMkB,IAAAA,CAAK4B,GAAAA,CAAUiG,UAAAA,CAAAA,GACrB,IAAA,EACHpG,EAAAA,EAAAA,CACCc,GAAAA,IAAegG,GAAAA,GACbA,GAAAA,GACA7G,EAAAA,GACAA,EAAAA,CAAAA,GAAAA,GACAE,GAAAA,CAAU4G,UAAAA,EACbjG,GAAAA,CAAAA,EAIDR,uBAAAA,CAAWN,EAAAA,EAAahD,GAAAA,CAAAA;CASlB;AAAA,SAASyF,yCAAAA,CAAQzF,GAAAA,EAAOmD,GAAAA,EAAAA;IAC9BsE,yCAAAA,CAAOzH,GAAAA,EAAOmD,GAAAA,EAAWsC,yCAAAA,CAAAA;CC/D1B;AAAA,SAAgBuE,yCAAAA,CAAahK,GAAAA,EAAOP,GAAAA,EAAO2B,GAAAA,EAAAA;IAAAA,IAEzC1B,GAAAA,EACAG,GAAAA,EACAC,EAAAA,EAHGC,EAAAA,GAAkBc,uBAAAA,CAAO,CAAA,CAAA,EAAIb,GAAAA,CAAMP,KAAAA,CAAAA;IAAAA,IAIlCK,EAAAA,IAAKL,GAAAA,CACA,KAAA,IAALK,EAAAA,GAAYJ,GAAAA,GAAMD,GAAAA,CAAMK,EAAAA,CAAAA,GACd,KAAA,IAALA,EAAAA,GAAYD,GAAAA,GAAMJ,GAAAA,CAAMK,EAAAA,CAAAA,GAC5BC,EAAAA,CAAgBD,EAAAA,CAAAA,GAAKL,GAAAA,CAAMK,EAAAA,CAAAA,CAAAA;IAAAA,OAG7BuB,SAAAA,CAAUC,MAAAA,GAAS,CAAA,IACtBvB,CAAAA,EAAAA,CAAgBqB,QAAAA,GACfC,SAAAA,CAAUC,MAAAA,GAAS,CAAA,GAAIjB,uBAAAA,CAAMkB,IAAAA,CAAKF,SAAAA,EAAW,CAAA,CAAA,GAAKD,GAAAA,CAAAA,EAG7C7B,uBAAAA,CACNS,GAAAA,CAAMR,IAAAA,EACNO,EAAAA,EACAL,GAAAA,IAAOM,GAAAA,CAAMN,GAAAA,EACbG,GAAAA,IAAOG,GAAAA,CAAMH,GAAAA,EACb,IAAA,CAAA,CAAA;CL3BK;AAAA,SAASoK,yCAAAA,CAAcC,GAAAA,EAAcC,GAAAA,EAAAA;IAAAA,IAGrCtI,GAAAA,GAAU;QAAA,GAAA,EAFhBsI,GAAAA,GAAY,MAAA,GAASrK,uBAAAA,EAAAA;QAAAA,EAAAA,EAILoK,GAAAA;QAEfE,QAAAA,EAAAA,SAAS3K,GAAAA,EAAO4K,GAAAA,EAAAA;YAAAA,OAIR5K,GAAAA,CAAM2B,QAAAA,CAASiJ,GAAAA,CAAAA,CAAAA;SAAAA;QAGvBC,QAAAA,EAAAA,SAAS7K,GAAAA,EAAAA;YAAAA,IAEH8K,GAAAA,EACAC,GAAAA;YAAAA,OAFAX,IAAAA,CAAKxB,eAAAA,IACLkC,CAAAA,GAAAA,GAAO,EAAA,EACPC,CAAAA,GAAAA,GAAM,CAAA,CAAA,CAAA,CACNL,GAAAA,CAAAA,GAAaN,IAAAA,EAAAA,IAAAA,CAEZxB,eAAAA,GAAkB,WAAA;gBAAA,OAAMmC,GAAAA,CAAAA;aAAAA,EAAAA,IAAAA,CAExBvC,qBAAAA,GAAwB,SAASwC,GAAAA,EAAAA;gBACjCZ,IAAAA,CAAKpK,KAAAA,CAAMoG,KAAAA,KAAU4E,GAAAA,CAAO5E,KAAAA,IAe/B0E,GAAAA,CAAKzH,IAAAA,CAAKT,uBAAAA,CAAAA;aAAAA,EAAAA,IAAAA,CAIPsF,GAAAA,GAAM,SAAArF,GAAAA,EAAAA;gBACViI,GAAAA,CAAKhI,IAAAA,CAAKD,GAAAA,CAAAA,CAAAA;gBAAAA,IACNoI,GAAAA,GAAMpI,GAAAA,CAAEsH,oBACZtH;gBAAAA,GAAAA,CAAEsH,oBAAAA,GAAuB,WAAA;oBACxBW,GAAAA,CAAKI,MAAAA,CAAOJ,GAAAA,CAAKvI,OAAAA,CAAQM,GAAAA,CAAAA,EAAI,CAAA,CAAA,EACzBoI,GAAAA,IAAKA,GAAAA,CAAInJ,IAAAA,CAAKe,GAAAA,CAAAA;iBAAAA;aAAAA,CAAAA,EAKd7C,GAAAA,CAAM2B,QAAAA,CAAAA;SAAAA;KAAAA;IAAAA,OAUPS,GAAAA,CAAQyI,QAAAA,CAAAA,EAAAA,GAAuBzI,GAAAA,CAAQuI,QAAAA,CAAS7C,WAAAA,GAAc1F,GAAAA,CAAAA;CJxC1DxB;AAAAA,uBAAAA,GAAQM,uBAAAA,CAAUN,KAAAA,ECfzBD,yCAAAA,GAAU;IAAA,GAAA,ESJT,SAAqBwK,GAAAA,EAAO5K,GAAAA,EAAAA;QAAAA,IAAAA,IAE9B+C,GAAAA,EAAW8H,GAAAA,EAAMC,GAAAA,EAEb9K,GAAAA,GAAQA,GAAAA,CAAAA,EAAAA,EAAAA,IACV+C,CAAAA,GAAAA,GAAY/C,GAAAA,CAAAA,GAAAA,CAAAA,IAAAA,CAAsB+C,GAAAA,CAAAA,EAAAA,EAAAA,IAAAA;YAAAA,IAErC8H,CAAAA,GAAAA,GAAO9H,GAAAA,CAAU7C,WAAAA,CAAAA,IAE4B,IAAA,IAAjC2K,GAAAA,CAAKE,wBAAAA,IAChBhI,CAAAA,GAAAA,CAAUiI,QAAAA,CAASH,GAAAA,CAAKE,wBAAAA,CAAyBH,GAAAA,CAAAA,CAAAA,EACjDE,GAAAA,GAAU/H,GAAAA,CAAAA,GAAAA,CAAAA,EAGwB,IAAA,IAA/BA,GAAAA,CAAUkI,iBAAAA,IACblI,CAAAA,GAAAA,CAAUkI,iBAAAA,CAAkBL,GAAAA,CAAAA,EAC5BE,GAAAA,GAAU/H,GAAAA,CAAAA,GAAAA,CAAAA,EAIP+H,GAAAA,EAAAA,OACK/H,GAAAA,CAAAA,GAAAA,GAA0BA,GAAAA,CAAAA;SAElC,CAAA,OAAO4D,GAAAA,EAAAA;YACRiE,GAAAA,GAAQjE,GAAAA;SAAAA;QAAAA,MAKLiE,GAAAA,CAAAA;KAAAA;CAAAA,ERjCHtL,uBAAAA,GAAU,CAAA,EA6FDgB,yCAAAA,GAAiB,SAAAN,GAAAA,EAAAA;IAAAA,OACpB,IAAA,IAATA,GAAAA,IAAAA,KAAuCC,CAAAA,KAAtBD,GAAAA,CAAME,WAAAA,CAAAA;CAAAA,ECvExB0B,yCAAAA,CAAU4F,SAAAA,CAAUwD,QAAAA,GAAW,SAASE,GAAAA,EAAQC,GAAAA,EAAAA;IAAAA,IAE3CC,GAAAA,AAEHA;IAAAA,GAAAA,GADsB,IAAA,IAAnBvB,IAAAA,CAAAA,GAAAA,IAA2BA,IAAAA,CAAAA,GAAAA,KAAoBA,IAAAA,CAAKjC,KAAAA,GACnDiC,IAAAA,CAAAA,GAAAA,GAEAA,IAAAA,CAAAA,GAAAA,GAAkBhJ,uBAAAA,CAAO,CAAA,CAAA,EAAIgJ,IAAAA,CAAKjC,KAAAA,CAAAA,EAGlB,UAAA,IAAA,OAAVsD,GAAAA,IAGVA,CAAAA,GAAAA,GAASA,GAAAA,CAAOrK,uBAAAA,CAAO,CAAA,CAAA,EAAIuK,GAAAA,CAAAA,EAAIvB,IAAAA,CAAKpK,KAAAA,CAAAA,CAAAA,EAGjCyL,GAAAA,IACHrK,uBAAAA,CAAOuK,GAAAA,EAAGF,GAAAA,CAAAA,EAIG,IAAA,IAAVA,GAAAA,IAEArB,IAAAA,CAAAA,GAAAA,IACCsB,CAAAA,GAAAA,IAAUtB,IAAAA,CAAAA,GAAAA,CAAsBtH,IAAAA,CAAK4I,GAAAA,CAAAA,EACzC9I,uBAAAA,CAAcwH,IAAAA,CAAAA,CAAAA;CAAAA,EAUhBjI,yCAAAA,CAAU4F,SAAAA,CAAU6D,WAAAA,GAAc,SAASF,GAAAA,EAAAA;IACtCtB,IAAAA,CAAAA,GAAAA,IAAAA,CAAAA,IAAAA,CAAAA,GAAAA,GAAAA,CAIW,CAAA,EACVsB,GAAAA,IAAUtB,IAAAA,CAAAA,GAAAA,CAAsBtH,IAAAA,CAAK4I,GAAAA,CAAAA,EACzC9I,uBAAAA,CAAcwH,IAAAA,CAAAA,CAAAA;CAAAA,EAchBjI,yCAAAA,CAAU4F,SAAAA,CAAUC,MAAAA,GAAS9F,yCAAAA,EAyFzBpB,uBAAAA,GAAgB,EAAA,EAQdC,uBAAAA,GACa,UAAA,IAAA,OAAX8K,OAAAA,GACJA,OAAAA,CAAQ9D,SAAAA,CAAU+D,IAAAA,CAAKC,IAAAA,CAAKF,OAAAA,CAAQG,OAAAA,EAAAA,CAAAA,GACpCC,UAAAA,EA2CJlJ,uBAAAA,CAAAA,GAAAA,GAAyB,CAAA,EC9Nd1C,uBAAAA,GAAI,CLFR,CAAA;AFIP,IAAIR,uBAAAA,GAAU,CAAA,AAqBd;AAAA,SAASC,yCAAAA,CAAYC,EAAAA,EAAMC,EAAAA,EAAOC,CAAAA,EAAKC,CAAAA,EAAUC,CAAAA,EAAAA;IAAAA,IAK/CC,CAAAA,EACAC,CAAAA,EAFGC,CAAAA,GAAkB,CAAA,CAAA;IAAA,IAGjBD,CAAAA,IAAKL,EAAAA,CACA,KAAA,IAALK,CAAAA,GACHD,CAAAA,GAAMJ,EAAAA,CAAMK,CAAAA,CAAAA,GAEZC,CAAAA,CAAgBD,CAAAA,CAAAA,GAAKL,EAAAA,CAAMK,CAAAA,CAAAA,CAAAA;IAAAA,IAIvBE,CAAAA,GAAQ;QACbR,IAAAA,EAAAA,EAAAA;QACAC,KAAAA,EAAOM,CAAAA;QACPL,GAAAA,EAAAA,CAAAA;QACAG,GAAAA,EAAAA,CAAAA;QAAAA,GAAAA,EACW,IAAA;QAAA,EAAA,EACF,IAAA;QAAA,GAAA,EACD,CAAA;QAAA,GAAA,EACF,IAAA;QAAA,GAAA,EAAA,KACII,CAAAA;QAAAA,GAAAA,EACE,IAAA;QAAA,GAAA,EACA,IAAA;QACZC,WAAAA,EAAAA,KAAaD,CAAAA;QAAAA,GAAAA,EAAAA,EACAX,uBAAAA;QACbK,QAAAA,EAAAA,CAAAA;QACAC,MAAAA,EAAAA,CAAAA;KAAAA;IAAAA,IAKmB,UAAA,IAAA,OAATJ,EAAAA,IAAwBK,CAAAA,CAAAA,GAAML,EAAAA,CAAKW,YAAAA,CAAAA,EAAAA,IACxCL,CAAAA,IAAKD,CAAAA,CAAAA,KACyB,CAAA,KAAvBE,CAAAA,CAAgBD,CAAAA,CAAAA,IAC1BC,CAAAA,CAAAA,CAAgBD,CAAAA,CAAAA,GAAKD,CAAAA,CAAIC,CAAAA,CAAAA,CAAAA,CAAAA;IAAAA,OAIxBM,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQJ,KAAAA,IAAOI,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQJ,KAAAA,CAAMA,CAAAA,CAAAA,EAC1BA,CAhER,CAAA;CAAA;AgBJA,SAAS,yBAAG,CAAC,GAAW,EAAE,KAAa,EAAE;IACvC,IAAI;QACF,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KACjE,CAAC,OAAO,KAAK,EAAE,CAAA,CAAE;CACnB;AAED,SAAS,yBAAG,CAAC,GAAW,EAAO;IAC7B,IAAI;QACF,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;QAEtD,IAAI,KAAK,EACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;KAE3B,CAAC,OAAO,KAAK,EAAE,CAAA,CAAE;CACnB;IAED,wCAA2B,GAAZ;SAAE,yBAAG;SAAE,yBAAG;CAAE;AChB3B,MAAM,2BAAK,GAAG,IAAI,GAAG,EAAE;AACvB,MAAM,8BAAQ,GAAG;IACf;QAAE,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,cAAG;KAAK;IACrB;QAAD,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,cAAG;KAAK;IACrB;QAAD,CAAC,EAAE,IAAI;QAAE,KAAK,EAAE,sCAAM;KAAY;IAC1B;QAAR,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,cAAG;KAAK;IACrB;QAAD,CAAC,EAAE,IAAI;QAAE,KAAK,EAAE,gCAAK;KAAU;IACzB;QAAN,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,cAAG;KAAK;IACrB;QAAD,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,cAAG;KAAK;IACrB;QAAD,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,cAAG;KAAK;IACpB;QAAD,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,gCAAM;KAAW;IACvB;QAAP,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,cAAG;KAAK;IACpB;QAAD,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,0BAAI;KAAQ;IACrB;QAAJ,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,cAAG;KAAK;CACrB;AAEJ,SAAS,mCAAa,GAAG;IACvB,KAAK,MAAM,EAAA,GAAE,CAAC,CAAA,CAAA,OAAE,KAAK,CAAA,CAAE,IAAI,8BAAQ,CAAE;QACnC,IAAI,iCAAW,CAAC,KAAK,CAAC,EACpB,OAAO,CAAC,CAAA;KAEX;CACF;AAED,SAAS,oCAAc,GAAG;IACxB,IAAI,iCAAW,CAAC,0BAAI,CAAC,EACnB,OAAO,KAAK,CAAA;IAGd,OAAO,IAAI,CAAA;CACZ;AAED,SAAS,iCAAW,CAAC,KAAK,EAAE;IAC1B,IAAI,2BAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAClB,OAAO,2BAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAGzB,MAAM,SAAS,GAAG,sCAAgB,CAAC,KAAK,CAAC;IACzC,2BAAK,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;IAE3B,OAAO,SAAS,CAAA;CACjB;AAED,0DAA0D;AAC1D,MAAM,sCAAgB,GAAI,CAAA,IAAM;IAC9B,IAAI,GAAG,GAAG,IAAI;IACd,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EACxC,GAAG,GAAG,QAAQ,CACX,aAAa,CAAC,QAAQ,CAAC,CACvB,UAAU,CAAC,IAAI,EAAE;YAAE,kBAAkB,EAAE,IAAI;SAAE,CAAC;KAEpD,CAAC,OAAM,CAAA,CAAE;IAEV,qBAAqB;IACrB,IAAI,CAAC,GAAG,EACN,OAAO,IAAM,KAAK,CAAA;IAGpB,MAAM,aAAa,GAAG,EAAE;IACxB,MAAM,YAAY,GAAG,EAAE;IACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC;IAE9C,4BAA4B;IAC5B,GAAG,CAAC,IAAI,GAAG,QAAQ,GAAG,sBAAsB;IAC5C,GAAG,CAAC,YAAY,GAAG,KAAK;IACxB,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,CAAC;IACnC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,aAAa;IAEjC,OAAO,CAAC,OAAO,GAAK;QAClB,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,GAAG,CAAC,EAAE,aAAa,CAAC;QAEpD,0BAA0B;QAC1B,GAAG,CAAC,SAAS,GAAG,SAAS;QACzB,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;QAE5B,wBAAwB;QACxB,GAAG,CAAC,SAAS,GAAG,SAAS;QACzB,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;QAEvC,MAAM,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,IAAI;QAClE,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM;QACtB,IAAI,CAAC,GAAG,CAAC;QAET,iCAAiC;QACjC,MAAO,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QAErC,mBAAmB;QACnB,IAAI,CAAC,IAAI,KAAK,EACZ,OAAO,KAAK,CAAA;QAGd,wFAAwF;QACxF,+BAA+B;QAC/B,MAAM,CAAC,GAAG,YAAY,GAAK,CAAC,GAAG,CAAC,GAAI,YAAY,AAAC;QACjD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QAC1C,MAAM,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;QAE3C,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACpC,OAAO,KAAK,CAAA;QAGd,kEAAkE;QAClE,8CAA8C;QAC9C,IAAI,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,YAAY,EAChD,OAAO,KAAK,CAAA;QAGd,YAAY;QACZ,OAAO,IAAI,CAAA;KACZ,CAAA;CACF,CAAA,EAAG;IAEJ,wCAAgD,GAAjC;mBAAE,mCAAa;oBAAE,oCAAc;CAAE;AC9GhD,MAAM,8BAAQ,GAAG;IACf,IAAI;IACJ,UAAU;IACV,eAAe;IACf,YAAY;IACZ,UAAU;IACV,8BAA8B;IAC9B,aAAa;IACb,KAAK;IACL,QAAQ;IACR,cAAc;IACd,UAAU;IACV,OAAO;IACP,KAAK;IACL,YAAY;IACZ,OAAO;CACR;AAED,IAAI,2BAAK,GAAe,IAAI;AAE5B,SAAS,yBAAG,CAAC,KAAqB,EAAE;IAClC,2BAAK,IAAK,CAAA,2BAAK,GAAG,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAA,CAAE,CAAA,AAAC;IAEhD,MAAM,OAAO,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;IACjC,IAAI,CAAC,OAAO,EAAE,OAAM;IAEpB,2BAAK,CAAC,OAAO,CAAC,IAAK,CAAA,2BAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,AAAC;IACtC,2BAAK,CAAC,OAAO,CAAC,IAAI,CAAC;IAEnB,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;IAC1B,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,YAAY,EAAE,2BAAK,CAAC;CAC/B;AAED,SAAS,yBAAG,CAAC,EAAA,iBAAE,eAAe,CAAA,CAAA,SAAE,OAAO,CAAA,CAAE,EAAE;IACzC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAA;IAE/B,2BAAK,IAAK,CAAA,2BAAK,GAAG,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA,AAAC;IAC1C,IAAI,QAAQ,GAAG,EAAE;IAEjB,IAAI,CAAC,2BAAK,EAAE;QACV,2BAAK,GAAG,CAAA,CAAE;QAEV,IAAK,IAAI,CAAC,IAAI,8BAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAE;YACxC,MAAM,OAAO,GAAG,8BAAQ,CAAC,CAAC,CAAC;YAE3B,2BAAK,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC;YAC5B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;SACvB;QAED,OAAO,QAAQ,CAAA;KAChB;IAED,MAAM,GAAG,GAAG,eAAe,GAAG,OAAO;IACrC,MAAM,IAAI,GAAG,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,MAAM,CAAC;IAE9B,IAAK,IAAI,OAAO,IAAI,2BAAK,CACvB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;IAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK;QACtB,MAAM,MAAM,GAAG,2BAAK,CAAC,CAAC,CAAC;QACvB,MAAM,MAAM,GAAG,2BAAK,CAAC,CAAC,CAAC;QAEvB,IAAI,MAAM,IAAI,MAAM,EAClB,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;QAG3B,OAAO,MAAM,GAAG,MAAM,CAAA;KACvB,CAAC;IAEF,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE;QACzB,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;QACtC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;QAEjC,KAAK,IAAI,SAAS,IAAI,UAAU,CAAE;YAChC,IAAI,SAAS,IAAI,IAAI,EAAE,SAAQ;YAC/B,OAAO,2BAAK,CAAC,SAAS,CAAC;SACxB;QAED,IAAI,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA,CAAE,EAAE;YACxC,OAAO,2BAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3C,QAAQ,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,CAAC,EAAE,IAAI,CAAC;SAC7B;QAED,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,YAAY,EAAE,2BAAK,CAAC;KAC/B;IAED,OAAO,QAAQ,CAAA;CAChB;IAED,wCAAqC,GAAtB;SAAE,yBAAG;SAAE,yBAAG;cAAE,8BAAQ;CAAE;;AG7FrC,yBAAc,GAAG,IAAI,CAAC,KAAK,CAAC,gkBAAspB,CAAC,CAAC;ACAprB,IAAA,wCAwHC,GAxHc;IACb,SAAS,EAAE;QACT,KAAK,EAAE,KAAK;KACb;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,KAAK;KACb;IACD,iBAAiB,EAAE;QACjB,KAAK,EAAE,IAAI;KACZ;IACD,iBAAiB,EAAE;QACjB,KAAK,EAAE,MAAM;KACd;IACD,eAAe,EAAE;QACf,KAAK,EAAE,EAAE;KACV;IACD,SAAS,EAAE;QACT,KAAK,EAAE,EAAE;KACV;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,EAAE;QACT,OAAO,EAAE;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,EAAE;YAAE,EAAE;YAAE,IAAI;YAAE,EAAE;YAAE,IAAI;YAAE,EAAE;YAAE,EAAE;SAAC;KACzD;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,EAAE;KACV;IACD,KAAK,EAAE;QACL,KAAK,EAAE,MAAM;QACb,OAAO,EAAE;YAAC,MAAM;YAAE,SAAS;YAAE,OAAO;SAAC;KACtC;IACD,MAAM,EAAE;QACN,KAAK,EAAE,IAAI;QACX,OAAO,EAAE;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;SACL;KACF;IACD,eAAe,EAAE;QACf,KAAK,EAAE,CAAC;KACT;IACD,WAAW,EAAE;QACX,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE;YAAC,KAAK;YAAE,QAAQ;YAAE,MAAM;SAAC;KACnC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,KAAK;KACb;IACD,cAAc,EAAE;QACd,KAAK,EAAE,IAAI;KACZ;IACD,OAAO,EAAE;QACP,KAAK,EAAE,CAAC;KACT;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,eAAe,EAAE;QACf,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YAAC,KAAK;YAAE,QAAQ;YAAE,MAAM;SAAC;KACnC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YAAC,QAAQ;YAAE,QAAQ;YAAE,MAAM;SAAC;KACtC;IACD,GAAG,EAAE;QACH,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YAAC,QAAQ;YAAE,OAAO;YAAE,UAAU;YAAE,QAAQ;YAAE,SAAS;SAAC;KAC9D;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,CAAC;QACR,OAAO,EAAE;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC;KAC5B;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE;YAAC,SAAS;YAAE,QAAQ;YAAE,MAAM;SAAC;KACvC;IACD,KAAK,EAAE;QACL,KAAK,EAAE,MAAM;QACb,OAAO,EAAE;YAAC,MAAM;YAAE,OAAO;YAAE,MAAM;SAAC;KACnC;IAED,OAAO;IACP,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,MAAM,EAAE,IAAI;IACZ,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IAEV,YAAY;IACZ,WAAW,EAAE,IAAI;IACjB,iBAAiB,EAAE,IAAI;IACvB,gBAAgB,EAAE,IAAI;IACtB,cAAc,EAAE,IAAI;IACpB,aAAa,EAAE,IAAI;IAEnB,aAAa;IACb,YAAY,EAAE;QACZ,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,IAAI;KACZ;CACF;AF9GM,IAAI,yCAAI,GAAG,IAAI;AACf,IAAI,yCAAI,GAAG,IAAI;AAEtB,MAAM,gCAAU,GAAG,CAAA,CAAE;AACrB,eAAe,+BAAS,CAAC,GAAG,EAAE;IAC5B,IAAI,gCAAU,CAAC,GAAG,CAAC,EACjB,OAAO,gCAAU,CAAC,GAAG,CAAC,CAAA;IAGxB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC;IACjC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;IAElC,gCAAU,CAAC,GAAG,CAAC,GAAG,IAAI;IACtB,OAAO,IAAI,CAAA;CACZ;AAED,IAAI,6BAAO,GAAyB,IAAI;AACxC,IAAI,+BAAS,GAAG,KAAK;AACrB,IAAI,kCAAY,GAAG,IAAI;AACvB,IAAI,iCAAW,GAAG,KAAK;AAEhB,SAAS,yCAAI,CAAC,OAAO,EAAE,EAAA,QAAE,MAAM,CAAA,CAAE,GAAG,CAAA,CAAE,EAAE;IAC7C,6BAAO,IACJ,CAAA,6BAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,GAAK;QAClC,kCAAY,GAAG,OAAO;KACvB,CAAC,CAAA,AAAC;IAEL,IAAI,OAAO,EACT,2BAAK,CAAC,OAAO,CAAC;SACT,IAAI,MAAM,IAAI,CAAC,iCAAW,EAC/B,OAAO,CAAC,IAAI,CACV,CAAC,EAAE,EAAE,MAAM,CAAC,2FAA2F,CAAC,CACzG;IAGH,OAAO,6BAAO,CAAA;CACf;AAED,eAAe,2BAAK,CAAC,KAAK,EAAE;IAC1B,iCAAW,GAAG,IAAI;IAElB,IAAI,EAAA,cAAE,YAAY,CAAA,CAAA,KAAE,GAAG,CAAA,CAAA,QAAE,MAAM,CAAA,CAAE,GAAG,KAAK;IACzC,YAAY,IAAK,CAAA,YAAY,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,YAAY,CAAC,KAAK,CAAA,AAAC;IAC/D,GAAG,IAAK,CAAA,GAAG,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,GAAG,CAAC,KAAK,CAAA,AAAC;IACpC,MAAM,IAAK,CAAA,MAAM,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,MAAM,CAAC,KAAK,CAAA,AAAC;IAE7C,IAAI,CAAC,yCAAI,EAAE;QACT,yCAAI,GACD,CAAA,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAA,IAClE,MAAM,+BAAS,CACd,CAAC,0DAA0D,EAAE,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CACxF,AAAC;QAEJ,yCAAI,CAAC,SAAS,GAAG,CAAA,CAAE;QACnB,yCAAI,CAAC,OAAO,GAAG,CAAA,CAAE;QAEjB,yCAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACtB,EAAE,EAAE,UAAU;YACd,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAK,MAAM,KAAK,IAAI,yCAAI,CAAC,OAAO,CAAE;YAChC,MAAM,OAAO,GAAG,yCAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACnC,MAAM,KAAK,GAAG,yCAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAClC,IAAI,CAAC,KAAK,EAAE,SAAQ;YAEpB,KAAK,CAAC,OAAO,IAAK,CAAA,KAAK,CAAC,OAAO,GAAG,EAAE,CAAA,AAAC;YACrC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;SAC1B;QAED,yCAAI,CAAC,kBAAkB,GAAG,yCAAI,CAAC,UAAU;KAC1C,MACC,yCAAI,CAAC,UAAU,GAAG,yCAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAK;QAC9C,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACzB,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAA;QAE1B,OAAO,KAAK,CAAA;KACb,CAAC;IAGJ,yCAAI,GACD,CAAA,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAA,IAClE,CAAA,MAAM,IAAI,IAAI,GACX,CAAA,GAAA,WAAA,GAAA,uBAAA,0BAAO,CAAA,GACP,MAAM,+BAAS,CACb,CAAC,0DAA0D,EAAE,MAAM,CAAC,KAAK,CAAC,CAC3E,CAAA,AAAC;IAER,IAAI,KAAK,CAAC,MAAM,EACd,IAAK,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAE;QAC1B,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QACf,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAChC,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAQ;QAEzD,QAAQ,CAAC,EAAE,IAAK,CAAA,QAAQ,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,AAAC;QAChD,QAAQ,CAAC,IAAI,IAAK,CAAA,QAAQ,CAAC,IAAI,GAAG,yCAAI,CAAC,UAAU,CAAC,MAAM,CAAA,AAAC;QAEzD,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,EAChC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,YAAY;QAGvD,yCAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,CACjC,yCAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK;KAEhC;IAGH,IAAI,KAAK,CAAC,UAAU,EAClB,yCAAI,CAAC,UAAU,GAAG,yCAAI,CAAC,kBAAkB,CACtC,MAAM,CAAC,CAAC,CAAC,GAAK;QACb,OAAO,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAA,CAAE,CAAA;KAC5C,CAAC,CACD,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK;QAChB,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;QAE1C,OAAO,EAAE,GAAG,EAAE,CAAA;KACf,CAAC;IAGN,IAAI,oBAAoB,GAAG,IAAI;IAC/B,IAAI,cAAc,GAAG,IAAI;IACzB,IAAI,GAAG,IAAI,QAAQ,EAAE;QACnB,oBAAoB,GAAG,CAAA,GAAA,wCAAa,CAAA,CAAC,aAAa,EAAE;QACpD,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,CAAA,GAAA,wCAAa,CAAA,CAAC,cAAc,EAAE;KACxE;IAED,IAAI,aAAa,GAAG,yCAAI,CAAC,UAAU,CAAC,MAAM;IAC1C,IAAI,gBAAgB,GAAG,KAAK;IAC5B,MAAO,aAAa,EAAE,CAAE;QACtB,MAAM,QAAQ,GAAG,yCAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QAE/C,IAAI,QAAQ,CAAC,EAAE,IAAI,UAAU,EAAE;YAC7B,IAAI,EAAA,iBAAE,eAAe,CAAA,CAAA,SAAE,OAAO,CAAA,CAAE,GAAG,KAAK;YAExC,eAAe,GACb,eAAe,IAAI,CAAC,GAChB,eAAe,GACf,CAAA,GAAA,wCAAW,CAAA,CAAC,eAAe,CAAC,KAAK;YACvC,OAAO,IAAK,CAAA,OAAO,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,OAAO,CAAC,KAAK,CAAA,AAAC;YAEhD,QAAQ,CAAC,MAAM,GAAG,CAAA,GAAA,wCAAc,CAAA,CAAC,GAAG,CAAC;iCAAE,eAAe;yBAAE,OAAO;aAAE,CAAC;SACnE;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE;YAC/C,yCAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YACxC,SAAQ;SACT;QAED,MAAM,EAAA,eAAE,aAAa,CAAA,CAAE,GAAG,KAAK;QAC/B,IAAI,aAAa,EAAE;YACjB,MAAM,IAAI,GAAG,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EACxB,QAAQ,CAAC,IAAI,GAAG,IAAI;SAEvB;QAED,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM;QACvC,MAAO,UAAU,EAAE,CAAE;YACnB,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;YAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,yCAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAEzD,MAAM,MAAM,GAAG,IAAM;gBACnB,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;aACtC;YAED,IACE,CAAC,KAAK,IACL,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,AAAC,EAC7D;gBACA,MAAM,EAAE;gBACR,SAAQ;aACT;YAED,IAAI,oBAAoB,IAAI,KAAK,CAAC,OAAO,GAAG,oBAAoB,EAAE;gBAChE,MAAM,EAAE;gBACR,SAAQ;aACT;YAED,IAAI,cAAc,IAAI,QAAQ,CAAC,EAAE,IAAI,OAAO,EAC1C;gBAAA,IAAI,CAAC,CAAA,GAAA,yCAAS,CAAA,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;oBACjC,MAAM,EAAE;oBACR,SAAQ;iBACT;aAAA,AACF;YAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjB,gBAAgB,GAAG,IAAI;gBACvB,KAAK,CAAC,MAAM,GACV,GAAG,GACH;oBACE;wBAAC,KAAK,CAAC,EAAE;wBAAE,KAAK;qBAAC;oBACjB;wBAAC,KAAK,CAAC,IAAI;wBAAE,IAAI;qBAAC;oBAClB;wBAAC,KAAK,CAAC,QAAQ;wBAAE,KAAK;qBAAC;oBACvB;wBAAC,KAAK,CAAC,SAAS;wBAAE,KAAK;qBAAC;iBACzB,CACE,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,GAAK;oBACzB,IAAI,CAAC,OAAO,EAAE,OAAM;oBACpB,OAAQ,CAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG;wBAAC,OAAO;qBAAC,CAAA,CACjD,GAAG,CAAC,CAAC,MAAM,GAAK;wBACf,OAAQ,CAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA,YAAa,GAAG;4BAAC,MAAM;yBAAC,CAAA,CAAE,GAAG,CACvD,CAAC,CAAC,GAAK,CAAC,CAAC,WAAW,EAAE,CACvB,CAAA;qBACF,CAAC,CACD,IAAI,EAAE,CAAA;iBACV,CAAC,CACD,IAAI,EAAE,CACN,MAAM,CAAC,CAAC,CAAC,GAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAC5B,IAAI,CAAC,GAAG,CAAC;gBAEd,IAAI,KAAK,CAAC,SAAS,EACjB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAE;oBACtC,IAAI,yCAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,SAAQ;oBACtC,yCAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE;iBACpC;gBAGH,IAAI,SAAS,GAAG,CAAC;gBACjB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAE;oBAC9B,IAAI,CAAC,IAAI,EAAE,SAAQ;oBACnB,SAAS,EAAE;oBAEX,MAAM,EAAA,QAAE,MAAM,CAAA,CAAE,GAAG,IAAI;oBACvB,IAAI,MAAM,EAAE;wBACV,yCAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE;wBAC/B,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;qBAC7B;oBAED,MAAM,cAAc,GAClB,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;iBACnD;aACF;SACF;KACF;IAED,IAAI,gBAAgB,EAClB,CAAA,GAAA,wCAAW,CAAA,CAAC,KAAK,EAAE;IAGrB,kCAAY,EAAE;CACf;AAEM,SAAS,yCAAQ,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE;IACrD,KAAK,IAAK,CAAA,KAAK,GAAG,CAAA,CAAE,CAAA,AAAC;IAErB,MAAM,MAAM,GAAG,CAAA,CAAE;IACjB,IAAK,IAAI,CAAC,IAAI,YAAY,CACxB,MAAM,CAAC,CAAC,CAAC,GAAG,yCAAO,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC;IAGtD,OAAO,MAAM,CAAA;CACd;AAEM,SAAS,yCAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE;IAC9D,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;IACvC,IAAI,KAAK,GACN,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IACzC,CAAA,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,SAAS,GACpD,KAAK,CAAC,QAAQ,CAAC,GACf,IAAI,CAAA,AAAC;IAEX,IAAI,CAAC,QAAQ,EACX,OAAO,KAAK,CAAA;IAGd,IACE,KAAK,IAAI,IAAI,IACb,QAAQ,CAAC,KAAK,IACd,OAAO,QAAQ,CAAC,KAAK,IAAI,OAAO,KAAK,EAAA;QAErC,IAAI,OAAO,QAAQ,CAAC,KAAK,IAAI,SAAS,EACpC,KAAK,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI;aAEvC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;KAE5C;IAED,IAAI,QAAQ,CAAC,SAAS,IAAI,KAAK,EAC7B,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC;IAGnC,IACE,KAAK,IAAI,IAAI,IACZ,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAA,CAAE,AAAC,EAE3D,KAAK,GAAG,QAAQ,CAAC,KAAK;IAGxB,OAAO,KAAK,CAAA;CACb;AD7SD,MAAM,sCAAgB,GAAA,2CAA8C;AACpE,IAAI,0BAAI,GAAG,IAAI;AAEf,SAAS,yBAAG,CAAC,OAAO,EAAE;IACpB,IAAI,OAAO,CAAC,EAAE,EACZ,OAAO,OAAO,CAAA;IAGhB,OACE,CAAA,GAAA,yCAAI,CAAA,CAAC,MAAM,CAAC,OAAO,CAAC,IACpB,CAAA,GAAA,yCAAI,CAAA,CAAC,MAAM,CAAC,CAAA,GAAA,yCAAI,CAAA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAClC,CAAA,GAAA,yCAAI,CAAA,CAAC,MAAM,CAAC,CAAA,GAAA,yCAAI,CAAA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CACnC;CACF;AAED,SAAS,2BAAK,GAAG;IACf,0BAAI,GAAG,IAAI;CACZ;AAED,eAAe,4BAAM,CAAC,KAAK,EAAE,EAAA,YAAE,UAAU,CAAA,CAAA,QAAE,MAAM,CAAA,CAAE,GAAG,CAAA,CAAE,EAAE;IACxD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;IAC/C,UAAU,IAAK,CAAA,UAAU,GAAG,EAAE,CAAA,AAAC;IAE/B,MAAM,CAAA,GAAA,yCAAI,CAAA,CAAC,IAAI,EAAE;QAAE,MAAM,EAAE,MAAM,IAAI,oBAAoB;KAAE,CAAC;IAE5D,MAAM,MAAM,GAAG,KAAK,CACjB,WAAW,EAAE,CACb,OAAO,CAAA,SAAU,KAAK,CAAC,CACvB,KAAK,CAAA,UAAW,CAChB,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,GAAK;QAC1B,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KAC/C,CAAC;IAEJ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAM;IAE1B,IAAI,IAAI,GAAG,0BAAI,IAAK,CAAA,0BAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,GAAA,yCAAI,CAAA,CAAC,MAAM,CAAC,CAAA,AAAC;IACtD,IAAI,OAAO,EAAE,MAAM;IAEnB,KAAK,MAAM,MAAK,IAAI,MAAM,CAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAK;QAEvB,OAAO,GAAG,EAAE;QACZ,MAAM,GAAG,CAAA,CAAE;QAEX,KAAK,MAAM,KAAK,IAAI,IAAI,CAAE;YACxB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAQ;YAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAK,CAAC,CAAC,CAAC;YAC/C,IAAI,KAAK,IAAI,CAAA,CAAE,EAAE,SAAQ;YAEzB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YACnB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAK,CAAA,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA,AAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,MAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;SACtD;QAED,IAAI,GAAG,OAAO;KACf;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EACpB,OAAO,OAAO,CAAA;IAGhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAE3B,IAAI,MAAM,IAAI,MAAM,EAClB,OAAO,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAGjC,OAAO,MAAM,GAAG,MAAM,CAAA;KACvB,CAAC;IAEF,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAC7B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;IAGxC,OAAO,OAAO,CAAA;CACf;IAED,wCAAuD,GAAxC;YAAE,4BAAM;SAAE,yBAAG;WAAE,2BAAK;sBAAE,sCAAgB;CAAE;AJ5EhD,MAAM,yCAAS,GAAG;IACvB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,cAAc;IACd,kBAAkB;IAClB,yBAAyB;IACzB,mBAAmB;IACnB,mBAAmB;CACpB;ADbM,SAAS,yCAAS,CAAC,CAAM,EAAE,CAAM,EAAW;IACjD,OACE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAChB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAChB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IACrB,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,GAAK,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CACzC;CACF;AAEM,eAAe,yCAAK,CAAC,MAAM,GAAG,CAAC,EAAE;IACtC,IAAK,IAAI,CAAC,IAAI;WAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;KAAC,CACrC,MAAM,IAAI,OAAO,CAAC,qBAAqB,CAAC;CAE3C;AAEM,SAAS,yCAAY,CAAC,KAAK,EAAE,EAAA,WAAE,SAAS,GAAG,CAAC,EAAE,GAAG,CAAA,CAAE,EAAE;IAC1D,MAAM,IAAI,GACR,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IACrB,CAAA,IAAM;QACL,SAAS,GAAG,CAAC;QACb,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;KAC9B,CAAA,EAAG;IAEN,MAAM,SAAS,GAAQ;QACrB,EAAE,EAAE,KAAK,CAAC,EAAE;QACZ,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU;KAChD;IAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EACxB,SAAS,CAAC,IAAI,GAAG,SAAS,GAAG,CAAC;IAGhC,IAAI,IAAI,CAAC,GAAG,EACV,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;IAG1B,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EACvC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO;IAGnC,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAC3C,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS;IAGvC,OAAO,SAAS,CAAA;CACjB;AAEM,eAAe,yCAAsB,CAAC,YAAY,EAAE;IACzD,MAAM,OAAO,GAAG,MAAM,CAAA,GAAA,wCAAW,CAAA,CAAC,MAAM,CAAC,YAAY,EAAE;QACrD,UAAU,EAAE,CAAC;QACb,MAAM,EAAE,wBAAwB;KACjC,CAAC;IACF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;IAE5C,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,GAAG,CAAC;IAEjB,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,KAAK,CAAE;QAC5B,IAAI,IAAI,CAAC,MAAM,IAAI,YAAY,EAC7B,MAAK;QAGP,SAAS,EAAE;KACZ;IAED,OAAO,yCAAY,CAAC,KAAK,EAAE;mBAAE,SAAS;KAAE,CAAC,CAAA;CAC1C;ASxED,MAAM,gCAAU,GAAG;IACjB,QAAQ,EAAE;QACR,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;sBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,ytBAAytB;cAAG;UAChuB;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,gkCAAgkC;cAAG;UACvkC;KAET;IAED,MAAM,EAAA,WAAA,GACJ,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;QAAC,KAAK,EAAC,4BAA4B;QAAC,OAAO,EAAC,aAAa;kBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;YAAC,CAAC,EAAC,kmBAAkmB;UAAG;MACzmB;IAGR,KAAK,EAAE;QACL,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;sBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,iKAAiK;cAAG;UACxK;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,gaAAga;cAAG;UACva;KAET;IAED,KAAK,EAAE;QACL,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;sBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,k1BAAk1B;cAAG;UACz1B;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,s9DAAs9D;cAAG;UAC79D;KAET;IAED,QAAQ,EAAE;QACR,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;;8BACzD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,wCAAwC;kBAAG;8BACnD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,+IAA+I;kBAAG;;UACtJ;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,yTAAyT;cAAG;UAChU;KAET;IAED,MAAM,EAAE;QACN,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;;8BACzD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,0GAA0G;kBAAG;8BACrH,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,khDAAkhD;kBAAG;;UACzhD;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,8tBAA8tB;cAAG;UACruB;KAET;IAED,OAAO,EAAE;QACP,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;;8BACzD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,iXAAiX;kBAAG;8BAC5X,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,8iBAA8iB;kBAAG;;UACrjB;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,woBAAwoB;cAAG;UAC/oB;KAET;IAED,MAAM,EAAE;QACN,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;;8BACzD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,+IAA+I;kBAAG;8BAC1J,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,0KAA0K;kBAAG;;UACjL;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,4gBAA4gB;cAAG;UACnhB;KAET;IAED,MAAM,EAAE;QACN,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;;8BACzD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,qSAAqS;kBAAG;8BAChT,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,CAAC,EAAC,qrBAAqrB;kBAAG;;UAC5rB;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,2oBAA2oB;cAAG;UAClpB;KAET;IAED,OAAO,EAAE;QACP,OAAO,EAAA,WAAA,GACL,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,WAAW;sBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,04CAA04C;cAAG;UACj5C;QAER,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,4BAA4B;YAAC,OAAO,EAAC,aAAa;sBAC3D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBAAC,CAAC,EAAC,+4CAA+4C;cAAG;UACt5C;KAET;CACF;AAED,MAAM,4BAAM,GAAG;IACb,KAAK,EAAA,WAAA,GACH,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;QAAC,KAAK,EAAC,4BAA4B;QAAC,OAAO,EAAC,WAAW;kBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;YAAC,CAAC,EAAC,mGAAmG;UAAG;MAC1G;IAGR,MAAM,EAAA,WAAA,GACJ,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;QAAC,KAAK,EAAC,4BAA4B;QAAC,OAAO,EAAC,WAAW;kBACzD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;YAAC,CAAC,EAAC,mJAAmJ;UAAG;MAC1J;CAET;IAED,wCAAqC,GAAtB;gBAAE,gCAAU;YAAE,4BAAM;CAAE;AE7ItB,SAAA,yCAAe,KAAK,EAAE;IACnC,IAAI,EAAA,IAAE,EAAE,CAAA,CAAA,MAAE,IAAI,CAAA,CAAA,OAAE,KAAK,CAAA,CAAE,GAAG,KAAK;IAE/B,IAAI,KAAK,CAAC,UAAU,EAAE;QACpB,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA,GAAA,wCAAW,CAAA,CAAC,gBAAgB,CAAC;QAEpE,IAAI,OAAO,EAAE;YACX,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;YAEf,IAAI,OAAO,CAAC,CAAC,CAAC,EACZ,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;SAEpB;KACF;IAED,KAAK,IAAK,CAAA,KAAK,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,CAAA,AAAC;IACtD,IAAI,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,QAAQ,CAAA;IAEjC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAEzD,MAAM,QAAQ,GACZ,SAAS,CAAC,GAAG,IACZ,CAAA,KAAK,CAAC,GAAG,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,WAAW,GACxC,OAAO,KAAK,CAAC,WAAW,KAAK,UAAU,GACrC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,GAC/C,CAAC,8CAA8C,EAAE,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAClH,SAAS,CAAA,AAAC;IAEhB,MAAM,cAAc,GAClB,OAAO,KAAK,CAAC,iBAAiB,KAAK,UAAU,GACzC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,GAClC,CAAC,8CAA8C,EAAE,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAE5G,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;QAAC,KAAK,EAAC,kBAAkB;QAAC,gBAAc,EAAE,KAAK,CAAC,GAAG;kBACrD,QAAQ,GAAA,WAAA,GACP,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK;gBAC7B,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK;gBAC9B,OAAO,EAAE,cAAc;aACxB;YACD,GAAG,EAAE,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,UAAU;YAC7C,GAAG,EAAE,QAAQ;UACb,GACA,KAAK,CAAC,GAAG,IAAI,QAAQ,GAAA,WAAA,GACvB,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;YACH,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK,CAAC,IAAI;gBACpB,UAAU,EACR,2IAA2I;aAC9I;sBAEA,SAAS,CAAC,MAAM;UACZ,GAAA,WAAA,GAEP,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;YACH,KAAK,EAAE;gBACL,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,MAAM,EAAE,KAAK,CAAC,IAAI;gBAClB,eAAe,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;gBACzC,cAAc,EAAE,CAAC,EAAE,GAAG,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACzC,GAAG,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,CACtB,CAAC,CAAC;gBACH,kBAAkB,EAAE,CAAC,EAClB,GAAG,GAAI,CAAA,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA,GAAK,SAAS,CAAC,CAAC,CAC5C,EAAE,EAAG,GAAG,GAAI,CAAA,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA,GAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACpD;UACK,AACT;MACI,CACR;CACF;AGzED,MAAM,uCAAiB,GACrB,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,WAAW,GAC/C,MAAM,CAAC,WAAW,GAClB,MAAM;AAEG,MAAM,wCAAW,SAAS,uCAAiB;IACxD,WAAW,kBAAkB,GAAG;QAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;KAC/B;IAeD,MAAM,CAAC,KAAK,GAAG,CAAA,CAAE,EAAE;QACjB,IAAK,IAAI,CAAC,IAAI,KAAK,CACjB,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;KAEnD;IAED,wBAAwB,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAM;QAE3B,MAAM,KAAK,GAAG,CAAA,GAAA,yCAAO,CAAA,CACnB,IAAI,EACJ;YAAE,CAAC,IAAI,CAAC,EAAE,QAAQ;SAAE,EACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EACtB,IAAI,CACL;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAC1C,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAC;YAAE,CAAC,IAAI,CAAC,EAAE,KAAK;SAAE,CAAC;aACtD;YACL,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK;YAClC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;SAC7B;KACF;IAED,oBAAoB,GAAG;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI;QAExB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAC7C,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;KAE9B;IA3CD,YAAY,KAAK,GAAG,CAAA,CAAE,CAAE;QACtB,KAAK,EAAE;QACP,IAAI,CAAC,KAAK,GAAG,KAAK;QAElB,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE;YAC7B,IAAI,GAAG,GAAG,IAAI;YACd,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAK,CAAA,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAA,AAAC;YAErE,IAAI,GAAG,EAAE,GAAG,CAAC,SAAS,GAAG,EAAE;YAC3B,IAAI,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;SACrC;KACF;CAiCF;ACtDc,MAAM,wCAAa,SAAS,CAAA,GAAA,wCAAW,CAAA;IAQpD,SAAS,GAAG;QACV,IAAI,CAAC,YAAY,CAAC;YAAE,IAAI,EAAE,MAAM;SAAE,CAAC;KACpC;IAED,YAAY,CAAC,MAAM,EAAE;QACnB,IAAI,CAAC,MAAM,EAAE,OAAM;QAEnB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;QAC7C,KAAK,CAAC,WAAW,GAAG,MAAM;QAE1B,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;KAChE;IAlBD,YAAY,KAAK,EAAE,EAAA,QAAE,MAAM,CAAA,CAAE,GAAG,CAAA,CAAE,CAAE;QAClC,KAAK,CAAC,KAAK,CAAC;QAEZ,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;KAC1B;CAcF;ICrBD,wCAoBC,GApBc;IACb,QAAQ,EAAE,EAAE;IACZ,EAAE,EAAE,EAAE;IACN,MAAM,EAAE,EAAE;IACV,UAAU,EAAE,EAAE;IACd,IAAI,EAAE;QACJ,KAAK,EAAE,EAAE;QACT,SAAS,EAAE,CAAC,KAAK,GAAK;YACpB,+DAAA,EAA+D;YAC/D,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EACnB,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAA;YAGrB,OAAO,KAAK,CAAA;SACb;KACF;IAED,SAAS;IACT,GAAG,EAAE,CAAA,GAAA,wCAAW,CAAA,CAAC,GAAG;IACpB,IAAI,EAAE,CAAA,GAAA,wCAAW,CAAA,CAAC,IAAI;CACvB;AJfc,MAAM,wCAAY,SAAS,CAAA,GAAA,wCAAW,CAAA;IAOnD,MAAM,iBAAiB,GAAG;QACxB,MAAM,KAAK,GAAG,CAAA,GAAA,yCAAQ,CAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA,GAAA,wCAAU,CAAA,EAAE,IAAI,CAAC;QACpD,KAAK,CAAC,OAAO,GAAG,IAAI;QACpB,KAAK,CAAC,GAAG,GAAG,CAAC,SAAS,GAAK;YACzB,IAAI,CAAC,SAAS,GAAG,SAAS;SAC3B;QAED,MAAM,CAAA,GAAA,yCAAI,CAAA,EAAE;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE,OAAM;QAE7B,CAAA,GAAA,yCAAM,CAAA,CAAA,WAAA,GAAC,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAK,CAAA,EAAA;YAAE,GAAG,KAAK;UAAI,EAAE,IAAI,CAAC;KACnC;IAfD,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC,KAAK,CAAC;KACb;CAcF;AAlBC,CAAA,GAAA,wCAAA,EADmB,wCAAY,EACxB,OAAK,EAAG,CAAA,GAAA,wCAAU,CAAA,CAAA;AAoB3B,IAAI,OAAO,cAAc,KAAK,WAAW,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,EAC1E,cAAc,CAAC,MAAM,CAAC,UAAU,EAAE,wCAAY,CAAC;AmB1BjD,IAAI+T,uBAAAA,EAGAlC,uBAAAA,EAeAmC,uBAAAA,EAZAC,uBAAAA,GAAc,CAAA,EAGdC,uBAAAA,GAAoB,EAAA,EAEpBC,uBAAAA,GAAgB/T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,EAChBkS,uBAAAA,GAAkBlS,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,EAClBgU,uBAAAA,GAAehU,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQoI,MAAAA,EACvB6L,uBAAAA,GAAYjU,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,EACZkU,uBAAAA,GAAmBlU,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQsE,OA4E/B;AAAA,SAAS6P,uBAAAA,CAAaC,EAAAA,EAAOhV,EAAAA,EAAAA;IACxBY,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,IACHA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,CAAcyR,uBAAAA,EAAkB2C,EAAAA,EAAOP,uBAAAA,IAAezU,EAAAA,CAAAA,EAEvDyU,uBAAAA,GAAc,CAAA,CAAA;IAAA,IAORQ,EAAAA,GACL5C,uBAAAA,CAAAA,GAAAA,IACCA,CAAAA,uBAAAA,CAAAA,GAAAA,GAA2B;QAAA,EAAA,EACpB,EAAA;QAAA,GAAA,EACU,EAAA;KAAA,CAAA;IAAA,OAGf2C,EAAAA,IAASC,EAAAA,CAAAA,EAAAA,CAAYnT,MAAAA,IACxBmT,EAAAA,CAAAA,EAAAA,CAAYlS,IAAAA,CAAK,CAAA,CAAA,CAAA,EAEXkS,EAAAA,CAAAA,EAAAA,CAAYD,EAAAA,CAAAA,CAAAA;CAMb;AAAA,SAASnB,yCAAAA,CAASqB,EAAAA,EAAAA;IAAAA,OACxBT,uBAAAA,GAAc,CAAA,EACPX,yCAAAA,CAAWqB,uBAAAA,EAAgBD,EAAAA,CAAAA,CAAAA;CASnC;AAAA,SAAgBpB,yCAAAA,CAAWsB,EAAAA,EAASF,EAAAA,EAAcG,EAAAA,EAAAA;IAAAA,IAE3CC,EAAAA,GAAYP,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,CAAA,CAAA;IAAA,OAC/Ce,EAAAA,CAAUC,CAAAA,GAAWH,EAAAA,EAChBE,EAAAA,CAAAA,GAAAA,IACJA,CAAAA,EAAAA,CAAAA,EAAAA,GAAmB;QACjBD,EAAAA,GAAiDA,EAAAA,CAAKH,EAAAA,CAAAA,GAA/CC,uBAAAA,CAAAA,KAAe1U,CAAAA,EAAWyU,EAAAA,CAAAA;QAElC,SAAAM,EAAAA,EAAAA;YAAAA,IACOC,EAAAA,GAAYH,EAAAA,CAAUC,CAAAA,CAASD,EAAAA,CAAAA,EAAAA,CAAiB,CAAA,CAAA,EAAIE,EAAAA,CAAAA,AACtDF;YAAAA,EAAAA,CAAAA,EAAAA,CAAiB,CAAA,CAAA,KAAOG,EAAAA,IAC3BH,CAAAA,EAAAA,CAAAA,EAAAA,GAAmB;gBAACG,EAAAA;gBAAWH,EAAAA,CAAAA,EAAAA,CAAiB,CAAA,CAAA;aAAA,EAChDA,EAAAA,CAAAA,GAAAA,CAAqB9J,QAAAA,CAAS,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA,EAKjC8J,EAAAA,CAAAA,GAAAA,GAAuBjD,uBAAAA,CAAAA,EAGjBiD,EAAAA,CAAAA,EAAAA,CAAAA;CAOD;AAAA,SAASvB,yCAAAA,CAAUpI,EAAAA,EAAU+J,EAAAA,EAAAA;IAAAA,IAE7BtN,EAAAA,GAAQ2M,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,CAAA,CAAA;IAAA,CACtC3T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,IAAwB+U,uBAAAA,CAAYvN,EAAAA,CAAAA,GAAAA,EAAasN,EAAAA,CAAAA,IACrDtN,CAAAA,EAAAA,CAAAA,EAAAA,GAAeuD,EAAAA,EACfvD,EAAAA,CAAAA,GAAAA,GAAcsN,EAAAA,EAEdrD,uBAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAyCtP,IAAAA,CAAKqF,EAAAA,CAAAA,CAAAA;CAQzC;AAAA,SAAS4L,yCAAAA,CAAgBrI,EAAAA,EAAU+J,EAAAA,EAAAA;IAAAA,IAEnCtN,EAAAA,GAAQ2M,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,CAAA,CAAA;IAAA,CACtC3T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,IAAwB+U,uBAAAA,CAAYvN,EAAAA,CAAAA,GAAAA,EAAasN,EAAAA,CAAAA,IACrDtN,CAAAA,EAAAA,CAAAA,EAAAA,GAAeuD,EAAAA,EACfvD,EAAAA,CAAAA,GAAAA,GAAcsN,EAAAA,EAEdrD,uBAAAA,CAAAA,GAAAA,CAAkCtP,IAAAA,CAAKqF,EAAAA,CAAAA,CAAAA;CAIlC;AAAA,SAAS6L,yCAAAA,CAAO2B,EAAAA,EAAAA;IAAAA,OACtBnB,uBAAAA,GAAc,CAAA,EACPN,yCAAAA,CAAQ,WAAA;QAAA,OAAO;YAAEjS,OAAAA,EAAS0T,EAAAA;SAAAA,CAAAA;KAAAA,EAAiB,EAAA,CAAA,CAAA;CAQnD;AAAA,SAAgB1B,yCAAAA,CAAoB7T,EAAAA,EAAKwV,EAAAA,EAAcH,EAAAA,EAAAA;IACtDjB,uBAAAA,GAAc,CAAA,EACdT,yCAAAA,CACC,WAAA;QACmB,UAAA,IAAA,OAAP3T,EAAAA,GAAmBA,EAAAA,CAAIwV,EAAAA,EAAAA,CAAAA,GACzBxV,EAAAA,IAAKA,CAAAA,EAAAA,CAAI6B,OAAAA,GAAU2T,EAAAA,EAAAA,CAAAA;KAAAA,EAErB,IAAA,IAARH,EAAAA,GAAeA,EAAAA,GAAOA,EAAAA,CAAKI,MAAAA,CAAOzV,EAAAA,CAAAA,CAAAA;CAQ7B;AAAA,SAAS8T,yCAAAA,CAAQ4B,EAAAA,EAASL,EAAAA,EAAAA;IAAAA,IAE1BtN,EAAAA,GAAQ2M,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,CAAA,CAAA;IAAA,OACvCoB,uBAAAA,CAAYvN,EAAAA,CAAAA,GAAAA,EAAasN,EAAAA,CAAAA,IAC5BtN,CAAAA,EAAAA,CAAAA,EAAAA,GAAe2N,EAAAA,EAAAA,EACf3N,EAAAA,CAAAA,GAAAA,GAAcsN,EAAAA,EACdtN,EAAAA,CAAAA,GAAAA,GAAiB2N,EAAAA,CAAAA,EAGX3N,EAAAA,CAAAA,EAAAA,CAAAA;CAOD;AAAA,SAASgM,yCAAAA,CAAYzI,EAAAA,EAAU+J,EAAAA,EAAAA;IAAAA,OACrCjB,uBAAAA,GAAc,CAAA,EACPN,yCAAAA,CAAQ,WAAA;QAAA,OAAMxI,EAAAA,CAAAA;KAAAA,EAAU+J,EAAAA,CAAAA,CAAAA;CAMzB;AAAA,SAASrB,yCAAAA,CAAWhS,EAAAA,EAAAA;IAAAA,IACpBuF,EAAAA,GAAWyK,uBAAAA,CAAiBhQ,OAAAA,CAAQA,EAAAA,CAAAA,GAAAA,CAAAA,EAKpC+F,EAAAA,GAAQ2M,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,CAAA,CAAA;IAAA,OAI3CnM,EAAAA,CAAAA,CAAAA,GAAiB/F,EAAAA,EACZuF,EAAAA,GAEe,CAAA,IAAA,IAAhBQ,EAAAA,CAAAA,EAAAA,IACHA,CAAAA,EAAAA,CAAAA,EAAAA,GAAAA,CAAe,CAAA,EACfR,EAAAA,CAASO,GAAAA,CAAIkK,uBAAAA,CAAAA,CAAAA,EAEPzK,EAAAA,CAAS3H,KAAAA,CAAMoG,KAAAA,CAAAA,GANAhE,EAAAA,CAAAA,EAAAA,CAAAA;CAahB;AAAA,SAASiS,yCAAAA,CAAcjO,EAAAA,EAAO2P,EAAAA,EAAAA;IAChCpV,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ0T,aAAAA,IACX1T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ0T,aAAAA,CAAc0B,EAAAA,GAAYA,EAAAA,CAAU3P,EAAAA,CAAAA,GAASA,EAAAA,CAAAA;CAOhD;AAAA,SAAS4P,yCAAAA,CAAiB/M,EAAAA,EAAAA;IAAAA,IAE1Bd,EAAAA,GAAQ2M,uBAAAA,CAAaR,uBAAAA,EAAAA,EAAgB,EAAA,CAAA,EACrC2B,EAAAA,GAAWrC,yCAAAA,EAAAA;IAAAA,OACjBzL,EAAAA,CAAAA,EAAAA,GAAec,EAAAA,EACVmJ,uBAAAA,CAAiB5G,iBAAAA,IACrB4G,CAAAA,uBAAAA,CAAiB5G,iBAAAA,GAAoB,SAAA0K,GAAAA,EAAAA;QAChC/N,EAAAA,CAAAA,EAAAA,IAAcA,EAAAA,CAAAA,EAAAA,CAAa+N,GAAAA,CAAAA,EAC/BD,EAAAA,CAAS,CAAA,CAAA,CAAGC,GAAAA,CAAAA;KAAAA,CAAAA,EAGP;QACND,EAAAA,CAAS,CAAA,CAAA;QACT,WAAA;YACCA,EAAAA,CAAS,CAAA,CAAA,CAAA,KAAGzV,CAAAA,CAAAA;SAAAA;KAAAA,CAAAA;CAQf;AAAA,SAAS2V,uBAAAA,GAAAA;IAAAA,IACJ7S,EAAAA;IAAAA,IAEJmR,uBAAAA,CAAkBvR,IAAAA,CAAK,SAACC,GAAAA,EAAGC,EAAAA,EAAAA;QAAAA,OAAMD,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAkBC,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA;KAAAA,CAAAA,EAC5CE,EAAAA,GAAYmR,uBAAAA,CAAkBnF,GAAAA,EAAAA,EAAAA,IAC/BhM,EAAAA,CAAAA,GAAAA,EAAAA,IAAAA;QAEJA,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAkCmF,OAAAA,CAAQ2N,uBAAAA,CAAAA,EAC1C9S,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAkCmF,OAAAA,CAAQ4N,uBAAAA,CAAAA,EAC1C/S,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAoC,EAAA;KACnC,CAAA,OAAO4D,EAAAA,EAAAA;QACR5D,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAoC,EAAA,EACpC3C,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,CAAoBuG,EAAAA,EAAG5D,EAAAA,CAAAA,GAAAA,CAAAA;KAAAA;CAtR1B3C;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAgB,SAAAJ,GAAAA,EAAAA;IACf6R,uBAAAA,GAAmB,IAAA,EACfsC,uBAAAA,IAAeA,uBAAAA,CAAcnU,GAAAA,CAAAA;CAAAA,EAGlCI,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAkB,SAAAJ,GAAAA,EAAAA;IACbsS,uBAAAA,IAAiBA,uBAAAA,CAAgBtS,GAAAA,CAAAA,EAGrC+T,uBAAAA,GAAe,CAAA,CAAA;IAAA,IAETU,EAAAA,GAHN5C,CAAAA,uBAAAA,GAAmB7R,GAAAA,CAAAA,GAAAA,CAAAA,CAAAA,GAIfyU;IAAAA,EAAAA,IACHA,CAAAA,EAAAA,CAAAA,GAAAA,CAAsBvM,OAAAA,CAAQ2N,uBAAAA,CAAAA,EAC9BpB,EAAAA,CAAAA,GAAAA,CAAsBvM,OAAAA,CAAQ4N,uBAAAA,CAAAA,EAC9BrB,EAAAA,CAAAA,GAAAA,GAAwB,EAAA,CAAA;CAAA,EAI1BrU,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQoI,MAAAA,GAAS,SAAAxI,EAAAA,EAAAA;IACZoU,uBAAAA,IAAcA,uBAAAA,CAAapU,EAAAA,CAAAA,CAAAA;IAAAA,IAEzBsC,EAAAA,GAAItC,EAAAA,CAAAA,GACNsC;IAAAA,EAAAA,IAAKA,EAAAA,CAAAA,GAAAA,IAAaA,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAA0BhB,MAAAA,IAsSzB,CAAA,CAAA,KArSX4S,uBAAAA,CAAkB3R,IAAAA,CAAKD,EAAAA,CAAAA,IAqSP0R,uBAAAA,KAAY5T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ2V,qBAAAA,IAAAA,CAC/C/B,CAAAA,uBAAAA,GAAU5T,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ2V,qBAAAA,CAAAA,IAvBpB,SAAwB5K,GAAAA,EAAAA;QAAAA,IAQnB6K,EAAAA,EAPEC,EAAAA,GAAO,WAAA;YACZC,YAAAA,CAAaC,EAAAA,CAAAA,EACTC,uBAAAA,IAASC,oBAAAA,CAAqBL,EAAAA,CAAAA,EAClCtK,UAAAA,CAAWP,GAAAA,CAAAA;SAAAA,EAENgL,EAAAA,GAAUzK,UAAAA,CAAWuK,EAAAA,EAhTR,GAAA,CAAA,AAmTfG;QAAAA,uBAAAA,IACHJ,CAAAA,EAAAA,GAAMD,qBAAAA,CAAsBE,EAAAA,CAAAA,CAAAA;KAAAA,CAAAA,CAcAL,uBAAAA,CAAAA,CAAAA,EArS7B/D,uBAAAA,GAAmB,IAAA;CAAA,EAGpBzR,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAkB,SAACJ,GAAAA,EAAOgD,EAAAA,EAAAA;IACzBA,EAAAA,CAAYF,IAAAA,CAAK,SAAAC,GAAAA,EAAAA;QAAAA,IAAAA;YAEfA,GAAAA,CAAAA,GAAAA,CAA2BmF,OAAAA,CAAQ2N,uBAAAA,CAAAA,EACnC9S,GAAAA,CAAAA,GAAAA,GAA6BA,GAAAA,CAAAA,GAAAA,CAA2BuT,MAAAA,CAAO,SAAA5N,GAAAA,EAAAA;gBAAAA,OAAAA,CAC9DA,GAAAA,CAAAA,EAAAA,IAAYoN,uBAAAA,CAAapN,GAAAA,CAAAA,CAAAA;aAAAA,CAAAA;SAEzB,CAAA,OAAO/B,GAAAA,EAAAA;YACR3D,EAAAA,CAAYF,IAAAA,CAAK,SAAAR,GAAAA,EAAAA;gBACZA,GAAAA,CAAAA,GAAAA,IAAoBA,CAAAA,GAAAA,CAAAA,GAAAA,GAAqB,EAAA,CAAA;aAAA,CAAA,EAE9CU,EAAAA,GAAc,EAAA,EACd5C,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,CAAoBuG,GAAAA,EAAG5D,GAAAA,CAAAA,GAAAA,CAAAA;SAAAA;KAAAA,CAAAA,EAIrBsR,uBAAAA,IAAWA,uBAAAA,CAAUrU,GAAAA,EAAOgD,EAAAA,CAAAA;CAAAA,EAGjC5C,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQsE,OAAAA,GAAU,SAAA1E,GAAAA,EAAAA;IACbsU,uBAAAA,IAAkBA,uBAAAA,CAAiBtU,GAAAA,CAAAA,CAAAA;IAAAA,IAIlCuW,EAAAA,EAFCjU,GAAAA,GAAItC,GAAAA,CAAAA,GACNsC;IAAAA,GAAAA,IAAKA,GAAAA,CAAAA,GAAAA,IAERA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA,EAAAA,CAAgB4F,OAAAA,CAAQ,SAAAkD,GAAAA,EAAAA;QAAAA,IAAAA;YAEtByK,uBAAAA,CAAczK,GAAAA,CAAAA;SACb,CAAA,OAAOzE,GAAAA,EAAAA;YACR4P,EAAAA,GAAa5P,GAAAA;SAAAA;KAAAA,CAAAA,EAGX4P,EAAAA,IAAYnW,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,CAAoBmW,EAAAA,EAAYjU,GAAAA,CAAAA,GAAAA,CAAAA,CAAAA;CAAAA,CA8NlD;AAAA,IAAI8T,uBAAAA,GAA0C,UAAA,IAAA,OAAzBL,qBAAAA,AA2CrB;AAAA,SAASF,uBAAAA,CAAcW,GAAAA,EAAAA;IAAAA,IAGhBC,GAAAA,GAAO5E,uBAAAA,EACT6E,GAAAA,GAAUF,GAAAA,CAAAA,GACQ;IAAA,UAAA,IAAA,OAAXE,GAAAA,IACVF,CAAAA,GAAAA,CAAAA,GAAAA,GAAAA,KAAgBvW,CAAAA,EAChByW,GAAAA,EAAAA,CAAAA,EAED7E,uBAAAA,GAAmB4E,GAAAA;CAOpB;AAAA,SAASX,uBAAAA,CAAaU,GAAAA,EAAAA;IAAAA,IAGfC,GAAAA,GAAO5E,uBAAAA,AACb2E;IAAAA,GAAAA,CAAAA,GAAAA,GAAgBA,GAAAA,CAAAA,EAAAA,EAAAA,EAChB3E,uBAAAA,GAAmB4E,GAAAA;CAOpB;AAAA,SAAStB,uBAAAA,CAAYwB,GAAAA,EAASC,GAAAA,EAAAA;IAAAA,OAAAA,CAE3BD,GAAAA,IACDA,GAAAA,CAAQrV,MAAAA,KAAWsV,GAAAA,CAAQtV,MAAAA,IAC3BsV,GAAAA,CAAQ9T,IAAAA,CAAK,SAACoQ,GAAAA,EAAKsB,EAAAA,EAAAA;QAAAA,OAAUtB,GAAAA,KAAQyD,GAAAA,CAAQnC,EAAAA,CAAAA,CAAAA;KAAAA,CAAAA,CAAAA;CAI/C;AAAA,SAASG,uBAAAA,CAAezB,GAAAA,EAAK2D,GAAAA,EAAAA;IAAAA,OACT,UAAA,IAAA,OAALA,GAAAA,GAAkBA,GAAAA,CAAE3D,GAAAA,CAAAA,GAAO2D,GA/X1C,CAAA;CAAA;AXIO,SAAShW,uBAAAA,CAAOC,EAAAA,EAAKrB,EAAAA,EAAAA;IAAAA,IACtB,IAAIK,EAAAA,IAAKL,EAAAA,CAAOqB,EAAAA,CAAIhB,EAAAA,CAAAA,GAAKL,EAAAA,CAAMK,EAAAA,CAAAA,CAAAA;IAAAA,OACPgB,EAAAA,CAAAA;CASvB;AAAA,SAAS6K,uBAAAA,CAAe/I,EAAAA,EAAGC,EAAAA,EAAAA;IAAAA,IAC5B,IAAI/C,EAAAA,IAAK8C,EAAAA,CAAAA,IAAa,UAAA,KAAN9C,EAAAA,IAAAA,CAAsBA,CAAAA,EAAAA,IAAK+C,EAAAA,CAAAA,EAAI,OAAA,CAAO,CAAA,CAAA;IAAA,IACtD,IAAI/C,EAAAA,IAAK+C,EAAAA,CAAAA,IAAa,UAAA,KAAN/C,EAAAA,IAAoB8C,EAAAA,CAAE9C,EAAAA,CAAAA,KAAO+C,EAAAA,CAAE/C,EAAAA,CAAAA,EAAI,OAAA,CAAO,CAAA,CAAA;IAAA,OAAA,CACxD,CAAA,CAAA;CCfD;AAAA,SAAS8L,yCAAAA,CAAcC,EAAAA,EAAAA;IAAAA,IAAAA,CACxBpM,KAAAA,GAAQoM,EAAAA;CCGP;AAAA,SAASC,yCAAAA,CAAKxJ,EAAAA,EAAGyJ,EAAAA,EAAAA;IAAAA,SACdC,EAAAA,CAAaC,EAAAA,EAAAA;QAAAA,IACjBpM,EAAAA,GAAMgK,IAAAA,CAAKpK,KAAAA,CAAMI,GAAAA,EACjBqM,EAAAA,GAAYrM,EAAAA,IAAOoM,EAAAA,CAAUpM,GAAAA;QAAAA,OAAAA,CAC5BqM,EAAAA,IAAarM,EAAAA,IACjBA,CAAAA,EAAAA,CAAI0B,IAAAA,GAAO1B,EAAAA,CAAI,IAAA,CAAA,GAASA,EAAAA,CAAI6B,OAAAA,GAAU,IAAA,CAAA,EAGlCqK,EAAAA,GAAAA,CAIGA,EAAAA,CAASlC,IAAAA,CAAKpK,KAAAA,EAAOwM,EAAAA,CAAAA,IAAAA,CAAeC,EAAAA,GAHpCP,uBAAAA,CAAe9B,IAAAA,CAAKpK,KAAAA,EAAOwM,EAAAA,CAAAA,CAAAA;KAAAA;IAAAA,SAM3BE,EAAAA,CAAO1M,EAAAA,EAAAA;QAAAA,OAAAA,IAAAA,CACVwI,qBAAAA,GAAwB+D,EAAAA,EACtB7K,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcmB,EAAAA,EAAG7C,EAAAA,CAAAA,CAAAA;KAAAA;IAAAA,OAEzB0M,EAAAA,CAAOC,WAAAA,GAAc,OAAA,GAAW9J,CAAAA,EAAAA,CAAE8J,WAAAA,IAAe9J,EAAAA,CAAEyD,IAAAA,CAAAA,GAAQ,GAAA,EAC3DoG,EAAAA,CAAO3E,SAAAA,CAAU6E,gBAAAA,GAAAA,CAAmB,CAAA,EACpCF,EAAAA,CAAAA,GAAAA,GAAAA,CAAoB,CAAA,EACbA,EAAAA,CAAAA;CAAAA;ADvBRP,CAAAA,yCAAAA,CAAcpE,SAAAA,GAAY,IAAI5F,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,CAEN0K,oBAAAA,GAAAA,CAAuB,CAAA,EAC/CV,yCAAAA,CAAcpE,SAAAA,CAAUS,qBAAAA,GAAwB,SAASxI,EAAAA,EAAOmI,EAAAA,EAAAA;IAAAA,OACxD+D,uBAAAA,CAAe9B,IAAAA,CAAKpK,KAAAA,EAAOA,EAAAA,CAAAA,IAAUkM,uBAAAA,CAAe9B,IAAAA,CAAKjC,KAAAA,EAAOA,EAAAA,CAAAA,CAAAA;CAAAA,CEVxE;AAAA,IAAI2E,uBAAAA,GAAcnM,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAClBA;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAgB,SAAAJ,EAAAA,EAAAA;IACXA,EAAAA,CAAMR,IAAAA,IAAQQ,EAAAA,CAAMR,IAAAA,CAAAA,GAAAA,IAAmBQ,EAAAA,CAAMH,GAAAA,IAChDG,CAAAA,EAAAA,CAAMP,KAAAA,CAAMI,GAAAA,GAAMG,EAAAA,CAAMH,GAAAA,EACxBG,EAAAA,CAAMH,GAAAA,GAAM,IAAA,CAAA,EAET0M,uBAAAA,IAAaA,uBAAAA,CAAYvM,EAAAA,CAAAA;CAAAA,CAG9B;AAAA,IAAawM,uBAAAA,GACM,WAAA,IAAA,OAAVC,MAAAA,IACPA,MAAAA,CAAOC,GAAAA,IACPD,MAAAA,CAAOC,GAAAA,CAAI,mBAAA,CAAA,IACZ,IAAA,AASM;AAAA,SAASC,yCAAAA,CAAWC,EAAAA,EAAAA;IAAAA,SAIjBC,EAAAA,CAAUpN,EAAAA,EAAOI,EAAAA,EAAAA;QAAAA,IACrBiN,EAAAA,GAAQjM,uBAAAA,CAAO,CAAA,CAAA,EAAIpB,EAAAA,CAAAA;QAAAA,OAAAA,OAChBqN,EAAAA,CAAMjN,GAAAA,EAEN+M,EAAAA,CACNE,EAAAA,EAFDjN,CAAAA,EAAAA,GAAMJ,EAAAA,CAAMI,GAAAA,IAAOA,EAAAA,CAAAA,IAGM,CAAA,QAAA,IAAA,OAARA,EAAAA,IAAsB,SAAA,IAAaA,EAAAA,CAAAA,GAAeA,EAAAA,GAAP,IAAA,CAAA,CAAA;KAAA;IAAA,OAK7DgN,EAAAA,CAAUE,QAAAA,GAAWP,uBAAAA,EAKrBK,EAAAA,CAAUpF,MAAAA,GAASoF,EAAAA,EAEnBA,EAAAA,CAAUrF,SAAAA,CAAU6E,gBAAAA,GAAmBQ,EAAAA,CAAAA,GAAAA,GAAAA,CAAuB,CAAA,EAC9DA,EAAAA,CAAUT,WAAAA,GAAc,aAAA,GAAiBQ,CAAAA,EAAAA,CAAGR,WAAAA,IAAeQ,EAAAA,CAAG7G,IAAAA,CAAAA,GAAQ,GAAA,EAC/D8G,EAAAA,CAAAA;CAAAA;AAAAA,IC/CFG,uBAAAA,GAAQ,SAAC5L,EAAAA,EAAUwL,EAAAA,EAAAA;IAAAA,OACR,IAAA,IAAZxL,EAAAA,GAAyB,IAAA,GACtByD,CAAAA,GAAAA,yCAAAA,CAAAA,CAAaA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAazD,EAAAA,CAAAA,CAAU6L,GAAAA,CAAIL,EAAAA,CAAAA,CAAAA,CAAAA;CAAAA,EAInCM,yCAAAA,GAAW;IACvBD,GAAAA,EAAKD,uBAAAA;IACL9E,OAAAA,EAAS8E,uBAAAA;IACTG,KAAAA,EAAAA,SAAM/L,GAAAA,EAAAA;QAAAA,OACEA,GAAAA,GAAWyD,CAAAA,GAAAA,yCAAAA,CAAAA,CAAazD,GAAAA,CAAAA,CAAUE,MAAAA,GAAS,CAAA,CAAA;KAAA;IAEnD8L,IAAAA,EAAAA,SAAKhM,GAAAA,EAAAA;QAAAA,IACEiM,EAAAA,GAAaxI,CAAAA,GAAAA,yCAAAA,CAAAA,CAAazD,GAAAA,CAAAA;QAAAA,IACN,CAAA,KAAtBiM,EAAAA,CAAW/L,MAAAA,EAAc,MAAM,eAAA,CAAA;QAAA,OAC5B+L,EAAAA,CAAW,CAAA,CAAA,CAAA;KAAA;IAEnBC,OAAAA,EAASzI,CAAAA,GAAAA,yCAAAA,CAAAA;CAAAA,EChBJ0I,uBAAAA,GAAgBnN,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GACtBA;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAsB,SAASwK,GAAAA,EAAO7D,GAAAA,EAAU9D,EAAAA,EAAAA;IAAAA,IAC3C2H,GAAAA,CAAMW,IAAAA,EAAAA;QAAAA,IAAAA,IAELxI,EAAAA,EACA/C,EAAAA,GAAQ+G,GAAAA,EAEJ/G,EAAAA,GAAQA,EAAAA,CAAAA,EAAAA,EAAAA,IACV+C,CAAAA,EAAAA,GAAY/C,EAAAA,CAAAA,GAAAA,CAAAA,IAAqB+C,EAAAA,CAAAA,GAAAA,EAAAA,OAChB,IAAA,IAAjBgE,GAAAA,CAAAA,GAAAA,IACHA,CAAAA,GAAAA,CAAAA,GAAAA,GAAgB9D,EAAAA,CAAAA,GAAAA,EAChB8D,GAAAA,CAAAA,GAAAA,GAAqB9D,EAAAA,CAAAA,GAAAA,CAAAA,EAGfF,EAAAA,CAAAA,GAAAA,CAA2B6H,GAAAA,EAAO7D,GAAAA,CAAAA,CAI5CwG;KAAAA;IAAAA,uBAAAA,CAAc3C,GAAAA,EAAO7D,GAAAA,EAAU9D,EAAAA,CAAAA;CAAAA,CAGhC;AAAA,IAAMuK,uBAAAA,GAAapN,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQsE,OAuE3B;AAAA,SAAgB+I,yCAAAA,GAAAA;IAAAA,IAAAA,CAAAA,GAAAA,GAEgB,CAAA,EAAA,IAAA,CAC1BC,CAAAA,GAAc,IAAA,EAAA,IAAA,CAAA,GAAA,GACQ,IAAA;CAoIrB;AAAA,SAASC,uBAAAA,CAAU3N,GAAAA,EAAAA;IAAAA,IAErB+C,GAAAA,GAAY/C,GAAAA,CAAAA,EAAAA,CAAAA,GAAAA;IAAAA,OACT+C,GAAAA,IAAaA,GAAAA,CAAAA,GAAAA,IAAwBA,GAAAA,CAAAA,GAAAA,CAAqB/C,GAAAA,CAAAA,CAAAA;CAG3D;AAAA,SAAS4N,yCAAAA,CAAKC,GAAAA,EAAAA;IAAAA,IAChBC,GAAAA,EACA/K,EAAAA,EACA6H,EAAAA;IAAAA,SAEKmD,EAAAA,CAAKtO,EAAAA,EAAAA;QAAAA,IACRqO,GAAAA,IACJA,CAAAA,GAAAA,GAAOD,GAAAA,EAAAA,CAAAA,CACFtC,IAAAA,CACJ,SAAAyC,GAAAA,EAAAA;YACCjL,EAAAA,GAAYiL,GAAAA,CAAQC,OAAAA,IAAWD,GAAAA;SAAAA,EAEhC,SAAArH,GAAAA,EAAAA;YACCiE,EAAAA,GAAQjE,GAAAA;SAAAA,CAAAA,EAKPiE,EAAAA,EAAAA,MACGA,EAAAA,CAAAA;QAAAA,IAAAA,CAGF7H,EAAAA,EAAAA,MACE+K,GAAAA,CAAAA;QAAAA,OAGA3M,CAAAA,GAAAA,yCAAAA,CAAAA,CAAc4B,EAAAA,EAAWtD,EAAAA,CAAAA,CAAAA;KAAAA;IAAAA,OAGjCsO,EAAAA,CAAK3B,WAAAA,GAAc,MAAA,EACnB2B,EAAAA,CAAAA,GAAAA,GAAAA,CAAkB,CAAA,EACXA,EAAAA,CAAAA;CCnQR;AAAA,SAAgBG,yCAAAA,GAAAA;IAAAA,IAAAA,CACVC,CAAAA,GAAQ,IAAA,EAAA,IAAA,CACRC,CAAAA,GAAO,IAAA;CDcbhO;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQsE,OAAAA,GAAU,SAAS1E,GAAAA,EAAAA;IAAAA,IAEpB+C,GAAAA,GAAY/C,GAAAA,CAAAA,GACd+C;IAAAA,GAAAA,IAAaA,GAAAA,CAAAA,GAAAA,IAChBA,GAAAA,CAAAA,GAAAA,EAAAA,EAOGA,GAAAA,IAAAA,CAAkC,CAAA,KAArB/C,GAAAA,CAAAA,GAAAA,IAChBA,CAAAA,GAAAA,CAAMR,IAAAA,GAAO,IAAA,CAAA,EAGVgO,uBAAAA,IAAYA,uBAAAA,CAAWxN,GAAAA,CAAAA;CAAAA,EAiE5ByN,CAAAA,yCAAAA,CAASjG,SAAAA,GAAY,IAAI5F,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,CAAAA,GAAAA,GAOa,SAASyM,GAAAA,EAASC,GAAAA,EAAAA;IAAAA,IACjDC,EAAAA,GAAsBD,GAAAA,CAAAA,GAAAA,EAGtBhM,EAAAA,GAAIuH,IAEW;IAAA,IAAA,IAAjBvH,EAAAA,CAAEoL,CAAAA,IACLpL,CAAAA,EAAAA,CAAEoL,CAAAA,GAAc,EAAA,CAAA,EAEjBpL,EAAAA,CAAEoL,CAAAA,CAAYnL,IAAAA,CAAKgM,EAAAA,CAAAA,CAAAA;IAAAA,IAEb9C,EAAAA,GAAUkC,uBAAAA,CAAUrL,EAAAA,CAAAA,GAAAA,CAAAA,EAEtBkM,EAAAA,GAAAA,CAAW,CAAA,EACTC,EAAAA,GAAa,WAAA;QACdD,EAAAA,IAEJA,CAAAA,EAAAA,GAAAA,CAAW,CAAA,EACXD,EAAAA,CAAAA,GAAAA,GAAiC,IAAA,EAE7B9C,EAAAA,GACHA,EAAAA,CAAQiD,EAAAA,CAAAA,GAERA,EAAAA,EAAAA,CAAAA;KAAAA,AAIFH;IAAAA,EAAAA,CAAAA,GAAAA,GAAiCE,EAAAA,CAAAA;IAAAA,IAE3BC,EAAAA,GAAuB,WAAA;QAAA,IAAA,CAAA,EACrBpM,EAAAA,CAAAA,GAAAA,EAA2B;YAAA,IAG7BA,EAAAA,CAAEsF,KAAAA,CAAAA,GAAAA,EAAkB;gBAAA,IACjB+G,GAAAA,GAAiBrM,EAAAA,CAAEsF,KAAAA,CAAAA,GACzBtF;gBAAAA,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAmB,CAAA,CAAA,GA5EvB,SAASsM,GAAAA,CAAe5O,GAAAA,EAAO6O,EAAAA,EAAgBC,EAAAA,EAAAA;oBAAAA,OAC1C9O,GAAAA,IACHA,CAAAA,GAAAA,CAAAA,GAAAA,GAAkB,IAAA,EAClBA,GAAAA,CAAAA,GAAAA,GACCA,GAAAA,CAAAA,GAAAA,IACAA,GAAAA,CAAAA,GAAAA,CAAgBiN,GAAAA,CAAI,SAAA9K,GAAAA,EAAAA;wBAAAA,OACnByM,GAAAA,CAAezM,GAAAA,EAAO0M,EAAAA,EAAgBC,EAAAA,CAAAA,CAAAA;qBAAAA,CAAAA,EAGpC9O,GAAAA,CAAAA,GAAAA,IACCA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,KAAgC6O,EAAAA,IAC/B7O,CAAAA,GAAAA,CAAAA,GAAAA,IACH8O,EAAAA,CAAe1J,YAAAA,CAAapF,GAAAA,CAAAA,GAAAA,EAAYA,GAAAA,CAAAA,GAAAA,CAAAA,EAEzCA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAAA,CAA0B,CAAA,EAC1BA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAA8B8O,EAAAA,CAAAA,CAAAA,EAK1B9O,GAAAA,CAAAA;iBAwDoB4O,CACvBD,GAAAA,EACAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,EACAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA;aAAAA;YAAAA,IAMEhB,GAAAA;YAAAA,IAFJrL,EAAAA,CAAE0I,QAAAA,CAAS;gBAAA,GAAA,EAAe1I,EAAAA,CAAAA,GAAAA,GAAwB,IAAA;aAAA,CAAA,EAG1CqL,GAAAA,GAAYrL,EAAAA,CAAEoL,CAAAA,CAAYqB,GAAAA,EAAAA,EACjCpB,GAAAA,CAAUtC,WAAAA,EAAAA;SAAAA;KAAAA,EAUP2D,EAAAA,GAAAA,CAA8C,CAAA,KAA/BV,GAAAA,CAAAA,GAChBhM;IAAAA,EAAAA,CAAAA,GAAAA,EAAAA,CAAAA,GAAgC0M,EAAAA,IACpC1M,EAAAA,CAAE0I,QAAAA,CAAS;QAAA,GAAA,EAAe1I,EAAAA,CAAAA,GAAAA,GAAwBA,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAmB,CAAA,CAAA;KAAA,CAAA,EAEtE+L,GAAAA,CAAQ9C,IAAAA,CAAKkD,EAAAA,EAAYA,EAAAA,CAAAA;CAAAA,EAG1BhB,yCAAAA,CAASjG,SAAAA,CAAUoC,oBAAAA,GAAuB,WAAA;IAAA,IAAA,CACpC8D,CAAAA,GAAc,EAAA;CAAA,EAQpBD,yCAAAA,CAASjG,SAAAA,CAAUC,MAAAA,GAAS,SAAShI,GAAAA,EAAOmI,GAAAA,EAAAA;IAAAA,IACvCiC,IAAAA,CAAAA,GAAAA,EAA0B;QAAA,IAIzBA,IAAAA,CAAAA,GAAAA,CAAAA,GAAAA,EAAuB;YAAA,IACpBgF,GAAAA,GAAiB9F,QAAAA,CAAS5H,aAAAA,CAAc,KAAA,CAAA,EACxC8N,EAAAA,GAAoBpF,IAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAsB,CAAA,CAAA,CAAA,GAAA;YAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAC1B,CAAA,CAAA,GArJzB,SAASqF,GAAAA,CAAclP,GAAAA,EAAO6O,GAAAA,EAAgB1L,GAAAA,EAAAA;gBAAAA,OACzCnD,GAAAA,IACCA,CAAAA,GAAAA,CAAAA,GAAAA,IAAoBA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,IACvBA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA,EAAAA,CAA+BkI,OAAAA,CAAQ,SAAAiH,GAAAA,EAAAA;oBACR,UAAA,IAAA,OAAnBA,GAAAA,CAAAA,GAAAA,IAA+BA,GAAAA,CAAAA,GAAAA,EAAAA;iBAAAA,CAAAA,EAG3CnP,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAA2B,IAAA,CAAA,EAIJ,IAAA,IADxBA,CAAAA,GAAAA,GAAQa,uBAAAA,CAAO,CAAA,CAAA,EAAIb,GAAAA,CAAAA,CAAAA,CAAAA,GAAAA,IAEdA,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,KAAgCmD,GAAAA,IACnCnD,CAAAA,GAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAA8B6O,GAAAA,CAAAA,EAE/B7O,GAAAA,CAAAA,GAAAA,GAAmB,IAAA,CAAA,EAGpBA,GAAAA,CAAAA,GAAAA,GACCA,GAAAA,CAAAA,GAAAA,IACAA,GAAAA,CAAAA,GAAAA,CAAgBiN,GAAAA,CAAI,SAAA9K,GAAAA,EAAAA;oBAAAA,OACnB+M,GAAAA,CAAc/M,GAAAA,EAAO0M,GAAAA,EAAgB1L,GAAAA,CAAAA,CAAAA;iBAAAA,CAAAA,CAAAA,EAIjCnD,GAAAA,CAAAA;aA4HsBkP,CAC1BrF,IAAAA,CAAAA,GAAAA,EACAgF,GAAAA,EACCI,EAAAA,CAAAA,GAAAA,GAAuCA,EAAAA,CAAAA,GAAAA,CAAAA;SAAAA;QAAAA,IAAAA,CAAAA,GAAAA,GAIf,IAAA;KAAA;IAAA,IAKtBG,EAAAA,GACLxH,GAAAA,CAAAA,GAAAA,IAAoBzG,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcQ,CAAAA,GAAAA,yCAAAA,CAAAA,EAAU,IAAA,EAAMlC,GAAAA,CAAM2P,QAAAA,CAAAA;IAAAA,OACrDA,EAAAA,IAAUA,CAAAA,EAAAA,CAAAA,GAAAA,GAAsB,IAAA,CAAA,EAE7B;QACNjO,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcQ,CAAAA,GAAAA,yCAAAA,CAAAA,EAAU,IAAA,EAAMiG,GAAAA,CAAAA,GAAAA,GAAmB,IAAA,GAAOnI,GAAAA,CAAM2B,QAAAA,CAAAA;QAC9DgO,EAAAA;KAAAA,CAAAA;CAAAA,CChMF;AAAA,IAAM3D,uBAAAA,GAAU,SAAC4D,GAAAA,EAAMlN,GAAAA,EAAOnB,GAAAA,EAAAA;IAAAA,IAAAA,EACvBA,GAAAA,CAdgB,CAAA,CAAA,KAcSA,GAAAA,CAfR,CAAA,CAAA,IAqBtBqO,GAAAA,CAAKjB,CAAAA,CAAKkB,MAAAA,CAAOnN,GAAAA,CAAAA,EAQhBkN,GAAAA,CAAK5P,KAAAA,CAAM8P,WAAAA,IACmB,CAAA,GAAA,KAA9BF,GAAAA,CAAK5P,KAAAA,CAAM8P,WAAAA,CAAY,CAAA,CAAA,IAAA,CAAcF,GAAAA,CAAKjB,CAAAA,CAAKoB,IAAAA,CAAAA,EAAAA,IAQjDxO,GAAAA,GAAOqO,GAAAA,CAAKlB,CAAAA,EACLnN,GAAAA,EAAM;QAAA,MACLA,GAAAA,CAAKM,MAAAA,GAAS,CAAA,EACpBN,GAAAA,CAAK+N,GAAAA,EAAL/N,EAAAA,CAAAA;QAAAA,IAEGA,GAAAA,CA1CiB,CAAA,CAAA,GA0CMA,GAAAA,CA3CL,CAAA,CAAA,EAAA,MA8CtBqO;QAAAA,GAAAA,CAAKlB,CAAAA,GAAQnN,GAAAA,GAAOA,GAAAA,CA5CJ,CAAA,CAAA;KAAA;CAAA,ACDlB;AAAA,SAASyO,uBAAAA,CAAgBhQ,GAAAA,EAAAA;IAAAA,OAAAA,IAAAA,CACnB4I,eAAAA,GAAkB,WAAA;QAAA,OAAM5I,GAAAA,CAAMoC,OAAAA,CAAAA;KAAAA,EAC5BpC,GAAAA,CAAM2B,QAAAA,CAAAA;CAUd;AAAA,SAASsO,uBAAAA,CAAOjQ,GAAAA,EAAAA;IAAAA,IACTkQ,GAAAA,GAAQ9F,IAAAA,EACV+F,GAAAA,GAAYnQ,GAAAA,CAAMoQ,CAEtBF;IAAAA,GAAAA,CAAM/F,oBAAAA,GAAuB,WAAA;QAC5BnC,CAAAA,GAAAA,yCAAAA,CAAAA,CAAO,IAAA,EAAMkI,GAAAA,CAAMG,CAAAA,CAAAA,EACnBH,GAAAA,CAAMG,CAAAA,GAAQ,IAAA,EACdH,GAAAA,CAAME,CAAAA,GAAa,IAAA;KAAA,EAKhBF,GAAAA,CAAME,CAAAA,IAAcF,GAAAA,CAAME,CAAAA,KAAeD,GAAAA,IAC5CD,GAAAA,CAAM/F,oBAAAA,EAAAA,EAKHnK,GAAAA,CAAAA,GAAAA,GACEkQ,CAAAA,GAAAA,CAAMG,CAAAA,IACVH,CAAAA,GAAAA,CAAME,CAAAA,GAAaD,GAAAA,EAGnBD,GAAAA,CAAMG,CAAAA,GAAQ;QACbjH,QAAAA,EAAU,CAAA;QACV5H,UAAAA,EAAY2O,GAAAA;QACZxG,UAAAA,EAAY,EAAA;QACZlE,WAAAA,EAAAA,SAAY/C,GAAAA,EAAAA;YAAAA,IAAAA,CACNiH,UAAAA,CAAW7G,IAAAA,CAAKJ,GAAAA,CAAAA,EACrBwN,GAAAA,CAAME,CAAAA,CAAW3K,WAAAA,CAAY/C,GAAAA,CAAAA;SAAAA;QAE9BiD,YAAAA,EAAAA,SAAajD,GAAAA,EAAO4N,CAAAA,EAAAA;YAAAA,IAAAA,CACd3G,UAAAA,CAAW7G,IAAAA,CAAKJ,GAAAA,CAAAA,EACrBwN,GAAAA,CAAME,CAAAA,CAAW3K,WAAAA,CAAY/C,GAAAA,CAAAA;SAAAA;QAE9BjB,WAAAA,EAAAA,SAAYiB,GAAAA,EAAAA;YAAAA,IAAAA,CACNiH,UAAAA,CAAWuB,MAAAA,CAAOd,IAAAA,CAAKT,UAAAA,CAAWpH,OAAAA,CAAQG,GAAAA,CAAAA,KAAW,CAAA,EAAG,CAAA,CAAA,EAC7DwN,GAAAA,CAAME,CAAAA,CAAW3O,WAAAA,CAAYiB,GAAAA,CAAAA;SAAAA;KAAAA,CAAAA,EAMhCsF,CAAAA,GAAAA,yCAAAA,CAAAA,CACCtG,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcsO,uBAAAA,EAAiB;QAAE5N,OAAAA,EAAS8N,GAAAA,CAAM9N,OAAAA;KAAAA,EAAWpC,GAAAA,CAAAA,GAAAA,CAAAA,EAC3DkQ,GAAAA,CAAMG,CAAAA,CAAAA,CAAAA,GAKCH,GAAAA,CAAMG,CAAAA,IACdH,GAAAA,CAAM/F,oBAAAA,EAAAA;CASD;AAAA,SAASoG,yCAAAA,CAAahQ,GAAAA,EAAO4P,GAAAA,EAAAA;IAAAA,OAC5BzO,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcuO,uBAAAA,EAAQ;QAAA,GAAA,EAAU1P,GAAAA;QAAO6P,CAAAA,EAAYD,GAAAA;KAAAA,CAAAA,CAAAA;CAAAA;ADrB3D1B,CAAAA,yCAAAA,CAAa1G,SAAAA,GAAY,IAAI5F,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,CAAAA,GAAAA,GAEO,SAASO,GAAAA,EAAAA;IAAAA,IACtCkN,GAAAA,GAAOxF,IAAAA,EACPoG,GAAAA,GAAYtC,uBAAAA,CAAU0B,GAAAA,CAAAA,GAAAA,CAAAA,EAExBrO,GAAAA,GAAOqO,GAAAA,CAAKjB,CAAAA,CAAK8B,GAAAA,CAAI/N,GAAAA,CAAAA;IAAAA,OACzBnB,GAAAA,CA5DuB,CAAA,CAAA,EAAA,EA8DhB,SAAAmP,EAAAA,EAAAA;QAAAA,IACAC,EAAAA,GAAmB,WAAA;YACnBf,GAAAA,CAAK5P,KAAAA,CAAM8P,WAAAA,GAKfvO,CAAAA,GAAAA,CAAKuB,IAAAA,CAAK4N,EAAAA,CAAAA,EACV1E,uBAAAA,CAAQ4D,GAAAA,EAAMlN,GAAAA,EAAOnB,GAAAA,CAAAA,CAAAA,GAHrBmP,EAAAA,EAAAA;SAAAA,AAMEF;QAAAA,GAAAA,GACHA,GAAAA,CAAUG,EAAAA,CAAAA,GAEVA,EAAAA,EAAAA;KAAAA,CAAAA;CAAAA,EAKHlC,yCAAAA,CAAa1G,SAAAA,CAAUC,MAAAA,GAAS,SAAShI,GAAAA,EAAAA;IAAAA,IAAAA,CACnC0O,CAAAA,GAAQ,IAAA,EAAA,IAAA,CACRC,CAAAA,GAAO,IAAIiC,GAAAA,CAAAA;IAAAA,IAEVjP,GAAAA,GAAWyD,CAAAA,GAAAA,yCAAAA,CAAAA,CAAapF,GAAAA,CAAM2B,QAAAA,CAAAA,AAChC3B;IAAAA,GAAAA,CAAM8P,WAAAA,IAAwC,GAAA,KAAzB9P,GAAAA,CAAM8P,WAAAA,CAAY,CAAA,CAAA,IAI1CnO,GAAAA,CAASkP,OAAAA,EAAAA,CAAAA;IAAAA,IAIL,IAAIxQ,GAAAA,GAAIsB,GAAAA,CAASE,MAAAA,EAAQxB,GAAAA,EAAAA,EAAAA,IAAAA,CAYxBsO,CAAAA,CAAKmC,GAAAA,CAAInP,GAAAA,CAAStB,GAAAA,CAAAA,EAAK+J,IAAAA,CAAKsE,CAAAA,GAAQ;QAAC,CAAA;QAAG,CAAA;QAAGtE,IAAAA,CAAKsE,CAAAA;KAAAA,CAAAA,CAAAA;IAAAA,OAE/C1O,GAAAA,CAAM2B,QAAAA,CAAAA;CAAAA,EAGd8M,yCAAAA,CAAa1G,SAAAA,CAAUY,kBAAAA,GAAqB8F,yCAAAA,CAAa1G,SAAAA,CAAUO,iBAAAA,GAAoB,WAAA;IAAA,IAAA,GAAA,GAAA,IAAA;IAAA,IAAA,CAOjFqG,CAAAA,CAAKlG,OAAAA,CAAQ,SAAClH,GAAAA,EAAMmB,GAAAA,EAAAA;QACxBsJ,uBAAAA,CAAQkE,GAAAA,EAAMxN,GAAAA,EAAOnB,GAAAA,CAAAA;KAAAA,CAAAA;CAAAA,CAAAA;AAAAA,IEnHVwP,uBAAAA,GACM,WAAA,IAAA,OAAV/D,MAAAA,IAAyBA,MAAAA,CAAOC,GAAAA,IAAOD,MAAAA,CAAOC,GAAAA,CAAI,eAAA,CAAA,IAC1D,KAAA,EAEK+D,uBAAAA,GAAAA,2OAAc,EAEdC,uBAAAA,GAA6B,WAAA,IAAA,OAAb3H,QAAAA,EAKhB4H,uBAAAA,GAAoB,SAAAnR,GAAAA,EAAAA;IAAAA,OACP,CAAA,WAAA,IAAA,OAAViN,MAAAA,IAA4C,QAAA,IAAA,OAAZA,MAAAA,EAAAA,GAAAA,iBAAAA,aAErC,CAAA,CACD3G,IAAAA,CAAKtG,GAAAA,CAAAA,CAAAA;CAAAA,AAuCR;AAAA,SAAgBiI,yCAAAA,CAAOzH,GAAAA,EAAO4Q,GAAAA,EAAQzF,GAAAA,EAAAA;IAAAA,OAGb,IAAA,IAApByF,GAAAA,CAAAA,GAAAA,IACHA,CAAAA,GAAAA,CAAOC,WAAAA,GAAc,EAAA,CAAA,EAGtBC,CAAAA,GAAAA,yCAAAA,CAAAA,CAAa9Q,GAAAA,EAAO4Q,GAAAA,CAAAA,EACG,UAAA,IAAA,OAAZzF,GAAAA,IAAwBA,GAAAA,EAAAA,EAE5BnL,GAAAA,GAAQA,GAAAA,CAAAA,GAAAA,GAAmB,IAAA,CAAA;CAGnC;AAAA,SAAgByF,yCAAAA,CAAQzF,GAAAA,EAAO4Q,GAAAA,EAAQzF,GAAAA,EAAAA;IAAAA,OACtC4F,CAAAA,GAAAA,yCAAAA,CAAAA,CAAc/Q,GAAAA,EAAO4Q,GAAAA,CAAAA,EACE,UAAA,IAAA,OAAZzF,GAAAA,IAAwBA,GAAAA,EAAAA,EAE5BnL,GAAAA,GAAQA,GAAAA,CAAAA,GAAAA,GAAmB,IAAA,CAAA;CArDnC4B;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAU4F,SAAAA,CAAU6E,gBAAAA,GAAmB,CAAA,CAAA,EASvC;IACC,oBAAA;IACA,2BAAA;IACA,qBAAA;CAAA,CACCnE,OAAAA,CAAQ,SAAAxI,GAAAA,EAAAA;IACTsR,MAAAA,CAAOC,cAAAA,CAAerP,CAAAA,GAAAA,yCAAAA,CAAAA,CAAU4F,SAAAA,EAAW9H,GAAAA,EAAK;QAC/CwR,YAAAA,EAAAA,CAAc,CAAA;QACdhB,GAAAA,EAAAA,WAAAA;YAAAA,OACQrG,IAAAA,CAAK,SAAA,GAAYnK,GAAAA,CAAAA,CAAAA;SAAAA;QAEzB6Q,GAAAA,EAAAA,SAAIY,GAAAA,EAAAA;YACHH,MAAAA,CAAOC,cAAAA,CAAepH,IAAAA,EAAMnK,GAAAA,EAAK;gBAChCwR,YAAAA,EAAAA,CAAc,CAAA;gBACdE,QAAAA,EAAAA,CAAU,CAAA;gBACVvL,KAAAA,EAAOsL,GAAAA;aAAAA,CAAAA;SAAAA;KAAAA,CAAAA;CAAAA,CAAAA,CAiCX;AAAA,IAAIE,uBAAAA,GAAejR,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ0G,KAS3B;AAAA,SAASwK,uBAAAA,GAAAA,CAAAA,CAET;AAAA,SAASC,uBAAAA,GAAAA;IAAAA,OACD1H,IAAAA,CAAK2H,YAAAA,CAAAA;CAGb;AAAA,SAASC,uBAAAA,GAAAA;IAAAA,OACD5H,IAAAA,CAAK6H,gBAAAA,CAAAA;CAfbtR;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQ0G,KAAAA,GAAQ,SAAAH,GAAAA,EAAAA;IAAAA,OACX0K,uBAAAA,IAAc1K,CAAAA,GAAAA,GAAI0K,uBAAAA,CAAa1K,GAAAA,CAAAA,CAAAA,EACnCA,GAAAA,CAAEgL,OAAAA,GAAUL,uBAAAA,EACZ3K,GAAAA,CAAE4K,oBAAAA,GAAuBA,uBAAAA,EACzB5K,GAAAA,CAAE8K,kBAAAA,GAAqBA,uBAAAA,EACf9K,GAAAA,CAAEiL,WAAAA,GAAcjL,GAAAA,CAAAA;CAAAA,CAazB;AAAA,IA2GIkL,uBAAAA,EA3GAC,uBAAAA,GAAsB;IACzBZ,YAAAA,EAAAA,CAAc,CAAA;IACdhB,GAAAA,EAAAA,WAAAA;QAAAA,OACQrG,IAAAA,CAAKkI,KAAAA,CAAAA;KAAAA;CAAAA,EAIVC,uBAAAA,GAAe5R,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQJ,KAC3BI;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAQJ,KAAAA,GAAQ,SAAAA,GAAAA,EAAAA;IAAAA,IACXR,GAAAA,GAAOQ,GAAAA,CAAMR,IAAAA,EACbC,GAAAA,GAAQO,GAAAA,CAAMP,KAAAA,EACdM,GAAAA,GAAkBN,GAAAA;IAAAA,IAGF,QAAA,IAAA,OAATD,GAAAA,EAAmB;QAAA,IACvByS,EAAAA,GAAAA,CAAAA,CAA0C,KAAvBzS,GAAAA,CAAKwC,OAAAA,CAAQ,GAAA,CAAA;QAAA,IAGjC,IAAIlC,EAAAA,IAFTC,GAAAA,GAAkB,CAAA,CAAA,EAEJN,GAAAA,CAAO;YAAA,IAChBoG,EAAAA,GAAQpG,GAAAA,CAAMK,EAAAA,CAEd4Q;YAAAA,uBAAAA,IAAgB,UAAA,KAAN5Q,EAAAA,IAA6B,UAAA,KAATN,GAAAA,IAInB,OAAA,KAANM,EAAAA,IAAiB,cAAA,IAAkBL,GAAAA,IAAkB,IAAA,IAAToG,EAAAA,IAK9C,CAAA,cAAA,KAAN/F,EAAAA,IACA,OAAA,IAAWL,GAAAA,IACI,IAAA,IAAfA,GAAAA,CAAMoG,KAAAA,GAIN/F,EAAAA,GAAI,OAAA,GACY,UAAA,KAANA,EAAAA,IAAAA,CAA8B,CAAA,KAAV+F,EAAAA,GAM9BA,EAAAA,GAAQ,EAAA,GACE,iBAAiBC,IAAAA,CAAKhG,EAAAA,CAAAA,GAChCA,EAAAA,GAAI,YAAA,GAEJ,6BAA6BgG,IAAAA,CAAKhG,EAAAA,GAAIN,GAAAA,CAAAA,IAAAA,CACrCmR,uBAAAA,CAAkBlR,GAAAA,CAAMD,IAAAA,CAAAA,GAEzBM,EAAAA,GAAI,SAAA,GACM,aAAagG,IAAAA,CAAKhG,EAAAA,CAAAA,GAC5BA,EAAAA,GAAI,WAAA,GACM,YAAYgG,IAAAA,CAAKhG,EAAAA,CAAAA,GAC3BA,EAAAA,GAAI,YAAA,GACM,6BAA6BgG,IAAAA,CAAKhG,EAAAA,CAAAA,GAC5CA,EAAAA,GAAIA,EAAAA,CAAEuG,WAAAA,EAAAA,GACI4L,EAAAA,IAAoBxB,uBAAAA,CAAY3K,IAAAA,CAAKhG,EAAAA,CAAAA,GAC/CA,EAAAA,GAAIA,EAAAA,CAAEsG,OAAAA,CAAAA,YAAoB,KAAA,CAAA,CAAOC,WAAAA,EAAAA,GACb,IAAA,KAAVR,EAAAA,IACVA,CAAAA,EAAAA,GAAAA,KAAQ5F,CAAAA,CAAAA,EAGTF,GAAAA,CAAgBD,EAAAA,CAAAA,GAAK+F,EAAAA,CAAAA;SAKb;QAAA,QAAA,IAARrG,GAAAA,IACAO,GAAAA,CAAgBmS,QAAAA,IAChB5N,KAAAA,CAAMC,OAAAA,CAAQxE,GAAAA,CAAgB8F,KAAAA,CAAAA,IAG9B9F,CAAAA,GAAAA,CAAgB8F,KAAAA,GAAQhB,CAAAA,GAAAA,yCAAAA,CAAAA,CAAapF,GAAAA,CAAM2B,QAAAA,CAAAA,CAAU8G,OAAAA,CAAQ,SAAA/F,GAAAA,EAAAA;YAC5DA,GAAAA,CAAM1C,KAAAA,CAAM0S,QAAAA,GAAAA,CAAAA,CAC0C,IAArDpS,GAAAA,CAAgB8F,KAAAA,CAAM7D,OAAAA,CAAQG,GAAAA,CAAM1C,KAAAA,CAAMoG,KAAAA,CAAAA;SAAAA,CAAAA,CAAAA,EAKjC,QAAA,IAARrG,GAAAA,IAAoD,IAAA,IAAhCO,GAAAA,CAAgBmK,YAAAA,IACvCnK,CAAAA,GAAAA,CAAgB8F,KAAAA,GAAQhB,CAAAA,GAAAA,yCAAAA,CAAAA,CAAapF,GAAAA,CAAM2B,QAAAA,CAAAA,CAAU8G,OAAAA,CAAQ,SAAA/F,GAAAA,EAAAA;YAE3DA,GAAAA,CAAM1C,KAAAA,CAAM0S,QAAAA,GADTpS,GAAAA,CAAgBmS,QAAAA,GAAAA,CAAAA,CAE0C,IAA5DnS,GAAAA,CAAgBmK,YAAAA,CAAalI,OAAAA,CAAQG,GAAAA,CAAM1C,KAAAA,CAAMoG,KAAAA,CAAAA,GAGjD9F,GAAAA,CAAgBmK,YAAAA,IAAgB/H,GAAAA,CAAM1C,KAAAA,CAAMoG,KAAAA;SAAAA,CAAAA,CAAAA,EAKhD7F,GAAAA,CAAMP,KAAAA,GAAQM,GAAAA,EAEVN,GAAAA,CAAMsS,KAAAA,IAAStS,GAAAA,CAAM2S,SAAAA,IACxBN,CAAAA,uBAAAA,CAAoBO,UAAAA,GAAa,WAAA,IAAe5S,GAAAA,EACzB,IAAA,IAAnBA,GAAAA,CAAM2S,SAAAA,IAAmBrS,CAAAA,GAAAA,CAAgBgS,KAAAA,GAAQtS,GAAAA,CAAM2S,SAAAA,CAAAA,EAC3DpB,MAAAA,CAAOC,cAAAA,CAAelR,GAAAA,EAAiB,WAAA,EAAa+R,uBAAAA,CAAAA,CAAAA;KAItD9R;IAAAA,GAAAA,CAAM+M,QAAAA,GAAWyD,uBAAAA,EAEbwB,uBAAAA,IAAcA,uBAAAA,CAAahS,GAAAA,CAAAA;CAAAA,CAKhC;AAAA,IAAMsS,uBAAAA,GAAkBlS,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GACxBA;AAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAAA,GAAAA,GAAkB,SAASJ,GAAAA,EAAAA;IACtBsS,uBAAAA,IACHA,uBAAAA,CAAgBtS,GAAAA,CAAAA,EAEjB6R,uBAAAA,GAAmB7R,GAAAA,CAAAA,GAAAA;CAAAA,CAOpB;AAAA,IAAauS,yCAAAA,GAAqD;IACjEC,sBAAAA,EAAwB;QACvB9Q,OAAAA,EAAS;YACR+Q,WAAAA,EAAAA,SAAY5Q,GAAAA,EAAAA;gBAAAA,OACJgQ,uBAAAA,CAAAA,GAAAA,CAAgChQ,GAAAA,CAAAA,GAAAA,CAAAA,CAAapC,KAAAA,CAAMoG,KAAAA,CAAAA;aAAAA;SAAAA;KAAAA;CAAAA,EC9LxD6M,yCAAAA,GAAU,QAAA,AAMhB;AAAA,SAASC,yCAAAA,CAAcnT,GAAAA,EAAAA;IAAAA,OACf2B,CAAAA,GAAAA,yCAAAA,CAAAA,CAAcqK,IAAAA,CAAK,IAAA,EAAMhM,GAAAA,CAAAA,CAAAA;CAQjC;AAAA,SAASc,yCAAAA,CAAesS,GAAAA,EAAAA;IAAAA,OAAAA,CAAAA,CACdA,GAAAA,IAAWA,GAAAA,CAAQ7F,QAAAA,KAAayD,uBAAAA,CAAAA;CAU1C;AAAA,SAASxG,yCAAAA,CAAa4I,GAAAA,EAAAA;IAAAA,OAChBtS,yCAAAA,CAAesS,GAAAA,CAAAA,GACbC,CAAAA,GAAAA,yCAAAA,CAAAA,CAAmBC,KAAAA,CAAM,IAAA,EAAMzR,SAAAA,CAAAA,GADDuR,GAAAA,CAAAA;CAStC;AAAA,SAASG,yCAAAA,CAAuBnD,GAAAA,EAAAA;IAAAA,OAAAA,CAAAA,CAC3BA,GAAAA,CAAAA,GAAAA,IACHkB,CAAAA,CAAAA,GAAAA,yCAAAA,CAAAA,CAAa,IAAA,EAAMlB,GAAAA,CAAAA,EAAAA,CACZ,CAAA,CAAA,CAAA;CAUT;AAAA,SAASoD,yCAAAA,CAAYjQ,GAAAA,EAAAA;IAAAA,OAElBA,GAAAA,IACCA,CAAAA,GAAAA,CAAUX,IAAAA,IAAgC,CAAA,KAAvBW,GAAAA,CAAU8F,QAAAA,IAAkB9F,GAAAA,CAAAA,IACjD,IAAA,CAAA;CAYF;AAAA,IAAMkQ,yCAAAA,GAA0B,SAAC9H,GAAAA,EAAU+H,GAAAA,EAAAA;IAAAA,OAAQ/H,GAAAA,CAAS+H,GAAAA,CAAAA,CAAAA;CAAAA,EAWtDC,yCAAAA,GAAY,SAAChI,GAAAA,EAAU+H,GAAAA,EAAAA;IAAAA,OAAQ/H,GAAAA,CAAS+H,GAAAA,CAAAA,CAAAA;CAAAA,EAMxCE,yCAAAA,GAAazR,CAAAA,GAAAA,yCAAAA,CAAAA;IAAAA,wCT9GZ,GS+IQ;IACd0R,QAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,UAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,SAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,eAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,MAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,mBAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,OAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,WAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,UAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAC,aAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACApB,OAAAA,EA9He,QAAA;IA+HfxF,QAAAA,EAAAA,yCAAAA;IACAzF,MAAAA,EAAAA,yCAAAA;IACAhC,OAAAA,EAAAA,yCAAAA;IACAsN,sBAAAA,EAAAA,yCAAAA;IACA/C,YAAAA,EAAAA,yCAAAA;IACA7O,aAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACA8I,aAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACA0I,aAAAA,EAAAA,yCAAAA;IACA3I,YAAAA,EAAAA,yCAAAA;IACAvI,SAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAE,QAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACArB,cAAAA,EAAAA,yCAAAA;IACA0S,WAAAA,EAAAA,yCAAAA;IACApR,SAAAA,EAAAA,CAAAA,GAAAA,yCAAAA,CAAAA;IACAgK,aAAAA,EAAAA,yCAAAA;IACAE,IAAAA,EAAAA,yCAAAA;IACAa,UAAAA,EAAAA,yCAAAA;IACAwG,SAAAA,EAAAA,yCAAAA;IACAF,uBAAAA,EAAAA,yCAAAA;IACAG,UAAAA,EAhEkBzR,CAAAA,GAAAA,yCAAAA,CAAAA;IAiElB8L,QAAAA,EAAAA,yCAAAA;IACAS,YAAAA,EAAAA,yCAAAA;IACAN,IAAAA,EAAAA,yCAAAA;IACA2E,kDAAAA,EAAAA,yCTlLM;CAAA;AFFP,MAAM,iCAAW,GAAG;IAClB,KAAK,EAAE,SAAS;IAChB,IAAI,EAAE,OAAO;CACd;AAEc,MAAM,wCAAU,SAAS,CAAA,GAAA,yCAAa,CAAA;IAanD,UAAU,CAAC,QAAQ,EAAE;QACnB,MAAM,EAAA,MAAE,IAAI,CAAA,CAAE,GAAG,QAAQ;QAEzB,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,GAAG,EACV,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;gBACH,KAAK,EAAC,MAAM;gBACZ,uBAAuB,EAAE;oBAAE,MAAM,EAAE,IAAI,CAAC,GAAG;iBAAE;cACvC,CACT;YAGH,IAAI,IAAI,CAAC,GAAG,EACV,OAAA,WAAA,GAAO,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;gBAAC,GAAG,EAAE,IAAI,CAAC,GAAG;cAAI,CAAA;SAEhC;QAED,MAAM,aAAa,GACjB,CAAA,GAAA,wCAAK,CAAA,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAA,GAAA,wCAAK,CAAA,CAAC,UAAU,CAAC,MAAM;QAE1D,MAAM,KAAK,GACT,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,GACtB,iCAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAC7B,IAAI,CAAC,KAAK,CAAC,KAAK;QAEtB,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,aAAa,CAAA;KAC7C;IAED,MAAM,GAAG;QACP,IAAI,qBAAqB,GAAG,IAAI;QAEhC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,EAAE,EAAC,KAAK;YACR,KAAK,EAAC,SAAS;YACf,eAAa,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAClC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;sBAEnB,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;gBAAC,KAAK,EAAC,eAAe;;oBACvB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAK;wBACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC3D,MAAM,QAAQ,GACZ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU;wBAE/D,IAAI,QAAQ,EACV,qBAAqB,GAAG,CAAC;wBAG3B,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,QAAM,EAAA;4BACL,YAAU,EAAE,KAAK;4BACjB,eAAa,EAAE,QAAQ,IAAI,SAAS;4BACpC,KAAK,EAAE,KAAK;4BACZ,IAAI,EAAC,QAAQ;4BACb,KAAK,EAAC,4BAA4B;4BAClC,WAAW,EAAE,CAAC,CAAC,GAAK,CAAC,CAAC,cAAc,EAAE;4BACtC,OAAO,EAAE,IAAM;gCACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;8CAAE,QAAQ;uCAAE,CAAC;iCAAE,CAAC;6BACpC;sCAEA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;0BACnB,CACV;qBACF,CAAC;kCAEF,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wBACF,KAAK,EAAC,KAAK;wBACX,KAAK,EAAE;4BACL,KAAK,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;4BACzC,OAAO,EAAE,qBAAqB,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC;4BAC9C,SAAS,EACP,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,GACpB,CAAC,sBAAsB,EAAE,qBAAqB,GAAG,GAAG,CAAC,EAAE,CAAC,GACxD,CAAC,WAAW,EAAE,qBAAqB,GAAG,GAAG,CAAC,EAAE,CAAC;yBACpD;sBACI;;cACH;UACF,CACP;KACF;IA5FD,aAAc;QACZ,KAAK,EAAE;QAEP,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,GAAK;YACrD,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAA;SACxB,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG;YACX,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;SAClC;KACF;CAmFF;AetGc,MAAM,wCAAmB,SAAS,CAAA,GAAA,yCAAa,CAAA;IAC5D,qBAAqB,CAAC,SAAS,EAAE;QAC/B,IAAK,IAAI,CAAC,IAAI,SAAS,CAAE;YACvB,IAAI,CAAC,IAAI,UAAU,EAAE,SAAQ;YAE7B,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAC/B,OAAO,IAAI,CAAA;SAEd;QAED,OAAO,KAAK,CAAA;KACb;IAED,MAAM,GAAG;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA;KAC3B;CACF;AlDND,MAAM,iCAAW,GAAG;IAClB,aAAa,EAAE,EAAE;CAClB;AAEc,MAAM,wCAAM,SAAS,CAAA,GAAA,yCAAS,CAAA;IAc3C,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;QAClC,OAAO;YACL,IAAI,EAAE,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI;YACrC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;SACnC,CAAA;KACF;IAED,kBAAkB,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK;QACnC,IAAI,CAAC,IAAI,GAAG;YACV,IAAI,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YACjB,UAAU,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YACvB,MAAM,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YACnB,MAAM,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YACnB,WAAW,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YACxB,cAAc,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;YAC3B,aAAa,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;SAC3B;QAED,IAAI,CAAC,QAAQ,EAAE;QAEf,IACE,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,IAChC,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,QAAQ,EACrC;YACA,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF;YAED,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ;SACrC;KACF;IAED,iBAAiB,GAAG;QAClB,IAAI,CAAC,QAAQ,EAAE;QAEf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU;QAEtC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACxB,MAAM,EAAA,aAAE,WAAW,CAAA,CAAE,GAAG,IAAI,CAAC,IAAI;YACjC,IAAI,WAAW,CAAC,OAAO,EACrB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE;SAE9B;KACF;IAED,yBAAyB,CAAC,SAAS,EAAE;QACnC,IAAI,CAAC,SAAS,IAAK,CAAA,IAAI,CAAC,SAAS,GAAG,CAAA,CAAE,CAAA,AAAC;QAEvC,IAAK,MAAM,EAAC,IAAI,SAAS,CACvB,IAAI,CAAC,SAAS,CAAC,EAAC,CAAC,GAAG,SAAS,CAAC,EAAC,CAAC;QAGlC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,IAAM;YACrC,IAAI,iBAAiB,GAAG,KAAK;YAE7B,IAAK,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,YAAY,EACtC,iBAAiB,GAAG,IAAI;aAE3B;YAED,OAAO,IAAI,CAAC,SAAS;YACrB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE;YAExC,IAAI,iBAAiB,EACnB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YAG9B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;SACzB,CAAC;KACH;IAED,oBAAoB,GAAG;QACrB,IAAI,CAAC,UAAU,EAAE;KAClB;IAED,MAAM,KAAK,CAAC,SAAS,GAAG,CAAA,CAAE,EAAE;QAC1B,MAAM,CAAA,GAAA,yCAAI,CAAA,CAAC,IAAI,CAAC,KAAK,CAAC;QAEtB,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,CAAC,SAAS,EAAE;QAEhB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAM;YAC7B,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,EAAE;SACnB,CAAC;KACH;IAED,QAAQ,GAAG;QACT,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;QAC3D,IAAI,CAAC,OAAO,EAAE;KACf;IAED,UAAU,GAAG;QACX,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;QAC9D,IAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC;QACrE,IAAI,CAAC,SAAS,EAAE;KACjB;IAED,OAAO,GAAG;QACR,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,WAAW,EAAE;KACnB;IAED,SAAS,CAAC,EAAA,QAAE,MAAM,GAAG,EAAE,EAAE,GAAG,CAAA,CAAE,EAAE;QAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACxB,MAAM,GAAG;YAAC,MAAM;SAAC;QAGnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAE;YACrC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAQ;YACvC,QAAQ,CAAC,UAAU,EAAE;SACtB;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;KACnC;IAED,QAAQ,GAAG;QACT,MAAM,EAAA,YAAE,UAAU,CAAA,CAAE,GAAG,CAAA,GAAA,yCAAI,CAAA;QAE3B,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE;QAEhC,MAAM,MAAM,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAK,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACvE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,EACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAK,CAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAA,AAAC;QAEtE,IAAI,CAAC,MAAM,GAAG,MAAM;QAEpB,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC;QAErB,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,GAAK;YACjC,MAAM,GAAG,GAAG,EAAE;YACd,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,EAAE;YAC9B,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAEnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;YACrC,MAAM,MAAM,GAAG,QAAQ,GAAG,iCAAW,CAAC,aAAa,GAAG,CAAA,CAAE,GAAG,CAAA,GAAA,yCAAS,CAAA,EAAE;YACtE,MAAM,CAAC,KAAK,GAAG,QAAQ;YACvB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAEjB,OAAO,GAAG,CAAA;SACX;QAED,KAAK,IAAI,SAAQ,IAAI,UAAU,CAAE;YAC/B,MAAM,IAAI,GAAG,EAAE;YACf,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,SAAQ,CAAC;YAEhC,KAAK,IAAI,KAAK,IAAI,SAAQ,CAAC,MAAM,CAAE;gBACjC,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,EACjC,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,SAAQ,CAAC;gBAG9B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC;gBACtB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAQ,CAAC,EAAE,EAAE;gBAAE,IAAI,EAAE,CAAA,GAAA,yCAAS,CAAA,EAAE;sBAAE,IAAI;aAAE,CAAC;SACnE;KACF;IAOD,SAAS,CAAC,KAAK,EAAE;QACf,IAAI,KAAK,IAAI,MAAM,EAAE,OAAO,KAAK,CAAA;QAEjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,8BAA8B,CAAC;YAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAA,OAAQ,EAAE,OAAO,OAAO,CAAA;YAEtD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC;SAClE;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAA;KACjD;IAoCD,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;QACrC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAM;QAC/B,MAAM,EAAA,SAAE,OAAO,CAAA,CAAA,iBAAE,eAAe,CAAA,CAAE,GAAG,KAAK;QAE1C,MAAM,gBAAgB,GAAG,IAAM;YAC7B,MAAM,EAAA,OAAE,KAAK,CAAA,CAAE,GAAG,OAAO,CAAC,qBAAqB,EAAE;YACjD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,CAAA;SAC3C;QAED,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,IAAM;YACxC,IAAI,CAAC,SAAS,CAAC;gBAAE,MAAM,EAAE,QAAQ;aAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC;gBAAE,OAAO,EAAE,gBAAgB,EAAE;aAAE,EAAE,IAAM;gBACnD,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,IAAM;oBACrB,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,WAAW,EAAE;iBACnB,CAAC;aACH,CAAC;SACH,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;QAE7B,OAAO,gBAAgB,EAAE,CAAA;KAC1B;IAED,UAAU,GAAG;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAA;KAChD;IAED,aAAa,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAEtC,IAAI,CAAC,KAAK,EAAE,OAAM;QAClB,OAAO,CAAA,GAAA,wCAAW,CAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;KAC9B;IAED,iBAAiB,GAAG;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;QAC/C,IAAI,CAAC,UAAU,EAAE,OAAM;QAEvB,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAE;QACnC,MAAM,kBAAkB,GAAG,CAAC,UAAU,GAAK;YACzC,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,EAC3C,UAAU,CAAC,QAAQ,CAAC;4BAAE,UAAU;aAAE,CAAC;SAEtC;QAED,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAC9B,SAAS,EAAE;gBAAC,GAAG;gBAAE,GAAG;aAAC;SACtB;QAED,MAAM,QAAQ,GAAG,IAAI,oBAAoB,CAAC,CAAC,OAAO,GAAK;YACrD,KAAK,MAAM,KAAK,IAAI,OAAO,CAAE;gBAC3B,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBAClC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,iBAAiB,CAAC;aACnD;YAED,MAAM,MAAM,GAAG;mBAAI,iBAAiB;aAAC;YACrC,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,MAAM,CAC9B,IAAI,KAAK,EAAE;gBACT,kBAAkB,CAAC,EAAE,CAAC;gBACtB,MAAK;aACN;SAEJ,EAAE,eAAe,CAAC;QAEnB,KAAK,MAAM,EAAA,MAAE,IAAI,CAAA,CAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAClD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAGhC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC9B;IAED,WAAW,GAAG;QACZ,MAAM,WAAW,GAAG;YAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;SAAE;QAEjD,MAAM,QAAQ,GAAG,IAAI,oBAAoB,CACvC,CAAC,OAAO,GAAK;YACX,KAAK,MAAM,KAAK,IAAI,OAAO,CAAE;gBAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAElD,IAAI,KAAK,CAAC,cAAc,EACtB,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI;qBAEzB,OAAO,WAAW,CAAC,KAAK,CAAC;aAE5B;YAED,IAAI,CAAC,QAAQ,CAAC;6BAAE,WAAW;aAAE,CAAC;SAC/B,EACD;YACE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAC9B,UAAU,EAAE,CAAC,EACX,IAAI,CAAC,KAAK,CAAC,eAAe,GAAI,CAAA,iCAAW,CAAC,aAAa,GAAG,CAAC,CAAA,AAAC,CAC7D,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,iCAAW,CAAC,aAAa,CAAC,EAAE,CAAC;SACrE,CACF;QAED,KAAK,MAAM,EAAA,MAAE,IAAI,CAAA,CAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAE;YACpD,KAAK,MAAM,GAAG,IAAI,IAAI,CACpB,IAAI,GAAG,CAAC,OAAO,EACb,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;SAGlC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC9B;IAED,cAAc,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,cAAc,EAAE;KACnB;IAuGD,aAAa,GAAG;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;QAC3C,IAAI,CAAC,KAAK,EAAE,OAAM;QAElB,KAAK,CAAC,IAAI,EAAE;KACb;IAED,QAAQ,CAAC,EAAA,GAAE,CAAC,CAAA,CAAA,OAAE,KAAK,CAAA,CAAA,MAAE,IAAI,CAAA,CAAA,OAAE,KAAK,CAAA,CAAA,IAAE,EAAE,CAAA,CAAA,MAAE,IAAI,CAAA,CAAE,EAAE;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAM;QAExB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG;QAE7B,MAAM,GAAG,GAAI,CAAA,IAAM;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAK,CAAA,IAAI,IAAI,EAAE,CAAA,AAAC,EACtC,OAAO,IAAI,CAAA;aAEd;YAED,IAAI,EAAE,IAAI,CAAA,CAAE,EAAE;gBACZ,IACE,CAAC,CAAC,CAAC,MAAM,IACR,CAAA,KAAK,IAAI,IAAI,CAAA,IACd,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAE1C,OAAO;oBAAC,CAAC;oBAAE,CAAC;iBAAC,CAAA;gBAGf,OAAO,IAAI,CAAA;aACZ;YAED,IAAI,IAAI,IAAI,KAAK,EAAE;gBACjB,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;gBAClB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAA,CAAE,GAAG,CAAC;gBAE/B,EAAE,IAAI,SAAS;gBACf,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;oBACZ,EAAE,IAAI,SAAS;oBACf,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;oBAEd,IAAI,CAAC,GAAG,EAAE;wBACR,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;wBAC/B,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;wBAEnC,OAAO;4BAAC,EAAE;4BAAE,EAAE;yBAAC,CAAA;qBAChB;oBAED,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC;iBAC/B;gBAED,OAAO;oBAAC,EAAE;oBAAE,EAAE;iBAAC,CAAA;aAChB;YAED,IAAI,EAAE,IAAI,IAAI,EAAE;gBACd,EAAE,IAAI,EAAE,GAAG,CAAA,CAAE,GAAG,CAAC;gBACjB,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;gBAEpB,IAAI,CAAC,GAAG,EAAE;oBACR,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;oBAC7B,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;oBAEjC,OAAO;wBAAC,EAAE;wBAAE,EAAE;qBAAC,CAAA;iBAChB;gBAED,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EACV,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC;gBAGrB,OAAO;oBAAC,EAAE;oBAAE,EAAE;iBAAC,CAAA;aAChB;SACF,CAAA,EAAG;QAEJ,IAAI,GAAG,EACL,CAAC,CAAC,cAAc,EAAE;aACb;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA,CAAE,EACxB,IAAI,CAAC,QAAQ,CAAC;gBAAE,GAAG,EAAE;oBAAC,CAAA,CAAE;oBAAE,CAAA,CAAE;iBAAC;aAAE,CAAC;YAGlC,OAAM;SACP;QAED,IAAI,CAAC,QAAQ,CAAC;iBAAE,GAAG;YAAE,QAAQ,EAAE,IAAI;SAAE,EAAE,IAAM;YAC3C,IAAI,CAAC,QAAQ,CAAC;gBAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;aAAE,CAAC;SAC/B,CAAC;KACH;IAED,QAAQ,CAAC,EAAA,YAAE,UAAU,CAAA,CAAA,KAAE,GAAG,CAAA,CAAE,EAAE;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAM;QAExB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,EAAE;QAEjD,IAAI,SAAS,GAAG,CAAC;QAEjB,IAAI,GAAG,IAAI,CAAC,EACV,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY;QAGrC,IAAI,UAAU,EAAE;YACd,MAAM,GAAG,GACP,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI;YACpE,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAExD,SAAS,GAAG,YAAY,CAAC,GAAG,GAAI,CAAA,UAAU,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,CAAA,GAAI,CAAC;SACvE;QAED,IAAI,GAAG,IAAI,CAAC,EAAA;YACV,IAAI,CAAC,GAAG,EACN,SAAS,GAAG,CAAC;iBACR;gBACL,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO;gBAClC,MAAM,MAAM,GAAG,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;gBAChE,MAAM,MAAM,GACV,MAAM,GACN,IAAI,CAAC,KAAK,CAAC,eAAe,GAC1B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI;gBAEnC,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAC3B,SAAS,GAAG,MAAM;qBACb,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,EACtD,SAAS,GAAG,MAAM,GAAG,UAAU,CAAC,MAAM;qBAEtC,OAAM;aAET;SACF;QAED,IAAI,CAAC,WAAW,EAAE;QAClB,MAAM,CAAC,SAAS,GAAG,SAAS;KAC7B;IAED,WAAW,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,IAAM;YACvC,OAAO,IAAI,CAAC,cAAc;SAC3B,EAAE,GAAG,CAAC;KACR;IAMD,eAAe,CAAC,GAAG,EAAE;QACnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAM;QACvD,IAAI,CAAC,QAAQ,CAAC;YAAE,GAAG,EAAE,GAAG,IAAI;gBAAC,CAAA,CAAE;gBAAE,CAAA,CAAE;aAAC;YAAE,QAAQ,EAAE,KAAK;SAAE,CAAC;KACzD;IAED,gBAAgB,CAAC,EAAA,GAAE,CAAC,CAAA,CAAA,OAAE,KAAK,CAAA,CAAA,KAAE,GAAG,CAAA,CAAE,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,OAAM;QAErC,IAAI,CAAC,KAAK,IAAI,GAAG,EACf,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAGjC,IAAI,KAAK,EAAE;YACT,MAAM,SAAS,GAAG,CAAA,GAAA,yCAAY,CAAA,CAAC,KAAK,EAAE;gBAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;aAAE,CAAC;YAEzE,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAC5B,CAAA,GAAA,wCAAc,CAAA,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC;YAG3C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;SACvC;KACF;IAqBD,UAAU,GAAG;QACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAM;QACjC,IAAI,CAAC,QAAQ,CAAC;YAAE,SAAS,EAAE,IAAI;YAAE,QAAQ,EAAE,IAAI;SAAE,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;KACjE;IAED,mBAAmB,CAAC,QAAQ,EAAE;QAC5B,IAAI,CAAC,QAAQ,CAAC;sBAAE,QAAQ;SAAE,CAAC;KAC5B;IAED,eAAe,CAAC,IAAI,EAAE;QACpB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,UAAU,EAAE;QAEjB,IAAI,CAAC,QAAQ,CAAC;kBAAE,IAAI;YAAE,QAAQ,EAAE,IAAI;SAAE,CAAC;QACvC,CAAA,GAAA,wCAAK,CAAA,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;KACxB;IAED,SAAS,GAAG;QACV,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAU,CAAA,EAAA;YAET,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;YACrC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YAChC,OAAO,EAAE,IAAI,CAAC,mBAAmB;WAP5B,IAAI,CAAC,MAAM,CAQhB,CACH;KACF;IAED,aAAa,GAAG;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAChD,MAAM,eAAe,GACnB,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM;QAE9D,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,EAAE,EAAC,SAAS;YACZ,KAAK,EAAC,kBAAkB;YACxB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,eAAa,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;;8BAEzC,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,KAAK,EAAC,4BAA4B;;sCACrC,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BACF,KAAK,EAAC,wCAAwC;4BAC9C,KAAK,EAAE;gCACL,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;gCAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;6BACrC;sCAED,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAK,CAAA,EAAA;gCACJ,KAAK,EAAE,KAAK;gCACZ,EAAE,EACA,eAAe,GACX,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,KAAK,GAClC,IAAI,CAAC,KAAK,CAAC,YAAY,IACtB,CAAA,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,KAAK,GAChC,YAAY,GACZ,UAAU,CAAA,AAAC;gCAErB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;gCACnB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;gCAChC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;gCAC5C,WAAW,EAAE,IAAI;gCACjB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;8BAC/C;0BACE;sCAEN,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BAAC,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;sCAChC,KAAK,IAAI,eAAe,GAAA,WAAA,GACvB,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;gCAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;kDACvD,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wCAAC,KAAK,EAAC,wBAAwB;kDAChC,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,mBAAmB;sCAC1C;kDACN,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wCAAC,KAAK,EAAC,mCAAmC;kDAC3C,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,mBAAmB;sCACzD;;8BACF,GAAA,WAAA,GAEN,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;gCAAC,KAAK,EAAC,6BAA6B;0CAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,IAAI;8BAAO,AAC3D;0BACG;;kBACF;gBAEL,CAAC,KAAK,IACL,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,SAAS,IACxC,IAAI,CAAC,oBAAoB,EAAE;;UACzB,CACP;KACF;IAED,iBAAiB,CAAC,KAAK,EAAE,EAAA,KAAE,GAAG,CAAA,CAAA,UAAE,QAAQ,CAAA,CAAA,MAAE,IAAI,CAAA,CAAE,EAAE;QAChD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;QACnD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM;QAC/B,MAAM,QAAQ,GAAG,CAAA,GAAA,yCAAS,CAAA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC;QAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QAEzC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAmB,CAAA,EAAA;sBAAiB,QAAQ;kBAAE,IAAI;kBAAE,IAAI;sBACvD,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,QAAM,EAAA;gBACL,YAAU,EAAE,MAAM;gBAClB,eAAa,EAAE,QAAQ,IAAI,SAAS;gBACpC,eAAa,EAAE,QAAQ;gBACvB,cAAY,EAAE,IAAI,CAAC,OAAO;gBAC1B,eAAa,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS;gBACpE,IAAI,EAAC,QAAQ;gBACb,KAAK,EAAC,8BAA8B;gBACpC,QAAQ,EAAC,IAAI;gBACb,OAAO,EAAE,CAAC,CAAC,GAAK,IAAI,CAAC,gBAAgB,CAAC;2BAAE,CAAC;+BAAE,KAAK;qBAAE,CAAC;gBACnD,YAAY,EAAE,IAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC7C,YAAY,EAAE,IAAM,IAAI,CAAC,eAAe,EAAE;gBAC1C,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;oBACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;oBAClC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;oBAC9B,UAAU,EAAE,CAAC;iBACd;;kCAED,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wBACF,aAAW,EAAC,MAAM;wBAClB,KAAK,EAAC,YAAY;wBAClB,KAAK,EAAE;4BACL,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;4BAC1C,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GACzC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CACzB,CAAA,QAAQ,GAAG,CAAC,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CACrD,GACD,SAAS;yBACd;sBACI;kCACP,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAK,CAAA,EAAA;wBACJ,KAAK,EAAE,KAAK;wBACZ,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;wBACnB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;wBAC1B,IAAI,EAAE,IAAI;wBACV,WAAW,EAAE,IAAI;wBACjB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;sBAC/C;;cACK;WAzCe,GAAG,CA0CP,CACvB;KACF;IAED,YAAY,GAAG;QACb,MAAM,cAAc,GAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,MAAM,IACpC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,QAAQ;QAEzC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;;8BACF,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,KAAK,EAAC,QAAQ;kBAAO;8BAC1B,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,KAAK,EAAC,kBAAkB;;sCAC3B,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BAAC,KAAK,EAAC,2BAA2B;;8CACpC,CAAA,GAAA,yCAAA,EAAC,OAAK,EAAA;oCACJ,IAAI,EAAC,QAAQ;oCACb,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;oCAC1B,WAAW,EAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,MAAM;oCACxB,OAAO,EAAE,IAAI,CAAC,iBAAiB;oCAC/B,OAAO,EAAE,IAAI,CAAC,iBAAiB;oCAC/B,SAAS,EAAE,IAAI,CAAC,mBAAmB;oCACnC,YAAY,EAAC,KAAK;kCACX;8CACT,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oCAAC,KAAK,EAAC,iBAAiB;8CAAE,CAAA,GAAA,wCAAK,CAAA,CAAC,MAAM,CAAC,KAAK;kCAAQ;gCACxD,IAAI,CAAC,KAAK,CAAC,aAAa,IAAA,WAAA,GACvB,CAAA,GAAA,yCAAA,EAAC,QAAM,EAAA;oCACL,KAAK,EAAC,OAAO;oCACb,YAAU,EAAC,OAAO;oCAClB,IAAI,EAAC,QAAQ;oCACb,KAAK,EAAC,kBAAkB;oCACxB,OAAO,EAAE,IAAI,CAAC,WAAW;oCACzB,WAAW,EAAE,IAAI,CAAC,cAAc;8CAE/B,CAAA,GAAA,wCAAK,CAAA,CAAC,MAAM,CAAC,MAAM;kCACb,AACV;;0BACG;wBAEL,cAAc,IAAI,IAAI,CAAC,oBAAoB,EAAE;;kBAC1C;;UACF,CACP;KACF;IAED,mBAAmB,GAAG;QACpB,MAAM,EAAA,eAAE,aAAa,CAAA,CAAE,GAAG,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,CAAA;QAE/B,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,KAAK,EAAC,UAAU;YAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;;8BACzC,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,KAAK,EAAE,CAAC,2BAA2B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;8BACpD,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU,CAAC,MAAM;kBACnB;8BACN,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;8BACD,CAAC,aAAa,CAAC,MAAM,GAAA,WAAA,GACpB,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wBAAC,KAAK,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;kCAC7C,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAA,WAAA,GAC1B,CAAA,GAAA,yCAAA,EAAC,GAAC,EAAA;4BAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;sCAAG,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU;0BAAK,AAC/D;sBACG,GAEN,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAK;wBAC5B,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BAAC,KAAK,EAAC,MAAM;sCACd,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,GAAK;gCACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;oCACnC,GAAG,EAAE;wCAAC,CAAC;wCAAE,EAAE;qCAAC;oCACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC;oCACzC,IAAI,EAAE,aAAa;iCACpB,CAAC,CAAA;6BACH,CAAC;0BACE,CACP;qBACF,CAAC,AACH;kBACG;;UACF,CACP;KACF;IAED,gBAAgB,GAAG;QACjB,MAAM,EAAA,YAAE,UAAU,CAAA,CAAE,GAAG,CAAA,GAAA,yCAAI,CAAA;QAC3B,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;QAEjC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS;gBACzC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;gBACpC,MAAM,EAAE,MAAM;aACf;sBAEA,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAK;gBAC5B,MAAM,EAAA,MAAE,IAAI,CAAA,CAAA,MAAE,IAAI,CAAA,CAAE,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAE5D,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBACF,SAAO,EAAE,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;oBAC3D,KAAK,EAAC,UAAU;oBAChB,GAAG,EAAE,IAAI;;sCAET,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BAAC,KAAK,EAAE,CAAC,2BAA2B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;sCACpD,QAAQ,CAAC,IAAI,IAAI,CAAA,GAAA,yCAAI,CAAA,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;0BAC1C;sCACN,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4BACF,KAAK,EAAC,UAAU;4BAChB,KAAK,EAAE;gCACL,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;6BACjD;sCAEA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAK;gCACpB,MAAM,SAAS,GACb,GAAG,CAAC,KAAK,GAAI,GAAG,CAAC,KAAK,GAAG,iCAAW,CAAC,aAAc;gCACrD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC;gCACjD,MAAM,GAAG,GAAG,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG,SAAS;gCAE9C,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAClB,OAAO,IAAI,CAAA;gCAGb,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO;gCACzB,MAAM,GAAG,GAAG,KAAK,GAAG,OAAO;gCAC3B,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;gCAElD,IAAI,QAAQ,CAAC,MAAM,GAAG,OAAO,EAC3B,QAAQ,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gCAGxD,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oCAEF,YAAU,EAAE,GAAG,CAAC,KAAK;oCACrB,GAAG,EAAE,GAAG;oCACR,KAAK,EAAC,UAAU;oCAChB,KAAK,EAAE;wCAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;qCAAE;8CAE7C,OAAO,IACN,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,GAAK;wCAC5B,IAAI,CAAC,OAAO,EACV,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;4CACF,KAAK,EAAE;gDACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;gDACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;6CACnC;0CACI,CACR;wCAGH,MAAM,KAAK,GAAG,CAAA,GAAA,wCAAW,CAAA,CAAC,GAAG,CAAC,OAAO,CAAC;wCAEtC,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;4CACnC,GAAG,EAAE;gDAAC,GAAG,CAAC,KAAK;gDAAE,EAAE;6CAAC;4CACpB,QAAQ,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE;4CAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;yCAChB,CAAC,CAAA;qCACH,CAAC;mCA1BC,GAAG,CAAC,KAAK,CA2BV,CACP;6BACF,CAAC;0BACE;;kBACF,CACP;aACF,CAAC;UACE,CACP;KACF;IAED,oBAAoB,GAAG;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,MAAM,EACvC,OAAO,IAAI,CAAA;QAGb,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,KAAK,EAAC,wCAAwC;YAC9C,KAAK,EAAE;gBACL,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;gBACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;aACnC;sBAED,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,QAAM,EAAA;gBACL,IAAI,EAAC,QAAQ;gBACb,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc;gBAC7B,KAAK,EAAC,yDAAyD;gBAC/D,eAAa,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG,SAAS;gBACpD,YAAU,EAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,MAAM;gBAC7B,KAAK,EAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,MAAM;gBACxB,OAAO,EAAE,IAAI,CAAC,SAAS;gBACvB,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;oBAC3B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;iBAC7B;0BAED,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oBAAC,KAAK,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;kBAAS;cACvD;UACL,CACP;KACF;IAED,gBAAgB,GAAG;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAChD,MAAM,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE;QAExC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YAAC,WAAS,EAAC,QAAQ;YAAC,KAAK,EAAC,SAAS;sBACpC,QAAQ;UACL,CACP;KACF;IAED,WAAW,GAAG;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;QACvD,MAAM,kBAAkB,GAAG,cAAc,CAAC,qBAAqB,EAAE;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;QAElD,MAAM,QAAQ,GAAG,CAAA,CAAE;QAEnB,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,EACnB,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,CAAC;aAE9D,QAAQ,CAAC,IAAI,GAAG,kBAAkB,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC;QAG7D,IACE,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,QAAQ,IACtC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,SAAS,EAExC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,kBAAkB,CAAC,GAAG,GAAG,CAAC;aACzD;YACL,QAAQ,CAAC,GAAG,GAAG,kBAAkB,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC;YAC3D,QAAQ,CAAC,MAAM,GAAG,MAAM;SACzB;QAED,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;YACF,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YACnB,IAAI,EAAC,YAAY;YACjB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,YAAU,EAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,MAAM;YAC7B,KAAK,EAAC,aAAa;YACnB,eAAa,EAAE,QAAQ,CAAC,GAAG,GAAG,KAAK,GAAG,QAAQ;YAC9C,KAAK,EAAE,QAAQ;sBAEd;mBAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;aAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAK;gBAC/B,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC;gBAClB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI;gBAEvC,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;;sCACF,CAAA,GAAA,yCAAA,EAAC,OAAK,EAAA;4BACJ,IAAI,EAAC,OAAO;4BACZ,IAAI,EAAC,WAAW;4BAChB,KAAK,EAAE,IAAI;4BACX,YAAU,EAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,CAAC;4BAC5B,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI;4BAC7C,cAAc,EAAE,OAAO;4BACvB,QAAQ,EAAE,IAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;4BAC9C,SAAS,EAAE,CAAC,CAAC,GAAK;gCAChB,IACE,CAAC,CAAC,IAAI,IAAI,OAAO,IACjB,CAAC,CAAC,IAAI,IAAI,OAAO,IACjB,CAAC,CAAC,IAAI,IAAI,KAAK,EACf;oCACA,CAAC,CAAC,cAAc,EAAE;oCAClB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;iCAC3B;6BACF;0BACM;sCAET,CAAA,GAAA,yCAAA,EAAC,QAAM,EAAA;4BACL,aAAW,EAAC,MAAM;4BAClB,QAAQ,EAAC,IAAI;4BACb,OAAO,EAAE,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;4BACzC,YAAY,EAAE,IAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;4BAClD,YAAY,EAAE,IAAM,IAAI,CAAC,mBAAmB,EAAE;4BAC9C,KAAK,EAAC,mCAAmC;;8CAEzC,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oCAAC,KAAK,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;kCAAS;8CACnD,CAAA,GAAA,yCAAA,EAAC,MAAI,EAAA;oCAAC,KAAK,EAAC,iBAAiB;8CAAE,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC,IAAI,CAAC;kCAAQ;;0BAChD;;kBACL,CACP;aACF,CAAC;UACE,CACP;KACF;IAED,MAAM,GAAG;QACP,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;QAEjE,OAAA,WAAA,GACE,CAAA,GAAA,yCAAA,EAAC,SAAO,EAAA;YACN,EAAE,EAAC,MAAM;YACT,KAAK,EAAC,kBAAkB;YACxB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE;gBACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAC1B,MAAM,GACN,CAAC,KAAK,EAAE,SAAS,CAAC,6CAA6C,CAAC;aACrE;YACD,gBAAc,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;YAC9B,YAAU,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YAC5B,WAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG,SAAS;;gBAE/C,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC3D,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;gBACnD,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,QAAQ,IAAA,WAAA,GACpC,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,KAAK,EAAC,YAAY;8BAAE,IAAI,CAAC,YAAY,EAAE;kBAAO,AACpD;8BAED,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;oBAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;oBAAE,KAAK,EAAC,6BAA6B;8BAC7D,WAAA,GAAA,CAAA,GAAA,yCAAA,EAAC,KAAG,EAAA;wBACF,KAAK,EAAE;4BACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,GAAG,SAAS;4BACnD,MAAM,EAAE,MAAM;yBACf;;4BAEA,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;4BAC5D,IAAI,CAAC,mBAAmB,EAAE;4BAC1B,IAAI,CAAC,gBAAgB,EAAE;;sBACpB;kBACF;gBAEL,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;gBACtD,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC9D,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC1C,IAAI,CAAC,gBAAgB,EAAE;;UAChB,CACX;KACF;IA5lCD,YAAY,KAAK,CAAE;QACjB,KAAK,EAAE;QAmLT,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,mBAAiB,EAAG,IAAM;YACxB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE,OAAM;YACtC,IAAI,CAAC,QAAQ,CAAC;gBAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO;aAAE,CAAC;SACpE,CAAA,CAAA;QAeD,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,oBAAkB,EAAG,CAAC,CAAC,GAAK;YAC1B,MAAM,EAAA,SAAE,OAAO,CAAA,CAAE,GAAG,IAAI,CAAC,KAAK;YAE9B,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,EAAE;gBACvB,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EACtB,IAAI,CAAC,UAAU,EAAE;gBAGnB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAC3B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;aAE/B;SACF,CAAA,CAAA;QAED,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,iBAAe,EAAG,CAAC,CAAC,GAAK;YACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAM;YACjC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC9B,CAAC,CAAC,cAAc,EAAE;gBAClB,CAAC,CAAC,wBAAwB,EAAE;gBAE5B,IAAI,CAAC,UAAU,EAAE;aAClB;SACF,CAAA,CAAA;QAED,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,mBAAiB,EAAG,CAAC,CAAC,GAAK;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAM;YACjC,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,EAAE;gBACrB,CAAC,CAAC,cAAc,EAAE;gBAClB,CAAC,CAAC,wBAAwB,EAAE;gBAE5B,IAAI,CAAC,UAAU,EAAE;aAClB;SACF,CAAA,CAAA;QAsHD,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,mBAAiB,EAAG,IAAM;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAChD,IAAI,CAAC,KAAK,EAAE,OAAM;YAElB,IAAI,CAAC,QAAQ,CAAC;gBAAE,GAAG,EAAE;oBAAC,CAAA,CAAE;oBAAE,CAAA,CAAE;iBAAC;aAAE,CAAC;SACjC,CAAA,CAAA;QAED,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,mBAAiB,EAAG,UAAY;YAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;YAC3C,IAAI,CAAC,KAAK,EAAE,OAAM;YAElB,MAAM,EAAA,OAAE,KAAK,CAAA,CAAE,GAAG,KAAK;YACvB,MAAM,aAAa,GAAG,MAAM,CAAA,GAAA,wCAAW,CAAA,CAAC,MAAM,CAAC,KAAK,CAAC;YACrD,MAAM,WAAW,GAAG,IAAM;gBACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAM;gBACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC;aACvC;YAED,IAAI,CAAC,aAAa,EAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;+BAAE,aAAa;gBAAE,GAAG,EAAE;oBAAC,CAAA,CAAE;oBAAE,CAAA,CAAE;iBAAC;aAAE,EAAE,WAAW,CAAC,CAAA;YAGrE,MAAM,GAAG,GAAG,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG;gBAAC,CAAC;gBAAE,CAAC;aAAC,GAAG;gBAAC,CAAA,CAAE;gBAAE,CAAA,CAAE;aAAC;YAC1E,MAAM,IAAI,GAAG,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,MAAM;YACnC,IAAI,GAAG,GAAG,IAAI;YAEd,KAAK,IAAI,KAAK,IAAI,aAAa,CAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;oBACnD,GAAG,GAAG,EAAE;oBACR,GAAG,CAAC,YAAY,GAAG,QAAQ;oBAC3B,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;oBACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;iBACf;gBAED,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC;gBAAE,aAAa,EAAE,IAAI;qBAAE,GAAG;aAAE,EAAE,WAAW,CAAC;SACzD,CAAA,CAAA;QAED,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,qBAAmB,EAAG,CAAC,CAAC,GAAK;YAC3B,wDAAwD;YACxD,MAAM,KAAK,GAAG,CAAC,CAAC,aAAa;YAC7B,CAAC,CAAC,wBAAwB,EAAE;YAE5B,OAAQ,CAAC,CAAC,GAAG;gBACX,KAAK,WAAW;oBACd,yBAAyB;oBACzB,qBAAqB;oBACrB,IAAI,CAAC,QAAQ,CAAC;2BAAE,CAAC;+BAAE,KAAK;wBAAE,IAAI,EAAE,IAAI;qBAAE,CAAC;oBACvC,MAAK;gBAEP,KAAK,YAAY;oBACf,yBAAyB;oBACzB,qBAAqB;oBACrB,IAAI,CAAC,QAAQ,CAAC;2BAAE,CAAC;+BAAE,KAAK;wBAAE,KAAK,EAAE,IAAI;qBAAE,CAAC;oBACxC,MAAK;gBAEP,KAAK,SAAS;oBACZ,yBAAyB;oBACzB,qBAAqB;oBACrB,IAAI,CAAC,QAAQ,CAAC;2BAAE,CAAC;+BAAE,KAAK;wBAAE,EAAE,EAAE,IAAI;qBAAE,CAAC;oBACrC,MAAK;gBAEP,KAAK,WAAW;oBACd,yBAAyB;oBACzB,qBAAqB;oBACrB,IAAI,CAAC,QAAQ,CAAC;2BAAE,CAAC;+BAAE,KAAK;wBAAE,IAAI,EAAE,IAAI;qBAAE,CAAC;oBACvC,MAAK;gBAEP,KAAK,OAAO;oBACV,CAAC,CAAC,cAAc,EAAE;oBAClB,IAAI,CAAC,gBAAgB,CAAC;2BAAE,CAAC;wBAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;qBAAE,CAAC;oBACjD,MAAK;gBAEP,KAAK,QAAQ;oBACX,CAAC,CAAC,cAAc,EAAE;oBAClB,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAC1B,IAAI,CAAC,WAAW,EAAE;yBAElB,IAAI,CAAC,aAAa,EAAE;oBAEtB,MAAK;gBAEP;oBACE,MAAK;aACR;SACF,CAAA,CAAA;QAED,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,aAAW,EAAG,IAAM;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;YAC3C,IAAI,CAAC,KAAK,EAAE,OAAM;YAElB,KAAK,CAAC,KAAK,GAAG,EAAE;YAChB,KAAK,CAAC,KAAK,EAAE;YAEb,IAAI,CAAC,iBAAiB,EAAE;SACzB,CAAA,CAAA;QAgJD,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,qBAAmB,EAAG,CAAC,EAAA,UAAE,QAAQ,CAAA,CAAA,GAAE,CAAC,CAAA,CAAE,GAAK;YACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG;gBAAE,GAAG,EAAE,CAAA,CAAE;aAAE,GAAG;gBAAE,UAAU,EAAE,QAAQ,CAAC,EAAE;aAAE,CAAC;SAClE,CAAA,CAAA;QAyBD,CAAA,GAAA,wCAAA,EAAA,IAAA,EAAA,WAAS,EAAG,CAAC,CAAC,GAAK;YACjB,MAAM,EAAA,eAAE,aAAa,CAAA,CAAE,GAAG,CAAC;YAC3B,MAAM,IAAI,GAAG,aAAa,CAAC,qBAAqB,EAAE;YAElD,IAAI,CAAC,QAAQ,CAAC;gBAAE,SAAS,EAAE,IAAI;aAAE,EAAE,UAAY;gBAC7C,mEAAmE;gBACnE,MAAM,CAAA,GAAA,yCAAK,CAAA,CAAC,CAAC,CAAC;gBAEd,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;gBACnC,IAAI,CAAC,IAAI,EAAE,OAAM;gBAEjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE;gBAEvC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;gBAC/D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;aACpE,CAAC;SACH,CAAA,CAAA;QAxnBC,IAAI,CAAC,SAAS,GAAG,EAAE;QAEnB,IAAI,CAAC,KAAK,GAAG;YACX,GAAG,EAAE;gBAAC,CAAA,CAAE;gBAAE,CAAA,CAAE;aAAC;YACb,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACvC,WAAW,EAAE;gBAAE,CAAC,EAAE,IAAI;aAAE;YACxB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;SAC/B;KACF;CAklCF;AmDtmCc,MAAM,wCAAa,SAAS,CAAA,GAAA,wCAAa,CAAA;IAOtD,MAAM,iBAAiB,GAAG;QACxB,MAAM,KAAK,GAAG,CAAA,GAAA,yCAAQ,CAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA,GAAA,wCAAW,CAAA,EAAE,IAAI,CAAC;QACrD,KAAK,CAAC,OAAO,GAAG,IAAI;QACpB,KAAK,CAAC,GAAG,GAAG,CAAC,SAAS,GAAK;YACzB,IAAI,CAAC,SAAS,GAAG,SAAS;SAC3B;QAED,MAAM,CAAA,GAAA,yCAAI,CAAA,CAAC,KAAK,CAAC;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,OAAM;QAE7B,CAAA,GAAA,yCAAM,CAAA,CAAA,WAAA,GAAC,CAAA,GAAA,yCAAA,EAAC,CAAA,GAAA,wCAAM,CAAA,EAAA;YAAE,GAAG,KAAK;UAAI,EAAE,IAAI,CAAC,UAAU,CAAC;KAC/C;IAfD,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC,KAAK,EAAE;YAAE,MAAM,EAAE,CAAA,GAAA,WAAA,GAAA,uBAAA,0BAAY,CAAA;SAAE,CAAC;KACvC;CAcF;AAlBC,CAAA,GAAA,wCAAA,EADmB,wCAAa,EACzB,OAAK,EAAG,CAAA,GAAA,wCAAW,CAAA,CAAA;AAoB5B,IACE,OAAO,cAAc,KAAK,WAAW,IACrC,CAAC,cAAc,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAEtC,cAAc,CAAC,MAAM,CAAC,iBAAiB,EAAE,wCAAa,CAAC;;ACjCzD,yBAAc,GAAG,kBAAkB,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "debugId": null}}]}