{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/map.js"], "sourcesContent": ["/**\n * Utility module to work with key-value stores.\n *\n * @module map\n */\n\n/**\n * Creates a new Map instance.\n *\n * @function\n * @return {Map<any, any>}\n *\n * @function\n */\nexport const create = () => new Map()\n\n/**\n * Copy a Map object into a fresh Map object.\n *\n * @function\n * @template K,V\n * @param {Map<K,V>} m\n * @return {Map<K,V>}\n */\nexport const copy = m => {\n  const r = create()\n  m.forEach((v, k) => { r.set(k, v) })\n  return r\n}\n\n/**\n * Get map property. Create T if property is undefined and set T on map.\n *\n * ```js\n * const listeners = map.setIfUndefined(events, 'eventName', set.create)\n * listeners.add(listener)\n * ```\n *\n * @function\n * @template {Map<any, any>} MAP\n * @template {MAP extends Map<any,infer V> ? function():V : unknown} CF\n * @param {MAP} map\n * @param {MAP extends Map<infer K,any> ? K : unknown} key\n * @param {CF} createT\n * @return {ReturnType<CF>}\n */\nexport const setIfUndefined = (map, key, createT) => {\n  let set = map.get(key)\n  if (set === undefined) {\n    map.set(key, set = createT())\n  }\n  return set\n}\n\n/**\n * Creates an Array and populates it with the content of all key-value pairs using the `f(value, key)` function.\n *\n * @function\n * @template K\n * @template V\n * @template R\n * @param {Map<K,V>} m\n * @param {function(V,K):R} f\n * @return {Array<R>}\n */\nexport const map = (m, f) => {\n  const res = []\n  for (const [key, value] of m) {\n    res.push(f(value, key))\n  }\n  return res\n}\n\n/**\n * Tests whether any key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @todo should rename to some - similarly to Array.some\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nexport const any = (m, f) => {\n  for (const [key, value] of m) {\n    if (f(value, key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * Tests whether all key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nexport const all = (m, f) => {\n  for (const [key, value] of m) {\n    if (!f(value, key)) {\n      return false\n    }\n  }\n  return true\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;;;;;;AACM,MAAM,SAAS,IAAM,IAAI;AAUzB,MAAM,OAAO,CAAA;IAClB,MAAM,IAAI;IACV,EAAE,OAAO,CAAC,CAAC,GAAG;QAAQ,EAAE,GAAG,CAAC,GAAG;IAAG;IAClC,OAAO;AACT;AAkBO,MAAM,iBAAiB,CAAC,KAAK,KAAK;IACvC,IAAI,MAAM,IAAI,GAAG,CAAC;IAClB,IAAI,QAAQ,WAAW;QACrB,IAAI,GAAG,CAAC,KAAK,MAAM;IACrB;IACA,OAAO;AACT;AAaO,MAAM,MAAM,CAAC,GAAG;IACrB,MAAM,MAAM,EAAE;IACd,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,EAAG;QAC5B,IAAI,IAAI,CAAC,EAAE,OAAO;IACpB;IACA,OAAO;AACT;AAcO,MAAM,MAAM,CAAC,GAAG;IACrB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,EAAG;QAC5B,IAAI,EAAE,OAAO,MAAM;YACjB,OAAO;QACT;IACF;IACA,OAAO;AACT;AAYO,MAAM,MAAM,CAAC,GAAG;IACrB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,EAAG;QAC5B,IAAI,CAAC,EAAE,OAAO,MAAM;YAClB,OAAO;QACT;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/set.js"], "sourcesContent": ["/**\n * Utility module to work with sets.\n *\n * @module set\n */\n\nexport const create = () => new Set()\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {Array<T>}\n */\nexport const toArray = set => Array.from(set)\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {T}\n */\nexport const first = set =>\n  set.values().next().value ?? undefined\n\n/**\n * @template T\n * @param {Iterable<T>} entries\n * @return {Set<T>}\n */\nexport const from = entries => new Set(entries)\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAEM,MAAM,SAAS,IAAM,IAAI;AAOzB,MAAM,UAAU,CAAA,MAAO,MAAM,IAAI,CAAC;AAOlC,MAAM,QAAQ,CAAA,MACnB,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI;AAOxB,MAAM,OAAO,CAAA,UAAW,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/array.js"], "sourcesContent": ["/**\n * Utility module to work with Arrays.\n *\n * @module array\n */\n\nimport * as set from './set.js'\n\n/**\n * Return the last element of an array. The element must exist\n *\n * @template L\n * @param {ArrayLike<L>} arr\n * @return {L}\n */\nexport const last = arr => arr[arr.length - 1]\n\n/**\n * @template C\n * @return {Array<C>}\n */\nexport const create = () => /** @type {Array<C>} */ ([])\n\n/**\n * @template D\n * @param {Array<D>} a\n * @return {Array<D>}\n */\nexport const copy = a => /** @type {Array<D>} */ (a.slice())\n\n/**\n * Append elements from src to dest\n *\n * @template M\n * @param {Array<M>} dest\n * @param {Array<M>} src\n */\nexport const appendTo = (dest, src) => {\n  for (let i = 0; i < src.length; i++) {\n    dest.push(src[i])\n  }\n}\n\n/**\n * Transforms something array-like to an actual Array.\n *\n * @function\n * @template T\n * @param {ArrayLike<T>|Iterable<T>} arraylike\n * @return {T}\n */\nexport const from = Array.from\n\n/**\n * True iff condition holds on every element in the Array.\n *\n * @function\n * @template ITEM\n * @template {ArrayLike<ITEM>} ARR\n *\n * @param {ARR} arr\n * @param {function(ITEM, number, ARR):boolean} f\n * @return {boolean}\n */\nexport const every = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (!f(arr[i], i, arr)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * True iff condition holds on some element in the Array.\n *\n * @function\n * @template S\n * @template {ArrayLike<S>} ARR\n * @param {ARR} arr\n * @param {function(S, number, ARR):boolean} f\n * @return {boolean}\n */\nexport const some = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (f(arr[i], i, arr)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @template ELEM\n *\n * @param {ArrayLike<ELEM>} a\n * @param {ArrayLike<ELEM>} b\n * @return {boolean}\n */\nexport const equalFlat = (a, b) => a.length === b.length && every(a, (item, index) => item === b[index])\n\n/**\n * @template ELEM\n * @param {Array<Array<ELEM>>} arr\n * @return {Array<ELEM>}\n */\nexport const flatten = arr => fold(arr, /** @type {Array<ELEM>} */ ([]), (acc, val) => acc.concat(val))\n\n/**\n * @template T\n * @param {number} len\n * @param {function(number, Array<T>):T} f\n * @return {Array<T>}\n */\nexport const unfold = (len, f) => {\n  const array = new Array(len)\n  for (let i = 0; i < len; i++) {\n    array[i] = f(i, array)\n  }\n  return array\n}\n\n/**\n * @template T\n * @template RESULT\n * @param {Array<T>} arr\n * @param {RESULT} seed\n * @param {function(RESULT, T, number):RESULT} folder\n */\nexport const fold = (arr, seed, folder) => arr.reduce(folder, seed)\n\nexport const isArray = Array.isArray\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {Array<T>}\n */\nexport const unique = arr => from(set.from(arr))\n\n/**\n * @template T\n * @template M\n * @param {ArrayLike<T>} arr\n * @param {function(T):M} mapper\n * @return {Array<T>}\n */\nexport const uniqueBy = (arr, mapper) => {\n  /**\n   * @type {Set<M>}\n   */\n  const happened = set.create()\n  /**\n   * @type {Array<T>}\n   */\n  const result = []\n  for (let i = 0; i < arr.length; i++) {\n    const el = arr[i]\n    const mapped = mapper(el)\n    if (!happened.has(mapped)) {\n      happened.add(mapped)\n      result.push(el)\n    }\n  }\n  return result\n}\n\n/**\n * @template {ArrayLike<any>} ARR\n * @template {function(ARR extends ArrayLike<infer T> ? T : never, number, ARR):any} MAPPER\n * @param {ARR} arr\n * @param {MAPPER} mapper\n * @return {Array<MAPPER extends function(...any): infer M ? M : never>}\n */\nexport const map = (arr, mapper) => {\n  /**\n   * @type {Array<any>}\n   */\n  const res = Array(arr.length)\n  for (let i = 0; i < arr.length; i++) {\n    res[i] = mapper(/** @type {any} */ (arr[i]), i, /** @type {any} */ (arr))\n  }\n  return /** @type {any} */ (res)\n}\n\n/**\n * This function bubble-sorts a single item to the correct position. The sort happens in-place and\n * might be useful to ensure that a single item is at the correct position in an otherwise sorted\n * array.\n *\n * @example\n *  const arr = [3, 2, 5]\n *  arr.sort((a, b) => a - b)\n *  arr // => [2, 3, 5]\n *  arr.splice(1, 0, 7)\n *  array.bubbleSortItem(arr, 1, (a, b) => a - b)\n *  arr // => [2, 3, 5, 7]\n *\n * @template T\n * @param {Array<T>} arr\n * @param {number} i\n * @param {(a:T,b:T) => number} compareFn\n */\nexport const bubblesortItem = (arr, i, compareFn) => {\n  const n = arr[i]\n  let j = i\n  // try to sort to the right\n  while (j + 1 < arr.length && compareFn(n, arr[j + 1]) > 0) {\n    arr[j] = arr[j + 1]\n    arr[++j] = n\n  }\n  if (i === j && j > 0) { // no change yet\n    // sort to the left\n    while (j > 0 && compareFn(arr[j - 1], n) > 0) {\n      arr[j] = arr[j - 1]\n      arr[--j] = n\n    }\n  }\n  return j\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;AAED;;AASO,MAAM,OAAO,CAAA,MAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;AAMvC,MAAM,SAAS,IAA+B,EAAE;AAOhD,MAAM,OAAO,CAAA,IAA8B,EAAE,KAAK;AASlD,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE;IAClB;AACF;AAUO,MAAM,OAAO,MAAM,IAAI;AAavB,MAAM,QAAQ,CAAC,KAAK;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM;YACtB,OAAO;QACT;IACF;IACA,OAAO;AACT;AAYO,MAAM,OAAO,CAAC,KAAK;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM;YACrB,OAAO;QACT;IACF;IACA,OAAO;AACT;AASO,MAAM,YAAY,CAAC,GAAG,IAAM,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,GAAG,CAAC,MAAM,QAAU,SAAS,CAAC,CAAC,MAAM;AAOhG,MAAM,UAAU,CAAA,MAAO,KAAK,KAAiC,EAAE,EAAG,CAAC,KAAK,MAAQ,IAAI,MAAM,CAAC;AAQ3F,MAAM,SAAS,CAAC,KAAK;IAC1B,MAAM,QAAQ,IAAI,MAAM;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG;IAClB;IACA,OAAO;AACT;AASO,MAAM,OAAO,CAAC,KAAK,MAAM,SAAW,IAAI,MAAM,CAAC,QAAQ;AAEvD,MAAM,UAAU,MAAM,OAAO;AAO7B,MAAM,SAAS,CAAA,MAAO,KAAK,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAE;AASpC,MAAM,WAAW,CAAC,KAAK;IAC5B;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC1B;;GAEC,GACD,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,MAAM,KAAK,GAAG,CAAC,EAAE;QACjB,MAAM,SAAS,OAAO;QACtB,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS;YACzB,SAAS,GAAG,CAAC;YACb,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AASO,MAAM,MAAM,CAAC,KAAK;IACvB;;GAEC,GACD,MAAM,MAAM,MAAM,IAAI,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,GAAG,CAAC,EAAE,GAAG,OAA2B,GAAG,CAAC,EAAE,EAAG,GAAuB;IACtE;IACA,OAA2B;AAC7B;AAoBO,MAAM,iBAAiB,CAAC,KAAK,GAAG;IACrC,MAAM,IAAI,GAAG,CAAC,EAAE;IAChB,IAAI,IAAI;IACR,2BAA2B;IAC3B,MAAO,IAAI,IAAI,IAAI,MAAM,IAAI,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,EAAG;QACzD,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE;QACnB,GAAG,CAAC,EAAE,EAAE,GAAG;IACb;IACA,IAAI,MAAM,KAAK,IAAI,GAAG;QACpB,mBAAmB;QACnB,MAAO,IAAI,KAAK,UAAU,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAG;YAC5C,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE;YACnB,GAAG,CAAC,EAAE,EAAE,GAAG;QACb;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/observable.js"], "sourcesContent": ["/**\n * Observable class prototype.\n *\n * @module observable\n */\n\nimport * as map from './map.js'\nimport * as set from './set.js'\nimport * as array from './array.js'\n\n/**\n * Handles named events.\n * @experimental\n *\n * This is basically a (better typed) duplicate of Observable, which will replace Observable in the\n * next release.\n *\n * @template {{[key in keyof EVENTS]: function(...any):void}} EVENTS\n */\nexport class ObservableV2 {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<string, Set<any>>}\n     */\n    this._observers = map.create()\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  on (name, f) {\n    map.setIfUndefined(this._observers, /** @type {string} */ (name), set.create).add(f)\n    return f\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, /** @type {any} */ (_f))\n      f(...args)\n    }\n    this.on(name, /** @type {any} */ (_f))\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name The event name.\n   * @param {Parameters<EVENTS[NAME]>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return array.from((this._observers.get(name) || map.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = map.create()\n  }\n}\n\n/* c8 ignore start */\n/**\n * Handles named events.\n *\n * @deprecated\n * @template N\n */\nexport class Observable {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<N, any>}\n     */\n    this._observers = map.create()\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  on (name, f) {\n    map.setIfUndefined(this._observers, name, set.create).add(f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, _f)\n      f(...args)\n    }\n    this.on(name, _f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @param {N} name The event name.\n   * @param {Array<any>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return array.from((this._observers.get(name) || map.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = map.create()\n  }\n}\n/* c8 ignore end */\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;AACA;;;;AAWO,MAAM;IACX,aAAe;QACb;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC7B;IAEA;;;;GAIC,GACD,GAAI,IAAI,EAAE,CAAC,EAAE;QACX,CAAA,GAAA,iLAAA,CAAA,iBAAkB,AAAD,EAAE,IAAI,CAAC,UAAU,EAAyB,MAAO,iLAAA,CAAA,SAAU,EAAE,GAAG,CAAC;QAClF,OAAO;IACT;IAEA;;;;GAIC,GACD,KAAM,IAAI,EAAE,CAAC,EAAE;QACb;;KAEC,GACD,MAAM,KAAK,CAAC,GAAG;YACb,IAAI,CAAC,GAAG,CAAC,MAA0B;YACnC,KAAK;QACP;QACA,IAAI,CAAC,EAAE,CAAC,MAA0B;IACpC;IAEA;;;;GAIC,GACD,IAAK,IAAI,EAAE,CAAC,EAAE;QACZ,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACtC,IAAI,cAAc,WAAW;YAC3B,UAAU,MAAM,CAAC;YACjB,IAAI,UAAU,IAAI,KAAK,GAAG;gBACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACzB;QACF;IACF;IAEA;;;;;;;;;GASC,GACD,KAAM,IAAI,EAAE,IAAI,EAAE;QAChB,iJAAiJ;QACjJ,OAAO,CAAA,GAAA,mLAAA,CAAA,OAAU,AAAD,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,GAAG,EAAE,MAAM,IAAI,OAAO,CAAC,CAAA,IAAK,KAAK;IAC5F;IAEA,UAAW;QACT,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC7B;AACF;AASO,MAAM;IACX,aAAe;QACb;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC7B;IAEA;;;GAGC,GACD,GAAI,IAAI,EAAE,CAAC,EAAE;QACX,CAAA,GAAA,iLAAA,CAAA,iBAAkB,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,iLAAA,CAAA,SAAU,EAAE,GAAG,CAAC;IAC5D;IAEA;;;GAGC,GACD,KAAM,IAAI,EAAE,CAAC,EAAE;QACb;;KAEC,GACD,MAAM,KAAK,CAAC,GAAG;YACb,IAAI,CAAC,GAAG,CAAC,MAAM;YACf,KAAK;QACP;QACA,IAAI,CAAC,EAAE,CAAC,MAAM;IAChB;IAEA;;;GAGC,GACD,IAAK,IAAI,EAAE,CAAC,EAAE;QACZ,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACtC,IAAI,cAAc,WAAW;YAC3B,UAAU,MAAM,CAAC;YACjB,IAAI,UAAU,IAAI,KAAK,GAAG;gBACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACzB;QACF;IACF;IAEA;;;;;;;;GAQC,GACD,KAAM,IAAI,EAAE,IAAI,EAAE;QAChB,iJAAiJ;QACjJ,OAAO,CAAA,GAAA,mLAAA,CAAA,OAAU,AAAD,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,GAAG,EAAE,MAAM,IAAI,OAAO,CAAC,CAAA,IAAK,KAAK;IAC5F;IAEA,UAAW;QACT,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC7B;AACF,EACA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/math.js"], "sourcesContent": ["/**\n * Common Math expressions.\n *\n * @module math\n */\n\nexport const floor = Math.floor\nexport const ceil = Math.ceil\nexport const abs = Math.abs\nexport const imul = Math.imul\nexport const round = Math.round\nexport const log10 = Math.log10\nexport const log2 = Math.log2\nexport const log = Math.log\nexport const sqrt = Math.sqrt\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The sum of a and b\n */\nexport const add = (a, b) => a + b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The smaller element of a and b\n */\nexport const min = (a, b) => a < b ? a : b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The bigger element of a and b\n */\nexport const max = (a, b) => a > b ? a : b\n\nexport const isNaN = Number.isNaN\n\nexport const pow = Math.pow\n/**\n * Base 10 exponential function. Returns the value of 10 raised to the power of pow.\n *\n * @param {number} exp\n * @return {number}\n */\nexport const exp10 = exp => Math.pow(10, exp)\n\nexport const sign = Math.sign\n\n/**\n * @param {number} n\n * @return {boolean} Wether n is negative. This function also differentiates between -0 and +0\n */\nexport const isNegativeZero = n => n !== 0 ? n < 0 : 1 / n < 0\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;AAEM,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,OAAO,KAAK,IAAI;AAQtB,MAAM,MAAM,CAAC,GAAG,IAAM,IAAI;AAQ1B,MAAM,MAAM,CAAC,GAAG,IAAM,IAAI,IAAI,IAAI;AAQlC,MAAM,MAAM,CAAC,GAAG,IAAM,IAAI,IAAI,IAAI;AAElC,MAAM,QAAQ,OAAO,KAAK;AAE1B,MAAM,MAAM,KAAK,GAAG;AAOpB,MAAM,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC,IAAI;AAElC,MAAM,OAAO,KAAK,IAAI;AAMtB,MAAM,iBAAiB,CAAA,IAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/binary.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Binary data constants.\n *\n * @module binary\n */\n\n/**\n * n-th bit activated.\n *\n * @type {number}\n */\nexport const BIT1 = 1\nexport const BIT2 = 2\nexport const BIT3 = 4\nexport const BIT4 = 8\nexport const BIT5 = 16\nexport const BIT6 = 32\nexport const BIT7 = 64\nexport const BIT8 = 128\nexport const BIT9 = 256\nexport const BIT10 = 512\nexport const BIT11 = 1024\nexport const BIT12 = 2048\nexport const BIT13 = 4096\nexport const BIT14 = 8192\nexport const BIT15 = 16384\nexport const BIT16 = 32768\nexport const BIT17 = 65536\nexport const BIT18 = 1 << 17\nexport const BIT19 = 1 << 18\nexport const BIT20 = 1 << 19\nexport const BIT21 = 1 << 20\nexport const BIT22 = 1 << 21\nexport const BIT23 = 1 << 22\nexport const BIT24 = 1 << 23\nexport const BIT25 = 1 << 24\nexport const BIT26 = 1 << 25\nexport const BIT27 = 1 << 26\nexport const BIT28 = 1 << 27\nexport const BIT29 = 1 << 28\nexport const BIT30 = 1 << 29\nexport const BIT31 = 1 << 30\nexport const BIT32 = 1 << 31\n\n/**\n * First n bits activated.\n *\n * @type {number}\n */\nexport const BITS0 = 0\nexport const BITS1 = 1\nexport const BITS2 = 3\nexport const BITS3 = 7\nexport const BITS4 = 15\nexport const BITS5 = 31\nexport const BITS6 = 63\nexport const BITS7 = 127\nexport const BITS8 = 255\nexport const BITS9 = 511\nexport const BITS10 = 1023\nexport const BITS11 = 2047\nexport const BITS12 = 4095\nexport const BITS13 = 8191\nexport const BITS14 = 16383\nexport const BITS15 = 32767\nexport const BITS16 = 65535\nexport const BITS17 = BIT18 - 1\nexport const BITS18 = BIT19 - 1\nexport const BITS19 = BIT20 - 1\nexport const BITS20 = BIT21 - 1\nexport const BITS21 = BIT22 - 1\nexport const BITS22 = BIT23 - 1\nexport const BITS23 = BIT24 - 1\nexport const BITS24 = BIT25 - 1\nexport const BITS25 = BIT26 - 1\nexport const BITS26 = BIT27 - 1\nexport const BITS27 = BIT28 - 1\nexport const BITS28 = BIT29 - 1\nexport const BITS29 = BIT30 - 1\nexport const BITS30 = BIT31 - 1\n/**\n * @type {number}\n */\nexport const BITS31 = 0x7FFFFFFF\n/**\n * @type {number}\n */\nexport const BITS32 = 0xFFFFFFFF\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;;;CAIC,GAED;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AACnB,MAAM,QAAQ,KAAK;AAOnB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AAIvB,MAAM,SAAS;AAIf,MAAM,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/number.js"], "sourcesContent": ["/**\n * Utility helpers for working with numbers.\n *\n * @module number\n */\n\nimport * as math from './math.js'\nimport * as binary from './binary.js'\n\nexport const MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER\nexport const MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER\n\nexport const LOWEST_INT32 = 1 << 31\nexport const HIGHEST_INT32 = binary.BITS31\nexport const HIGHEST_UINT32 = binary.BITS32\n\n/* c8 ignore next */\nexport const isInteger = Number.isInteger || (num => typeof num === 'number' && isFinite(num) && math.floor(num) === num)\nexport const isNaN = Number.isNaN\nexport const parseInt = Number.parseInt\n\n/**\n * Count the number of \"1\" bits in an unsigned 32bit number.\n *\n * Super fun bitcount algorithm by <PERSON>.\n *\n * @param {number} n\n */\nexport const countBits = n => {\n  n &= binary.BITS32\n  let count = 0\n  while (n) {\n    n &= (n - 1)\n    count++\n  }\n  return count\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;AAED;AACA;;;AAEO,MAAM,mBAAmB,OAAO,gBAAgB;AAChD,MAAM,mBAAmB,OAAO,gBAAgB;AAEhD,MAAM,eAAe,KAAK;AAC1B,MAAM,gBAAgB,oLAAA,CAAA,SAAa;AACnC,MAAM,iBAAiB,oLAAA,CAAA,SAAa;AAGpC,MAAM,YAAY,OAAO,SAAS,IAAI,CAAC,CAAA,MAAO,OAAO,QAAQ,YAAY,SAAS,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,SAAS,GAAG;AACjH,MAAM,QAAQ,OAAO,KAAK;AAC1B,MAAM,WAAW,OAAO,QAAQ;AAShC,MAAM,YAAY,CAAA;IACvB,KAAK,oLAAA,CAAA,SAAa;IAClB,IAAI,QAAQ;IACZ,MAAO,EAAG;QACR,KAAM,IAAI;QACV;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/string.js"], "sourcesContent": ["import * as array from './array.js'\n\n/**\n * Utility module to work with strings.\n *\n * @module string\n */\n\nexport const fromCharCode = String.fromCharCode\nexport const fromCodePoint = String.fromCodePoint\n\n/**\n * The largest utf16 character.\n * Corresponds to Uint8Array([255, 255]) or charcodeof(2x2^8)\n */\nexport const MAX_UTF16_CHARACTER = fromCharCode(65535)\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst toLowerCase = s => s.toLowerCase()\n\nconst trimLeftRegex = /^\\s*/g\n\n/**\n * @param {string} s\n * @return {string}\n */\nexport const trimLeft = s => s.replace(trimLeftRegex, '')\n\nconst fromCamelCaseRegex = /([A-Z])/g\n\n/**\n * @param {string} s\n * @param {string} separator\n * @return {string}\n */\nexport const fromCamelCase = (s, separator) => trimLeft(s.replace(fromCamelCaseRegex, match => `${separator}${toLowerCase(match)}`))\n\n/**\n * Compute the utf8ByteLength\n * @param {string} str\n * @return {number}\n */\nexport const utf8ByteLength = str => unescape(encodeURIComponent(str)).length\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nexport const _encodeUtf8Polyfill = str => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  const buf = new Uint8Array(len)\n  for (let i = 0; i < len; i++) {\n    buf[i] = /** @type {number} */ (encodedString.codePointAt(i))\n  }\n  return buf\n}\n\n/* c8 ignore next */\nexport const utf8TextEncoder = /** @type {TextEncoder} */ (typeof TextEncoder !== 'undefined' ? new TextEncoder() : null)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nexport const _encodeUtf8Native = str => utf8TextEncoder.encode(str)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\n/* c8 ignore next */\nexport const encodeUtf8 = utf8TextEncoder ? _encodeUtf8Native : _encodeUtf8Polyfill\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nexport const _decodeUtf8Polyfill = buf => {\n  let remainingLen = buf.length\n  let encodedString = ''\n  let bufPos = 0\n  while (remainingLen > 0) {\n    const nextLen = remainingLen < 10000 ? remainingLen : 10000\n    const bytes = buf.subarray(bufPos, bufPos + nextLen)\n    bufPos += nextLen\n    // Starting with ES5.1 we can supply a generic array-like object as arguments\n    encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n    remainingLen -= nextLen\n  }\n  return decodeURIComponent(escape(encodedString))\n}\n\n/* c8 ignore next */\nexport let utf8TextDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf-8', { fatal: true, ignoreBOM: true })\n\n/* c8 ignore start */\nif (utf8TextDecoder && utf8TextDecoder.decode(new Uint8Array()).length === 1) {\n  // Safari doesn't handle BOM correctly.\n  // This fixes a bug in Safari 13.0.5 where it produces a BOM the first time it is called.\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the first call and\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the second call\n  // Another issue is that from then on no BOM chars are recognized anymore\n  /* c8 ignore next */\n  utf8TextDecoder = null\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nexport const _decodeUtf8Native = buf => /** @type {TextDecoder} */ (utf8TextDecoder).decode(buf)\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\n/* c8 ignore next */\nexport const decodeUtf8 = utf8TextDecoder ? _decodeUtf8Native : _decodeUtf8Polyfill\n\n/**\n * @param {string} str The initial string\n * @param {number} index Starting position\n * @param {number} remove Number of characters to remove\n * @param {string} insert New content to insert\n */\nexport const splice = (str, index, remove, insert = '') => str.slice(0, index) + insert + str.slice(index + remove)\n\n/**\n * @param {string} source\n * @param {number} n\n */\nexport const repeat = (source, n) => array.unfold(n, () => source).join('')\n\n/**\n * Escape HTML characters &,<,>,',\" to their respective HTML entities &amp;,&lt;,&gt;,&#39;,&quot;\n *\n * @param {string} str\n */\nexport const escapeHTML = str =>\n  str.replace(/[&<>'\"]/g, r => /** @type {string} */ ({\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    \"'\": '&#39;',\n    '\"': '&quot;'\n  }[r]))\n\n/**\n * Reverse of `escapeHTML`\n *\n * @param {string} str\n */\nexport const unescapeHTML = str =>\n  str.replace(/&amp;|&lt;|&gt;|&#39;|&quot;/g, r => /** @type {string} */ ({\n    '&amp;': '&',\n    '&lt;': '<',\n    '&gt;': '>',\n    '&#39;': \"'\",\n    '&quot;': '\"'\n  }[r]))\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAQO,MAAM,eAAe,OAAO,YAAY;AACxC,MAAM,gBAAgB,OAAO,aAAa;AAM1C,MAAM,sBAAsB,aAAa;AAEhD;;;CAGC,GACD,MAAM,cAAc,CAAA,IAAK,EAAE,WAAW;AAEtC,MAAM,gBAAgB;AAMf,MAAM,WAAW,CAAA,IAAK,EAAE,OAAO,CAAC,eAAe;AAEtD,MAAM,qBAAqB;AAOpB,MAAM,gBAAgB,CAAC,GAAG,YAAc,SAAS,EAAE,OAAO,CAAC,oBAAoB,CAAA,QAAS,GAAG,YAAY,YAAY,QAAQ;AAO3H,MAAM,iBAAiB,CAAA,MAAO,SAAS,mBAAmB,MAAM,MAAM;AAMtE,MAAM,sBAAsB,CAAA;IACjC,MAAM,gBAAgB,SAAS,mBAAmB;IAClD,MAAM,MAAM,cAAc,MAAM;IAChC,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,GAAG,CAAC,EAAE,GAA0B,cAAc,WAAW,CAAC;IAC5D;IACA,OAAO;AACT;AAGO,MAAM,kBAA8C,OAAO,gBAAgB,cAAc,IAAI,gBAAgB;AAM7G,MAAM,oBAAoB,CAAA,MAAO,gBAAgB,MAAM,CAAC;AAOxD,MAAM,aAAa,kBAAkB,oBAAoB;AAMzD,MAAM,sBAAsB,CAAA;IACjC,IAAI,eAAe,IAAI,MAAM;IAC7B,IAAI,gBAAgB;IACpB,IAAI,SAAS;IACb,MAAO,eAAe,EAAG;QACvB,MAAM,UAAU,eAAe,QAAQ,eAAe;QACtD,MAAM,QAAQ,IAAI,QAAQ,CAAC,QAAQ,SAAS;QAC5C,UAAU;QACV,6EAA6E;QAC7E,iBAAiB,OAAO,aAAa,CAAC,KAAK,CAAC,MAA0B;QACtE,gBAAgB;IAClB;IACA,OAAO,mBAAmB,OAAO;AACnC;AAGO,IAAI,kBAAkB,OAAO,gBAAgB,cAAc,OAAO,IAAI,YAAY,SAAS;IAAE,OAAO;IAAM,WAAW;AAAK;AAEjI,mBAAmB,GACnB,IAAI,mBAAmB,gBAAgB,MAAM,CAAC,IAAI,cAAc,MAAM,KAAK,GAAG;IAC5E,uCAAuC;IACvC,yFAAyF;IACzF,8EAA8E;IAC9E,2EAA2E;IAC3E,yEAAyE;IACzE,kBAAkB,GAClB,kBAAkB;AACpB;AAOO,MAAM,oBAAoB,CAAA,MAAO,wBAAwB,GAAG,AAAC,gBAAiB,MAAM,CAAC;AAOrF,MAAM,aAAa,kBAAkB,oBAAoB;AAQzD,MAAM,SAAS,CAAC,KAAK,OAAO,QAAQ,SAAS,EAAE,GAAK,IAAI,KAAK,CAAC,GAAG,SAAS,SAAS,IAAI,KAAK,CAAC,QAAQ;AAMrG,MAAM,SAAS,CAAC,QAAQ,IAAM,CAAA,GAAA,mLAAA,CAAA,SAAY,AAAD,EAAE,GAAG,IAAM,QAAQ,IAAI,CAAC;AAOjE,MAAM,aAAa,CAAA,MACxB,IAAI,OAAO,CAAC,YAAY,CAAA,IAA4B,CAAA;YAClD,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,CAAA,CAAC,CAAC,EAAE;AAOC,MAAM,eAAe,CAAA,MAC1B,IAAI,OAAO,CAAC,iCAAiC,CAAA,IAA4B,CAAA;YACvE,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,UAAU;QACZ,CAAA,CAAC,CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/encoding.js"], "sourcesContent": ["/**\n * Efficient schema-less binary encoding with support for variable length encoding.\n *\n * Use [lib0/encoding] with [lib0/decoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module encoding\n */\n\nimport * as math from './math.js'\nimport * as number from './number.js'\nimport * as binary from './binary.js'\nimport * as string from './string.js'\nimport * as array from './array.js'\n\n/**\n * A BinaryEncoder handles the encoding to an Uint8Array.\n */\nexport class Encoder {\n  constructor () {\n    this.cpos = 0\n    this.cbuf = new Uint8Array(100)\n    /**\n     * @type {Array<Uint8Array>}\n     */\n    this.bufs = []\n  }\n}\n\n/**\n * @function\n * @return {Encoder}\n */\nexport const createEncoder = () => new Encoder()\n\n/**\n * @param {function(Encoder):void} f\n */\nexport const encode = (f) => {\n  const encoder = createEncoder()\n  f(encoder)\n  return toUint8Array(encoder)\n}\n\n/**\n * The current length of the encoded data.\n *\n * @function\n * @param {Encoder} encoder\n * @return {number}\n */\nexport const length = encoder => {\n  let len = encoder.cpos\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    len += encoder.bufs[i].length\n  }\n  return len\n}\n\n/**\n * Check whether encoder is empty.\n *\n * @function\n * @param {Encoder} encoder\n * @return {boolean}\n */\nexport const hasContent = encoder => encoder.cpos > 0 || encoder.bufs.length > 0\n\n/**\n * Transform to Uint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @return {Uint8Array} The created ArrayBuffer.\n */\nexport const toUint8Array = encoder => {\n  const uint8arr = new Uint8Array(length(encoder))\n  let curPos = 0\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    const d = encoder.bufs[i]\n    uint8arr.set(d, curPos)\n    curPos += d.length\n  }\n  uint8arr.set(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos), curPos)\n  return uint8arr\n}\n\n/**\n * Verify that it is possible to write `len` bytes wtihout checking. If\n * necessary, a new Buffer with the required length is attached.\n *\n * @param {Encoder} encoder\n * @param {number} len\n */\nexport const verifyLen = (encoder, len) => {\n  const bufferLen = encoder.cbuf.length\n  if (bufferLen - encoder.cpos < len) {\n    encoder.bufs.push(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos))\n    encoder.cbuf = new Uint8Array(math.max(bufferLen, len) * 2)\n    encoder.cpos = 0\n  }\n}\n\n/**\n * Write one byte to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The byte that is to be encoded.\n */\nexport const write = (encoder, num) => {\n  const bufferLen = encoder.cbuf.length\n  if (encoder.cpos === bufferLen) {\n    encoder.bufs.push(encoder.cbuf)\n    encoder.cbuf = new Uint8Array(bufferLen * 2)\n    encoder.cpos = 0\n  }\n  encoder.cbuf[encoder.cpos++] = num\n}\n\n/**\n * Write one byte at a specific position.\n * Position must already be written (i.e. encoder.length > pos)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos Position to which to write data\n * @param {number} num Unsigned 8-bit integer\n */\nexport const set = (encoder, pos, num) => {\n  let buffer = null\n  // iterate all buffers and adjust position\n  for (let i = 0; i < encoder.bufs.length && buffer === null; i++) {\n    const b = encoder.bufs[i]\n    if (pos < b.length) {\n      buffer = b // found buffer\n    } else {\n      pos -= b.length\n    }\n  }\n  if (buffer === null) {\n    // use current buffer\n    buffer = encoder.cbuf\n  }\n  buffer[pos] = num\n}\n\n/**\n * Write one byte as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeUint8 = write\n\n/**\n * Write one byte as an unsigned Integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nexport const setUint8 = set\n\n/**\n * Write two bytes as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeUint16 = (encoder, num) => {\n  write(encoder, num & binary.BITS8)\n  write(encoder, (num >>> 8) & binary.BITS8)\n}\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nexport const setUint16 = (encoder, pos, num) => {\n  set(encoder, pos, num & binary.BITS8)\n  set(encoder, pos + 1, (num >>> 8) & binary.BITS8)\n}\n\n/**\n * Write two bytes as an unsigned integer\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeUint32 = (encoder, num) => {\n  for (let i = 0; i < 4; i++) {\n    write(encoder, num & binary.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeUint32BigEndian = (encoder, num) => {\n  for (let i = 3; i >= 0; i--) {\n    write(encoder, (num >>> (8 * i)) & binary.BITS8)\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nexport const setUint32 = (encoder, pos, num) => {\n  for (let i = 0; i < 4; i++) {\n    set(encoder, pos + i, num & binary.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write a variable length unsigned integer. Max encodable integer is 2^53.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeVarUint = (encoder, num) => {\n  while (num > binary.BITS7) {\n    write(encoder, binary.BIT8 | (binary.BITS7 & num))\n    num = math.floor(num / 128) // shift >>> 7\n  }\n  write(encoder, binary.BITS7 & num)\n}\n\n/**\n * Write a variable length integer.\n *\n * We use the 7th bit instead for signaling that this is a negative number.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeVarInt = (encoder, num) => {\n  const isNegative = math.isNegativeZero(num)\n  if (isNegative) {\n    num = -num\n  }\n  //             |- whether to continue reading         |- whether is negative     |- number\n  write(encoder, (num > binary.BITS6 ? binary.BIT8 : 0) | (isNegative ? binary.BIT7 : 0) | (binary.BITS6 & num))\n  num = math.floor(num / 64) // shift >>> 6\n  // We don't need to consider the case of num === 0 so we can use a different\n  // pattern here than above.\n  while (num > 0) {\n    write(encoder, (num > binary.BITS7 ? binary.BIT8 : 0) | (binary.BITS7 & num))\n    num = math.floor(num / 128) // shift >>> 7\n  }\n}\n\n/**\n * A cache to store strings temporarily\n */\nconst _strBuffer = new Uint8Array(30000)\nconst _maxStrBSize = _strBuffer.length / 3\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nexport const _writeVarStringNative = (encoder, str) => {\n  if (str.length < _maxStrBSize) {\n    // We can encode the string into the existing buffer\n    /* c8 ignore next */\n    const written = string.utf8TextEncoder.encodeInto(str, _strBuffer).written || 0\n    writeVarUint(encoder, written)\n    for (let i = 0; i < written; i++) {\n      write(encoder, _strBuffer[i])\n    }\n  } else {\n    writeVarUint8Array(encoder, string.encodeUtf8(str))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nexport const _writeVarStringPolyfill = (encoder, str) => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    write(encoder, /** @type {number} */ (encodedString.codePointAt(i)))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\n/* c8 ignore next */\nexport const writeVarString = (string.utf8TextEncoder && /** @type {any} */ (string.utf8TextEncoder).encodeInto) ? _writeVarStringNative : _writeVarStringPolyfill\n\n/**\n * Write a string terminated by a special byte sequence. This is not very performant and is\n * generally discouraged. However, the resulting byte arrays are lexiographically ordered which\n * makes this a nice feature for databases.\n *\n * The string will be encoded using utf8 and then terminated and escaped using writeTerminatingUint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nexport const writeTerminatedString = (encoder, str) =>\n  writeTerminatedUint8Array(encoder, string.encodeUtf8(str))\n\n/**\n * Write a terminating Uint8Array. Note that this is not performant and is generally\n * discouraged. There are few situations when this is needed.\n *\n * We use 0x0 as a terminating character. 0x1 serves as an escape character for 0x0 and 0x1.\n *\n * Example: [0,1,2] is encoded to [1,0,1,1,2,0]. 0x0, and 0x1 needed to be escaped using 0x1. Then\n * the result is terminated using the 0x0 character.\n *\n * This is basically how many systems implement null terminated strings. However, we use an escape\n * character 0x1 to avoid issues and potenial attacks on our database (if this is used as a key\n * encoder for NoSql databases).\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} buf The string that is to be encoded.\n */\nexport const writeTerminatedUint8Array = (encoder, buf) => {\n  for (let i = 0; i < buf.length; i++) {\n    const b = buf[i]\n    if (b === 0 || b === 1) {\n      write(encoder, 1)\n    }\n    write(encoder, buf[i])\n  }\n  write(encoder, 0)\n}\n\n/**\n * Write the content of another Encoder.\n *\n * @TODO: can be improved!\n *        - Note: Should consider that when appending a lot of small Encoders, we should rather clone than referencing the old structure.\n *                Encoders start with a rather big initial buffer.\n *\n * @function\n * @param {Encoder} encoder The enUint8Arr\n * @param {Encoder} append The BinaryEncoder to be written.\n */\nexport const writeBinaryEncoder = (encoder, append) => writeUint8Array(encoder, toUint8Array(append))\n\n/**\n * Append fixed-length Uint8Array to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nexport const writeUint8Array = (encoder, uint8Array) => {\n  const bufferLen = encoder.cbuf.length\n  const cpos = encoder.cpos\n  const leftCopyLen = math.min(bufferLen - cpos, uint8Array.length)\n  const rightCopyLen = uint8Array.length - leftCopyLen\n  encoder.cbuf.set(uint8Array.subarray(0, leftCopyLen), cpos)\n  encoder.cpos += leftCopyLen\n  if (rightCopyLen > 0) {\n    // Still something to write, write right half..\n    // Append new buffer\n    encoder.bufs.push(encoder.cbuf)\n    // must have at least size of remaining buffer\n    encoder.cbuf = new Uint8Array(math.max(bufferLen * 2, rightCopyLen))\n    // copy array\n    encoder.cbuf.set(uint8Array.subarray(leftCopyLen))\n    encoder.cpos = rightCopyLen\n  }\n}\n\n/**\n * Append an Uint8Array to Encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nexport const writeVarUint8Array = (encoder, uint8Array) => {\n  writeVarUint(encoder, uint8Array.byteLength)\n  writeUint8Array(encoder, uint8Array)\n}\n\n/**\n * Create an DataView of the next `len` bytes. Use it to write data after\n * calling this function.\n *\n * ```js\n * // write float32 using DataView\n * const dv = writeOnDataView(encoder, 4)\n * dv.setFloat32(0, 1.1)\n * // read float32 using DataView\n * const dv = readFromDataView(encoder, 4)\n * dv.getFloat32(0) // => 1.100000023841858 (leaving it to the reader to find out why this is the correct result)\n * ```\n *\n * @param {Encoder} encoder\n * @param {number} len\n * @return {DataView}\n */\nexport const writeOnDataView = (encoder, len) => {\n  verifyLen(encoder, len)\n  const dview = new DataView(encoder.cbuf.buffer, encoder.cpos, len)\n  encoder.cpos += len\n  return dview\n}\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nexport const writeFloat32 = (encoder, num) => writeOnDataView(encoder, 4).setFloat32(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nexport const writeFloat64 = (encoder, num) => writeOnDataView(encoder, 8).setFloat64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nexport const writeBigInt64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigInt64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nexport const writeBigUint64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigUint64(0, num, false)\n\nconst floatTestBed = new DataView(new ArrayBuffer(4))\n/**\n * Check if a number can be encoded as a 32 bit float.\n *\n * @param {number} num\n * @return {boolean}\n */\nconst isFloat32 = num => {\n  floatTestBed.setFloat32(0, num)\n  return floatTestBed.getFloat32(0) === num\n}\n\n/**\n * Encode data with efficient binary format.\n *\n * Differences to JSON:\n * • Transforms data to a binary format (not to a string)\n * • Encodes undefined, NaN, and ArrayBuffer (these can't be represented in JSON)\n * • Numbers are efficiently encoded either as a variable length integer, as a\n *   32 bit float, as a 64 bit float, or as a 64 bit bigint.\n *\n * Encoding table:\n *\n * | Data Type           | Prefix   | Encoding Method    | Comment |\n * | ------------------- | -------- | ------------------ | ------- |\n * | undefined           | 127      |                    | Functions, symbol, and everything that cannot be identified is encoded as undefined |\n * | null                | 126      |                    | |\n * | integer             | 125      | writeVarInt        | Only encodes 32 bit signed integers |\n * | float32             | 124      | writeFloat32       | |\n * | float64             | 123      | writeFloat64       | |\n * | bigint              | 122      | writeBigInt64      | |\n * | boolean (false)     | 121      |                    | True and false are different data types so we save the following byte |\n * | boolean (true)      | 120      |                    | - 0b01111000 so the last bit determines whether true or false |\n * | string              | 119      | writeVarString     | |\n * | object<string,any>  | 118      | custom             | Writes {length} then {length} key-value pairs |\n * | array<any>          | 117      | custom             | Writes {length} then {length} json values |\n * | Uint8Array          | 116      | writeVarUint8Array | We use Uint8Array for any kind of binary data |\n *\n * Reasons for the decreasing prefix:\n * We need the first bit for extendability (later we may want to encode the\n * prefix with writeVarUint). The remaining 7 bits are divided as follows:\n * [0-30]   the beginning of the data range is used for custom purposes\n *          (defined by the function that uses this library)\n * [31-127] the end of the data range is used for data encoding by\n *          lib0/encoding.js\n *\n * @param {Encoder} encoder\n * @param {undefined|null|number|bigint|boolean|string|Object<string,any>|Array<any>|Uint8Array} data\n */\nexport const writeAny = (encoder, data) => {\n  switch (typeof data) {\n    case 'string':\n      // TYPE 119: STRING\n      write(encoder, 119)\n      writeVarString(encoder, data)\n      break\n    case 'number':\n      if (number.isInteger(data) && math.abs(data) <= binary.BITS31) {\n        // TYPE 125: INTEGER\n        write(encoder, 125)\n        writeVarInt(encoder, data)\n      } else if (isFloat32(data)) {\n        // TYPE 124: FLOAT32\n        write(encoder, 124)\n        writeFloat32(encoder, data)\n      } else {\n        // TYPE 123: FLOAT64\n        write(encoder, 123)\n        writeFloat64(encoder, data)\n      }\n      break\n    case 'bigint':\n      // TYPE 122: BigInt\n      write(encoder, 122)\n      writeBigInt64(encoder, data)\n      break\n    case 'object':\n      if (data === null) {\n        // TYPE 126: null\n        write(encoder, 126)\n      } else if (array.isArray(data)) {\n        // TYPE 117: Array\n        write(encoder, 117)\n        writeVarUint(encoder, data.length)\n        for (let i = 0; i < data.length; i++) {\n          writeAny(encoder, data[i])\n        }\n      } else if (data instanceof Uint8Array) {\n        // TYPE 116: ArrayBuffer\n        write(encoder, 116)\n        writeVarUint8Array(encoder, data)\n      } else {\n        // TYPE 118: Object\n        write(encoder, 118)\n        const keys = Object.keys(data)\n        writeVarUint(encoder, keys.length)\n        for (let i = 0; i < keys.length; i++) {\n          const key = keys[i]\n          writeVarString(encoder, key)\n          writeAny(encoder, data[key])\n        }\n      }\n      break\n    case 'boolean':\n      // TYPE 120/121: boolean (true/false)\n      write(encoder, data ? 120 : 121)\n      break\n    default:\n      // TYPE 127: undefined\n      write(encoder, 127)\n  }\n}\n\n/**\n * Now come a few stateful encoder that have their own classes.\n */\n\n/**\n * Basic Run Length Encoder - a basic compression implementation.\n *\n * Encodes [1,1,1,7] to [1,3,7,1] (3 times 1, 1 time 7). This encoder might do more harm than good if there are a lot of values that are not repeated.\n *\n * It was originally used for image compression. Cool .. article http://csbruce.com/cbm/transactor/pdfs/trans_v7_i06.pdf\n *\n * @note T must not be null!\n *\n * @template T\n */\nexport class RleEncoder extends Encoder {\n  /**\n   * @param {function(Encoder, T):void} writer\n   */\n  constructor (writer) {\n    super()\n    /**\n     * The writer\n     */\n    this.w = writer\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  /**\n   * @param {T} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      this.w(this, v)\n      this.s = v\n    }\n  }\n}\n\n/**\n * Basic diff decoder using variable length encoding.\n *\n * Encodes the values [3, 1100, 1101, 1050, 0] to [3, 1097, 1, -51, -1050] using writeVarInt.\n */\nexport class IntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    writeVarInt(this, v - this.s)\n    this.s = v\n  }\n}\n\n/**\n * A combination of IntDiffEncoder and RleEncoder.\n *\n * Basically first writes the IntDiffEncoder and then counts duplicate diffs using RleEncoding.\n *\n * Encodes the values [1,1,1,2,3,4,5,6] as [1,1,0,2,1,5] (RLE([1,0,0,1,1,1,1,1]) ⇒ RleIntDiff[1,1,0,2,1,5])\n */\nexport class RleIntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v && this.count > 0) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      writeVarInt(this, v - this.s)\n      this.s = v\n    }\n  }\n}\n\n/**\n * @param {UintOptRleEncoder} encoder\n */\nconst flushUintOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set sign to positive\n    // case 2: write several values. set sign to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encoder.count === 1 ? encoder.s : -encoder.s)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * Optimized Rle encoder that does not suffer from the mentioned problem of the basic Rle encoder.\n *\n * Internally uses VarInt encoder to write unsigned integers. If the input occurs multiple times, we write\n * write it as a negative number. The UintOptRleDecoder then understands that it needs to read a count.\n *\n * Encodes [1,2,3,3,3] as [1,2,-3,3] (once 1, once 2, three times 3)\n */\nexport class UintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Increasing Uint Optimized RLE Encoder\n *\n * The RLE encoder counts the number of same occurences of the same value.\n * The IncUintOptRle encoder counts if the value increases.\n * I.e. 7, 8, 9, 10 will be encoded as [-7, 4]. 1, 3, 5 will be encoded\n * as [1, 3, 5].\n */\nexport class IncUintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s + this.count === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * @param {IntDiffOptRleEncoder} encoder\n */\nconst flushIntDiffOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    //          31 bit making up the diff | wether to write the counter\n    // const encodedDiff = encoder.diff << 1 | (encoder.count === 1 ? 0 : 1)\n    const encodedDiff = encoder.diff * 2 + (encoder.count === 1 ? 0 : 1)\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set first bit to positive\n    // case 2: write several values. set first bit to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encodedDiff)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * A combination of the IntDiffEncoder and the UintOptRleEncoder.\n *\n * The count approach is similar to the UintDiffOptRleEncoder, but instead of using the negative bitflag, it encodes\n * in the LSB whether a count is to be read. Therefore this Encoder only supports 31 bit integers!\n *\n * Encodes [1, 2, 3, 2] as [3, 1, 6, -1] (more specifically [(1 << 1) | 1, (3 << 0) | 0, -1])\n *\n * Internally uses variable length encoding. Contrary to normal UintVar encoding, the first byte contains:\n * * 1 bit that denotes whether the next value is a count (LSB)\n * * 1 bit that denotes whether this value is negative (MSB - 1)\n * * 1 bit that denotes whether to continue reading the variable length integer (MSB)\n *\n * Therefore, only five bits remain to encode diff ranges.\n *\n * Use this Encoder only when appropriate. In most cases, this is probably a bad idea.\n */\nexport class IntDiffOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.diff === v - this.s) {\n      this.s = v\n      this.count++\n    } else {\n      flushIntDiffOptRleEncoder(this)\n      this.count = 1\n      this.diff = v - this.s\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushIntDiffOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Optimized String Encoder.\n *\n * Encoding many small strings in a simple Encoder is not very efficient. The function call to decode a string takes some time and creates references that must be eventually deleted.\n * In practice, when decoding several million small strings, the GC will kick in more and more often to collect orphaned string objects (or maybe there is another reason?).\n *\n * This string encoder solves the above problem. All strings are concatenated and written as a single string using a single encoding call.\n *\n * The lengths are encoded using a UintOptRleEncoder.\n */\nexport class StringEncoder {\n  constructor () {\n    /**\n     * @type {Array<string>}\n     */\n    this.sarr = []\n    this.s = ''\n    this.lensE = new UintOptRleEncoder()\n  }\n\n  /**\n   * @param {string} string\n   */\n  write (string) {\n    this.s += string\n    if (this.s.length > 19) {\n      this.sarr.push(this.s)\n      this.s = ''\n    }\n    this.lensE.write(string.length)\n  }\n\n  toUint8Array () {\n    const encoder = new Encoder()\n    this.sarr.push(this.s)\n    this.s = ''\n    writeVarString(encoder, this.sarr.join(''))\n    writeUint8Array(encoder, this.lensE.toUint8Array())\n    return toUint8Array(encoder)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAKO,MAAM;IACX,aAAe;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW;QAC3B;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG,EAAE;IAChB;AACF;AAMO,MAAM,gBAAgB,IAAM,IAAI;AAKhC,MAAM,SAAS,CAAC;IACrB,MAAM,UAAU;IAChB,EAAE;IACF,OAAO,aAAa;AACtB;AASO,MAAM,SAAS,CAAA;IACpB,IAAI,MAAM,QAAQ,IAAI;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAK;QAC5C,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,MAAM;IAC/B;IACA,OAAO;AACT;AASO,MAAM,aAAa,CAAA,UAAW,QAAQ,IAAI,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,GAAG;AASxE,MAAM,eAAe,CAAA;IAC1B,MAAM,WAAW,IAAI,WAAW,OAAO;IACvC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAK;QAC5C,MAAM,IAAI,QAAQ,IAAI,CAAC,EAAE;QACzB,SAAS,GAAG,CAAC,GAAG;QAChB,UAAU,EAAE,MAAM;IACpB;IACA,SAAS,GAAG,CAAC,IAAI,WAAW,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI,GAAG;IACnE,OAAO;AACT;AASO,MAAM,YAAY,CAAC,SAAS;IACjC,MAAM,YAAY,QAAQ,IAAI,CAAC,MAAM;IACrC,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;QAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI;QACrE,QAAQ,IAAI,GAAG,IAAI,WAAW,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,WAAW,OAAO;QACzD,QAAQ,IAAI,GAAG;IACjB;AACF;AASO,MAAM,QAAQ,CAAC,SAAS;IAC7B,MAAM,YAAY,QAAQ,IAAI,CAAC,MAAM;IACrC,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI;QAC9B,QAAQ,IAAI,GAAG,IAAI,WAAW,YAAY;QAC1C,QAAQ,IAAI,GAAG;IACjB;IACA,QAAQ,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACjC;AAWO,MAAM,MAAM,CAAC,SAAS,KAAK;IAChC,IAAI,SAAS;IACb,0CAA0C;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,IAAI,WAAW,MAAM,IAAK;QAC/D,MAAM,IAAI,QAAQ,IAAI,CAAC,EAAE;QACzB,IAAI,MAAM,EAAE,MAAM,EAAE;YAClB,SAAS,EAAE,eAAe;;QAC5B,OAAO;YACL,OAAO,EAAE,MAAM;QACjB;IACF;IACA,IAAI,WAAW,MAAM;QACnB,qBAAqB;QACrB,SAAS,QAAQ,IAAI;IACvB;IACA,MAAM,CAAC,IAAI,GAAG;AAChB;AASO,MAAM,aAAa;AAUnB,MAAM,WAAW;AASjB,MAAM,cAAc,CAAC,SAAS;IACnC,MAAM,SAAS,MAAM,oLAAA,CAAA,QAAY;IACjC,MAAM,SAAS,AAAC,QAAQ,IAAK,oLAAA,CAAA,QAAY;AAC3C;AASO,MAAM,YAAY,CAAC,SAAS,KAAK;IACtC,IAAI,SAAS,KAAK,MAAM,oLAAA,CAAA,QAAY;IACpC,IAAI,SAAS,MAAM,GAAG,AAAC,QAAQ,IAAK,oLAAA,CAAA,QAAY;AAClD;AASO,MAAM,cAAc,CAAC,SAAS;IACnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,SAAS,MAAM,oLAAA,CAAA,QAAY;QACjC,SAAS;IACX;AACF;AAUO,MAAM,uBAAuB,CAAC,SAAS;IAC5C,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;QAC3B,MAAM,SAAS,AAAC,QAAS,IAAI,IAAM,oLAAA,CAAA,QAAY;IACjD;AACF;AAUO,MAAM,YAAY,CAAC,SAAS,KAAK;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,IAAI,SAAS,MAAM,GAAG,MAAM,oLAAA,CAAA,QAAY;QACxC,SAAS;IACX;AACF;AASO,MAAM,eAAe,CAAC,SAAS;IACpC,MAAO,MAAM,oLAAA,CAAA,QAAY,CAAE;QACzB,MAAM,SAAS,oLAAA,CAAA,OAAW,GAAI,oLAAA,CAAA,QAAY,GAAG;QAC7C,MAAM,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,MAAM,KAAK,cAAc;;IAC5C;IACA,MAAM,SAAS,oLAAA,CAAA,QAAY,GAAG;AAChC;AAWO,MAAM,cAAc,CAAC,SAAS;IACnC,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,iBAAmB,AAAD,EAAE;IACvC,IAAI,YAAY;QACd,MAAM,CAAC;IACT;IACA,0FAA0F;IAC1F,MAAM,SAAS,CAAC,MAAM,oLAAA,CAAA,QAAY,GAAG,oLAAA,CAAA,OAAW,GAAG,CAAC,IAAI,CAAC,aAAa,oLAAA,CAAA,OAAW,GAAG,CAAC,IAAK,oLAAA,CAAA,QAAY,GAAG;IACzG,MAAM,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,MAAM,IAAI,cAAc;;IACzC,4EAA4E;IAC5E,2BAA2B;IAC3B,MAAO,MAAM,EAAG;QACd,MAAM,SAAS,CAAC,MAAM,oLAAA,CAAA,QAAY,GAAG,oLAAA,CAAA,OAAW,GAAG,CAAC,IAAK,oLAAA,CAAA,QAAY,GAAG;QACxE,MAAM,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,MAAM,KAAK,cAAc;;IAC5C;AACF;AAEA;;CAEC,GACD,MAAM,aAAa,IAAI,WAAW;AAClC,MAAM,eAAe,WAAW,MAAM,GAAG;AASlC,MAAM,wBAAwB,CAAC,SAAS;IAC7C,IAAI,IAAI,MAAM,GAAG,cAAc;QAC7B,oDAAoD;QACpD,kBAAkB,GAClB,MAAM,UAAU,oLAAA,CAAA,kBAAsB,CAAC,UAAU,CAAC,KAAK,YAAY,OAAO,IAAI;QAC9E,aAAa,SAAS;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAChC,MAAM,SAAS,UAAU,CAAC,EAAE;QAC9B;IACF,OAAO;QACL,mBAAmB,SAAS,CAAA,GAAA,oLAAA,CAAA,aAAiB,AAAD,EAAE;IAChD;AACF;AASO,MAAM,0BAA0B,CAAC,SAAS;IAC/C,MAAM,gBAAgB,SAAS,mBAAmB;IAClD,MAAM,MAAM,cAAc,MAAM;IAChC,aAAa,SAAS;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,SAAgC,cAAc,WAAW,CAAC;IAClE;AACF;AAUO,MAAM,iBAAiB,AAAC,oLAAA,CAAA,kBAAsB,IAAI,gBAAgB,GAAG,AAAC,oLAAA,CAAA,kBAAsB,CAAE,UAAU,GAAI,wBAAwB;AAapI,MAAM,wBAAwB,CAAC,SAAS,MAC7C,0BAA0B,SAAS,CAAA,GAAA,oLAAA,CAAA,aAAiB,AAAD,EAAE;AAmBhD,MAAM,4BAA4B,CAAC,SAAS;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,MAAM,IAAI,GAAG,CAAC,EAAE;QAChB,IAAI,MAAM,KAAK,MAAM,GAAG;YACtB,MAAM,SAAS;QACjB;QACA,MAAM,SAAS,GAAG,CAAC,EAAE;IACvB;IACA,MAAM,SAAS;AACjB;AAaO,MAAM,qBAAqB,CAAC,SAAS,SAAW,gBAAgB,SAAS,aAAa;AAStF,MAAM,kBAAkB,CAAC,SAAS;IACvC,MAAM,YAAY,QAAQ,IAAI,CAAC,MAAM;IACrC,MAAM,OAAO,QAAQ,IAAI;IACzB,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,YAAY,MAAM,WAAW,MAAM;IAChE,MAAM,eAAe,WAAW,MAAM,GAAG;IACzC,QAAQ,IAAI,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,GAAG,cAAc;IACtD,QAAQ,IAAI,IAAI;IAChB,IAAI,eAAe,GAAG;QACpB,+CAA+C;QAC/C,oBAAoB;QACpB,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI;QAC9B,8CAA8C;QAC9C,QAAQ,IAAI,GAAG,IAAI,WAAW,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,YAAY,GAAG;QACtD,aAAa;QACb,QAAQ,IAAI,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC;QACrC,QAAQ,IAAI,GAAG;IACjB;AACF;AASO,MAAM,qBAAqB,CAAC,SAAS;IAC1C,aAAa,SAAS,WAAW,UAAU;IAC3C,gBAAgB,SAAS;AAC3B;AAmBO,MAAM,kBAAkB,CAAC,SAAS;IACvC,UAAU,SAAS;IACnB,MAAM,QAAQ,IAAI,SAAS,QAAQ,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;IAC9D,QAAQ,IAAI,IAAI;IAChB,OAAO;AACT;AAMO,MAAM,eAAe,CAAC,SAAS,MAAQ,gBAAgB,SAAS,GAAG,UAAU,CAAC,GAAG,KAAK;AAMtF,MAAM,eAAe,CAAC,SAAS,MAAQ,gBAAgB,SAAS,GAAG,UAAU,CAAC,GAAG,KAAK;AAMtF,MAAM,gBAAgB,CAAC,SAAS,MAAQ,gBAAgB,GAAG,AAAC,gBAAgB,SAAS,GAAI,WAAW,CAAC,GAAG,KAAK;AAM7G,MAAM,iBAAiB,CAAC,SAAS,MAAQ,gBAAgB,GAAG,AAAC,gBAAgB,SAAS,GAAI,YAAY,CAAC,GAAG,KAAK;AAEtH,MAAM,eAAe,IAAI,SAAS,IAAI,YAAY;AAClD;;;;;CAKC,GACD,MAAM,YAAY,CAAA;IAChB,aAAa,UAAU,CAAC,GAAG;IAC3B,OAAO,aAAa,UAAU,CAAC,OAAO;AACxC;AAuCO,MAAM,WAAW,CAAC,SAAS;IAChC,OAAQ,OAAO;QACb,KAAK;YACH,mBAAmB;YACnB,MAAM,SAAS;YACf,eAAe,SAAS;YACxB;QACF,KAAK;YACH,IAAI,CAAA,GAAA,oLAAA,CAAA,YAAgB,AAAD,EAAE,SAAS,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,SAAS,oLAAA,CAAA,SAAa,EAAE;gBAC7D,oBAAoB;gBACpB,MAAM,SAAS;gBACf,YAAY,SAAS;YACvB,OAAO,IAAI,UAAU,OAAO;gBAC1B,oBAAoB;gBACpB,MAAM,SAAS;gBACf,aAAa,SAAS;YACxB,OAAO;gBACL,oBAAoB;gBACpB,MAAM,SAAS;gBACf,aAAa,SAAS;YACxB;YACA;QACF,KAAK;YACH,mBAAmB;YACnB,MAAM,SAAS;YACf,cAAc,SAAS;YACvB;QACF,KAAK;YACH,IAAI,SAAS,MAAM;gBACjB,iBAAiB;gBACjB,MAAM,SAAS;YACjB,OAAO,IAAI,CAAA,GAAA,mLAAA,CAAA,UAAa,AAAD,EAAE,OAAO;gBAC9B,kBAAkB;gBAClB,MAAM,SAAS;gBACf,aAAa,SAAS,KAAK,MAAM;gBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;oBACpC,SAAS,SAAS,IAAI,CAAC,EAAE;gBAC3B;YACF,OAAO,IAAI,gBAAgB,YAAY;gBACrC,wBAAwB;gBACxB,MAAM,SAAS;gBACf,mBAAmB,SAAS;YAC9B,OAAO;gBACL,mBAAmB;gBACnB,MAAM,SAAS;gBACf,MAAM,OAAO,OAAO,IAAI,CAAC;gBACzB,aAAa,SAAS,KAAK,MAAM;gBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;oBACpC,MAAM,MAAM,IAAI,CAAC,EAAE;oBACnB,eAAe,SAAS;oBACxB,SAAS,SAAS,IAAI,CAAC,IAAI;gBAC7B;YACF;YACA;QACF,KAAK;YACH,qCAAqC;YACrC,MAAM,SAAS,OAAO,MAAM;YAC5B;QACF;YACE,sBAAsB;YACtB,MAAM,SAAS;IACnB;AACF;AAiBO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,YAAa,MAAM,CAAE;QACnB,KAAK;QACL;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG;YAChB,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;gBAClB,4DAA4D;gBAC5D,aAAa,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,gFAAgF;;YACrH;YACA,IAAI,CAAC,KAAK,GAAG;YACb,oBAAoB;YACpB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,CAAC,GAAG;QACX;IACF;AACF;AAOO,MAAM,uBAAuB;IAClC;;GAEC,GACD,YAAa,KAAK,CAAE;QAClB,KAAK;QACL;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;IACX;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,YAAY,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,GAAG;IACX;AACF;AASO,MAAM,0BAA0B;IACrC;;GAEC,GACD,YAAa,KAAK,CAAE;QAClB,KAAK;QACL;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAG,GAAG;YAClC,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;gBAClB,4DAA4D;gBAC5D,aAAa,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,gFAAgF;;YACrH;YACA,IAAI,CAAC,KAAK,GAAG;YACb,oBAAoB;YACpB,YAAY,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,GAAG;QACX;IACF;AACF;AAEA;;CAEC,GACD,MAAM,yBAAyB,CAAA;IAC7B,IAAI,QAAQ,KAAK,GAAG,GAAG;QACrB,4DAA4D;QAC5D,oDAAoD;QACpD,+FAA+F;QAC/F,YAAY,QAAQ,OAAO,EAAE,QAAQ,KAAK,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;QACzE,IAAI,QAAQ,KAAK,GAAG,GAAG;YACrB,aAAa,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,GAAG,gFAAgF;;QACnI;IACF;AACF;AAUO,MAAM;IACX,aAAe;QACb,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG;YAChB,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,uBAAuB,IAAI;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,CAAC,GAAG;QACX;IACF;IAEA;;;;GAIC,GACD,eAAgB;QACd,uBAAuB,IAAI;QAC3B,OAAO,aAAa,IAAI,CAAC,OAAO;IAClC;AACF;AAUO,MAAM;IACX,aAAe;QACb,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG;YAC7B,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,uBAAuB,IAAI;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,CAAC,GAAG;QACX;IACF;IAEA;;;;GAIC,GACD,eAAgB;QACd,uBAAuB,IAAI;QAC3B,OAAO,aAAa,IAAI,CAAC,OAAO;IAClC;AACF;AAEA;;CAEC,GACD,MAAM,4BAA4B,CAAA;IAChC,IAAI,QAAQ,KAAK,GAAG,GAAG;QACrB,mEAAmE;QACnE,wEAAwE;QACxE,MAAM,cAAc,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC;QACnE,4DAA4D;QAC5D,yDAAyD;QACzD,oGAAoG;QACpG,YAAY,QAAQ,OAAO,EAAE;QAC7B,IAAI,QAAQ,KAAK,GAAG,GAAG;YACrB,aAAa,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,GAAG,gFAAgF;;QACnI;IACF;AACF;AAmBO,MAAM;IACX,aAAe;QACb,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IACd;IAEA;;GAEC,GACD,MAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE;YAC5B,IAAI,CAAC,CAAC,GAAG;YACT,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,0BAA0B,IAAI;YAC9B,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,CAAC,GAAG;QACX;IACF;IAEA;;;;GAIC,GACD,eAAgB;QACd,0BAA0B,IAAI;QAC9B,OAAO,aAAa,IAAI,CAAC,OAAO;IAClC;AACF;AAYO,MAAM;IACX,aAAe;QACb;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEA;;GAEC,GACD,MAAO,MAAM,EAAE;QACb,IAAI,CAAC,CAAC,IAAI;QACV,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrB,IAAI,CAAC,CAAC,GAAG;QACX;QACA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,MAAM;IAChC;IAEA,eAAgB;QACd,MAAM,UAAU,IAAI;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,CAAC,GAAG;QACT,eAAe,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACvC,gBAAgB,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY;QAChD,OAAO,aAAa;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/error.js"], "sourcesContent": ["/**\n * Error helpers.\n *\n * @module error\n */\n\n/**\n * @param {string} s\n * @return {Error}\n */\n/* c8 ignore next */\nexport const create = s => new Error(s)\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nexport const methodUnimplemented = () => {\n  throw create('Method unimplemented')\n}\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nexport const unexpectedCase = () => {\n  throw create('Unexpected case')\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;CAGC,GACD,kBAAkB;;;;;AACX,MAAM,SAAS,CAAA,IAAK,IAAI,MAAM;AAO9B,MAAM,sBAAsB;IACjC,MAAM,OAAO;AACf;AAOO,MAAM,iBAAiB;IAC5B,MAAM,OAAO;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/decoding.js"], "sourcesContent": ["/**\n * Efficient schema-less binary decoding with support for variable length encoding.\n *\n * Use [lib0/decoding] with [lib0/encoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module decoding\n */\n\nimport * as binary from './binary.js'\nimport * as math from './math.js'\nimport * as number from './number.js'\nimport * as string from './string.js'\nimport * as error from './error.js'\nimport * as encoding from './encoding.js'\n\nconst errorUnexpectedEndOfArray = error.create('Unexpected end of array')\nconst errorIntegerOutOfRange = error.create('Integer out of Range')\n\n/**\n * A Decoder handles the decoding of an Uint8Array.\n */\nexport class Decoder {\n  /**\n   * @param {Uint8Array} uint8Array Binary data to decode\n   */\n  constructor (uint8Array) {\n    /**\n     * Decoding target.\n     *\n     * @type {Uint8Array}\n     */\n    this.arr = uint8Array\n    /**\n     * Current decoding position.\n     *\n     * @type {number}\n     */\n    this.pos = 0\n  }\n}\n\n/**\n * @function\n * @param {Uint8Array} uint8Array\n * @return {Decoder}\n */\nexport const createDecoder = uint8Array => new Decoder(uint8Array)\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {boolean}\n */\nexport const hasContent = decoder => decoder.pos !== decoder.arr.length\n\n/**\n * Clone a decoder instance.\n * Optionally set a new position parameter.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} [newPos] Defaults to current position\n * @return {Decoder} A clone of `decoder`\n */\nexport const clone = (decoder, newPos = decoder.pos) => {\n  const _decoder = createDecoder(decoder.arr)\n  _decoder.pos = newPos\n  return _decoder\n}\n\n/**\n * Create an Uint8Array view of the next `len` bytes and advance the position by `len`.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} len The length of bytes to read\n * @return {Uint8Array}\n */\nexport const readUint8Array = (decoder, len) => {\n  const view = new Uint8Array(decoder.arr.buffer, decoder.pos + decoder.arr.byteOffset, len)\n  decoder.pos += len\n  return view\n}\n\n/**\n * Read variable length Uint8Array.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nexport const readVarUint8Array = decoder => readUint8Array(decoder, readVarUint(decoder))\n\n/**\n * Read the rest of the content as an ArrayBuffer\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nexport const readTailAsUint8Array = decoder => readUint8Array(decoder, decoder.arr.length - decoder.pos)\n\n/**\n * Skip one byte, jump to the next position.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} The next position\n */\nexport const skip8 = decoder => decoder.pos++\n\n/**\n * Read one byte as unsigned integer.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} Unsigned 8-bit integer\n */\nexport const readUint8 = decoder => decoder.arr[decoder.pos++]\n\n/**\n * Read 2 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const readUint16 = decoder => {\n  const uint =\n    decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8)\n  decoder.pos += 2\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const readUint32 = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8) +\n    (decoder.arr[decoder.pos + 2] << 16) +\n    (decoder.arr[decoder.pos + 3] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const readUint32BigEndian = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos + 3] +\n    (decoder.arr[decoder.pos + 2] << 8) +\n    (decoder.arr[decoder.pos + 1] << 16) +\n    (decoder.arr[decoder.pos] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const peekUint8 = decoder => decoder.arr[decoder.pos]\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const peekUint16 = decoder =>\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8)\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const peekUint32 = decoder => (\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8) +\n  (decoder.arr[decoder.pos + 2] << 16) +\n  (decoder.arr[decoder.pos + 3] << 24)\n) >>> 0\n\n/**\n * Read unsigned integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nexport const readVarUint = decoder => {\n  let num = 0\n  let mult = 1\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    const r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & binary.BITS7) * mult // shift $r << (7*#iterations) and add it to num\n    mult *= 128 // next iteration, shift 7 \"more\" to the left\n    if (r < binary.BIT8) {\n      return num\n    }\n    /* c8 ignore start */\n    if (num > number.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Read signed integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n * @todo This should probably create the inverse ~num if number is negative - but this would be a breaking change.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nexport const readVarInt = decoder => {\n  let r = decoder.arr[decoder.pos++]\n  let num = r & binary.BITS6\n  let mult = 64\n  const sign = (r & binary.BIT7) > 0 ? -1 : 1\n  if ((r & binary.BIT8) === 0) {\n    // don't continue reading\n    return sign * num\n  }\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & binary.BITS7) * mult\n    mult *= 128\n    if (r < binary.BIT8) {\n      return sign * num\n    }\n    /* c8 ignore start */\n    if (num > number.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nexport const peekVarUint = decoder => {\n  const pos = decoder.pos\n  const s = readVarUint(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nexport const peekVarInt = decoder => {\n  const pos = decoder.pos\n  const s = readVarInt(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * We don't test this function anymore as we use native decoding/encoding by default now.\n * Better not modify this anymore..\n *\n * Transforming utf8 to a string is pretty expensive. The code performs 10x better\n * when String.fromCodePoint is fed with all characters as arguments.\n * But most environments have a maximum number of arguments per functions.\n * For effiency reasons we apply a maximum of 10000 characters at once.\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String.\n */\n/* c8 ignore start */\nexport const _readVarStringPolyfill = decoder => {\n  let remainingLen = readVarUint(decoder)\n  if (remainingLen === 0) {\n    return ''\n  } else {\n    let encodedString = String.fromCodePoint(readUint8(decoder)) // remember to decrease remainingLen\n    if (--remainingLen < 100) { // do not create a Uint8Array for small strings\n      while (remainingLen--) {\n        encodedString += String.fromCodePoint(readUint8(decoder))\n      }\n    } else {\n      while (remainingLen > 0) {\n        const nextLen = remainingLen < 10000 ? remainingLen : 10000\n        // this is dangerous, we create a fresh array view from the existing buffer\n        const bytes = decoder.arr.subarray(decoder.pos, decoder.pos + nextLen)\n        decoder.pos += nextLen\n        // Starting with ES5.1 we can supply a generic array-like object as arguments\n        encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n        remainingLen -= nextLen\n      }\n    }\n    return decodeURIComponent(escape(encodedString))\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n */\nexport const _readVarStringNative = decoder =>\n  /** @type any */ (string.utf8TextDecoder).decode(readVarUint8Array(decoder))\n\n/**\n * Read string of variable length\n * * varUint is used to store the length of the string\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n *\n */\n/* c8 ignore next */\nexport const readVarString = string.utf8TextDecoder ? _readVarStringNative : _readVarStringPolyfill\n\n/**\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nexport const readTerminatedUint8Array = decoder => {\n  const encoder = encoding.createEncoder()\n  let b\n  while (true) {\n    b = readUint8(decoder)\n    if (b === 0) {\n      return encoding.toUint8Array(encoder)\n    }\n    if (b === 1) {\n      b = readUint8(decoder)\n    }\n    encoding.write(encoder, b)\n  }\n}\n\n/**\n * @param {Decoder} decoder\n * @return {string}\n */\nexport const readTerminatedString = decoder => string.decodeUtf8(readTerminatedUint8Array(decoder))\n\n/**\n * Look ahead and read varString without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {string}\n */\nexport const peekVarString = decoder => {\n  const pos = decoder.pos\n  const s = readVarString(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * @param {Decoder} decoder\n * @param {number} len\n * @return {DataView}\n */\nexport const readFromDataView = (decoder, len) => {\n  const dv = new DataView(decoder.arr.buffer, decoder.arr.byteOffset + decoder.pos, len)\n  decoder.pos += len\n  return dv\n}\n\n/**\n * @param {Decoder} decoder\n */\nexport const readFloat32 = decoder => readFromDataView(decoder, 4).getFloat32(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nexport const readFloat64 = decoder => readFromDataView(decoder, 8).getFloat64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nexport const readBigInt64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigInt64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nexport const readBigUint64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigUint64(0, false)\n\n/**\n * @type {Array<function(Decoder):any>}\n */\nconst readAnyLookupTable = [\n  decoder => undefined, // CASE 127: undefined\n  decoder => null, // CASE 126: null\n  readVarInt, // CASE 125: integer\n  readFloat32, // CASE 124: float32\n  readFloat64, // CASE 123: float64\n  readBigInt64, // CASE 122: bigint\n  decoder => false, // CASE 121: boolean (false)\n  decoder => true, // CASE 120: boolean (true)\n  readVarString, // CASE 119: string\n  decoder => { // CASE 118: object<string,any>\n    const len = readVarUint(decoder)\n    /**\n     * @type {Object<string,any>}\n     */\n    const obj = {}\n    for (let i = 0; i < len; i++) {\n      const key = readVarString(decoder)\n      obj[key] = readAny(decoder)\n    }\n    return obj\n  },\n  decoder => { // CASE 117: array<any>\n    const len = readVarUint(decoder)\n    const arr = []\n    for (let i = 0; i < len; i++) {\n      arr.push(readAny(decoder))\n    }\n    return arr\n  },\n  readVarUint8Array // CASE 116: Uint8Array\n]\n\n/**\n * @param {Decoder} decoder\n */\nexport const readAny = decoder => readAnyLookupTable[127 - readUint8(decoder)](decoder)\n\n/**\n * T must not be null.\n *\n * @template T\n */\nexport class RleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {function(Decoder):T} reader\n   */\n  constructor (uint8Array, reader) {\n    super(uint8Array)\n    /**\n     * The reader\n     */\n    this.reader = reader\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = this.reader(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {T} */ (this.s)\n  }\n}\n\nexport class IntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    this.s += readVarInt(this)\n    return this.s\n  }\n}\n\nexport class RleIntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      this.s += readVarInt(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nexport class UintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = math.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nexport class IncUintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = math.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s++)\n  }\n}\n\nexport class IntDiffOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      const diff = readVarInt(this)\n      // if the first bit is set, we read more data\n      const hasCount = diff & 1\n      this.diff = math.floor(diff / 2) // shift >> 1\n      this.count = 1\n      if (hasCount) {\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.s += this.diff\n    this.count--\n    return this.s\n  }\n}\n\nexport class StringDecoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    this.decoder = new UintOptRleDecoder(uint8Array)\n    this.str = readVarString(this.decoder)\n    /**\n     * @type {number}\n     */\n    this.spos = 0\n  }\n\n  /**\n   * @return {string}\n   */\n  read () {\n    const end = this.spos + this.decoder.read()\n    const res = this.str.slice(this.spos, end)\n    this.spos = end\n    return res\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,4BAA4B,CAAA,GAAA,mLAAA,CAAA,SAAY,AAAD,EAAE;AAC/C,MAAM,yBAAyB,CAAA,GAAA,mLAAA,CAAA,SAAY,AAAD,EAAE;AAKrC,MAAM;IACX;;GAEC,GACD,YAAa,UAAU,CAAE;QACvB;;;;KAIC,GACD,IAAI,CAAC,GAAG,GAAG;QACX;;;;KAIC,GACD,IAAI,CAAC,GAAG,GAAG;IACb;AACF;AAOO,MAAM,gBAAgB,CAAA,aAAc,IAAI,QAAQ;AAOhD,MAAM,aAAa,CAAA,UAAW,QAAQ,GAAG,KAAK,QAAQ,GAAG,CAAC,MAAM;AAWhE,MAAM,QAAQ,CAAC,SAAS,SAAS,QAAQ,GAAG;IACjD,MAAM,WAAW,cAAc,QAAQ,GAAG;IAC1C,SAAS,GAAG,GAAG;IACf,OAAO;AACT;AAaO,MAAM,iBAAiB,CAAC,SAAS;IACtC,MAAM,OAAO,IAAI,WAAW,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,UAAU,EAAE;IACtF,QAAQ,GAAG,IAAI;IACf,OAAO;AACT;AAYO,MAAM,oBAAoB,CAAA,UAAW,eAAe,SAAS,YAAY;AAQzE,MAAM,uBAAuB,CAAA,UAAW,eAAe,SAAS,QAAQ,GAAG,CAAC,MAAM,GAAG,QAAQ,GAAG;AAQhG,MAAM,QAAQ,CAAA,UAAW,QAAQ,GAAG;AAQpC,MAAM,YAAY,CAAA,UAAW,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG;AASvD,MAAM,aAAa,CAAA;IACxB,MAAM,OACJ,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,GACxB,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC;IACpC,QAAQ,GAAG,IAAI;IACf,OAAO;AACT;AASO,MAAM,aAAa,CAAA;IACxB,MAAM,OACJ,AAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,GACzB,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC,IAClC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,IACnC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,MAAO;IAC5C,QAAQ,GAAG,IAAI;IACf,OAAO;AACT;AAUO,MAAM,sBAAsB,CAAA;IACjC,MAAM,OACJ,AAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,GAC7B,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC,IAClC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,IACnC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAO;IACxC,QAAQ,GAAG,IAAI;IACf,OAAO;AACT;AAUO,MAAM,YAAY,CAAA,UAAW,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC;AAUrD,MAAM,aAAa,CAAA,UACxB,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,GACxB,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC;AAU7B,MAAM,aAAa,CAAA,UAAW,AACnC,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,GACxB,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC,IAClC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,IACnC,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,MAC/B;AAYC,MAAM,cAAc,CAAA;IACzB,IAAI,MAAM;IACV,IAAI,OAAO;IACX,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM;IAC9B,MAAO,QAAQ,GAAG,GAAG,IAAK;QACxB,MAAM,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG;QACpC,0CAA0C;QAC1C,MAAM,MAAM,CAAC,IAAI,oLAAA,CAAA,QAAY,IAAI,KAAK,gDAAgD;;QACtF,QAAQ,IAAI,6CAA6C;;QACzD,IAAI,IAAI,oLAAA,CAAA,OAAW,EAAE;YACnB,OAAO;QACT;QACA,mBAAmB,GACnB,IAAI,MAAM,oLAAA,CAAA,mBAAuB,EAAE;YACjC,MAAM;QACR;IACA,kBAAkB,GACpB;IACA,MAAM;AACR;AAaO,MAAM,aAAa,CAAA;IACxB,IAAI,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG;IAClC,IAAI,MAAM,IAAI,oLAAA,CAAA,QAAY;IAC1B,IAAI,OAAO;IACX,MAAM,OAAO,CAAC,IAAI,oLAAA,CAAA,OAAW,IAAI,IAAI,CAAC,IAAI;IAC1C,IAAI,CAAC,IAAI,oLAAA,CAAA,OAAW,MAAM,GAAG;QAC3B,yBAAyB;QACzB,OAAO,OAAO;IAChB;IACA,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM;IAC9B,MAAO,QAAQ,GAAG,GAAG,IAAK;QACxB,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG;QAC9B,0CAA0C;QAC1C,MAAM,MAAM,CAAC,IAAI,oLAAA,CAAA,QAAY,IAAI;QACjC,QAAQ;QACR,IAAI,IAAI,oLAAA,CAAA,OAAW,EAAE;YACnB,OAAO,OAAO;QAChB;QACA,mBAAmB,GACnB,IAAI,MAAM,oLAAA,CAAA,mBAAuB,EAAE;YACjC,MAAM;QACR;IACA,kBAAkB,GACpB;IACA,MAAM;AACR;AASO,MAAM,cAAc,CAAA;IACzB,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,IAAI,YAAY;IACtB,QAAQ,GAAG,GAAG;IACd,OAAO;AACT;AASO,MAAM,aAAa,CAAA;IACxB,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,IAAI,WAAW;IACrB,QAAQ,GAAG,GAAG;IACd,OAAO;AACT;AAgBO,MAAM,yBAAyB,CAAA;IACpC,IAAI,eAAe,YAAY;IAC/B,IAAI,iBAAiB,GAAG;QACtB,OAAO;IACT,OAAO;QACL,IAAI,gBAAgB,OAAO,aAAa,CAAC,UAAU,UAAU,oCAAoC;;QACjG,IAAI,EAAE,eAAe,KAAK;YACxB,MAAO,eAAgB;gBACrB,iBAAiB,OAAO,aAAa,CAAC,UAAU;YAClD;QACF,OAAO;YACL,MAAO,eAAe,EAAG;gBACvB,MAAM,UAAU,eAAe,QAAQ,eAAe;gBACtD,2EAA2E;gBAC3E,MAAM,QAAQ,QAAQ,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,EAAE,QAAQ,GAAG,GAAG;gBAC9D,QAAQ,GAAG,IAAI;gBACf,6EAA6E;gBAC7E,iBAAiB,OAAO,aAAa,CAAC,KAAK,CAAC,MAA0B;gBACtE,gBAAgB;YAClB;QACF;QACA,OAAO,mBAAmB,OAAO;IACnC;AACF;AAQO,MAAM,uBAAuB,CAAA,UAClC,cAAc,GAAG,AAAC,oLAAA,CAAA,kBAAsB,CAAE,MAAM,CAAC,kBAAkB;AAY9D,MAAM,gBAAgB,oLAAA,CAAA,kBAAsB,GAAG,uBAAuB;AAMtE,MAAM,2BAA2B,CAAA;IACtC,MAAM,UAAU,CAAA,GAAA,sLAAA,CAAA,gBAAsB,AAAD;IACrC,IAAI;IACJ,MAAO,KAAM;QACX,IAAI,UAAU;QACd,IAAI,MAAM,GAAG;YACX,OAAO,CAAA,GAAA,sLAAA,CAAA,eAAqB,AAAD,EAAE;QAC/B;QACA,IAAI,MAAM,GAAG;YACX,IAAI,UAAU;QAChB;QACA,CAAA,GAAA,sLAAA,CAAA,QAAc,AAAD,EAAE,SAAS;IAC1B;AACF;AAMO,MAAM,uBAAuB,CAAA,UAAW,CAAA,GAAA,oLAAA,CAAA,aAAiB,AAAD,EAAE,yBAAyB;AASnF,MAAM,gBAAgB,CAAA;IAC3B,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,IAAI,cAAc;IACxB,QAAQ,GAAG,GAAG;IACd,OAAO;AACT;AAOO,MAAM,mBAAmB,CAAC,SAAS;IACxC,MAAM,KAAK,IAAI,SAAS,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG,EAAE;IAClF,QAAQ,GAAG,IAAI;IACf,OAAO;AACT;AAKO,MAAM,cAAc,CAAA,UAAW,iBAAiB,SAAS,GAAG,UAAU,CAAC,GAAG;AAK1E,MAAM,cAAc,CAAA,UAAW,iBAAiB,SAAS,GAAG,UAAU,CAAC,GAAG;AAK1E,MAAM,eAAe,CAAA,UAAW,gBAAgB,GAAG,AAAC,iBAAiB,SAAS,GAAI,WAAW,CAAC,GAAG;AAKjG,MAAM,gBAAgB,CAAA,UAAW,gBAAgB,GAAG,AAAC,iBAAiB,SAAS,GAAI,YAAY,CAAC,GAAG;AAE1G;;CAEC,GACD,MAAM,qBAAqB;IACzB,CAAA,UAAW;IACX,CAAA,UAAW;IACX;IACA;IACA;IACA;IACA,CAAA,UAAW;IACX,CAAA,UAAW;IACX;IACA,CAAA;QACE,MAAM,MAAM,YAAY;QACxB;;KAEC,GACD,MAAM,MAAM,CAAC;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,MAAM,cAAc;YAC1B,GAAG,CAAC,IAAI,GAAG,QAAQ;QACrB;QACA,OAAO;IACT;IACA,CAAA;QACE,MAAM,MAAM,YAAY;QACxB,MAAM,MAAM,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAI,IAAI,CAAC,QAAQ;QACnB;QACA,OAAO;IACT;IACA,kBAAkB,uBAAuB;CAC1C;AAKM,MAAM,UAAU,CAAA,UAAW,kBAAkB,CAAC,MAAM,UAAU,SAAS,CAAC;AAOxE,MAAM,mBAAmB;IAC9B;;;GAGC,GACD,YAAa,UAAU,EAAE,MAAM,CAAE;QAC/B,KAAK,CAAC;QACN;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QACd;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAQ;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;YACzB,IAAI,WAAW,IAAI,GAAG;gBACpB,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI,EAAE,oEAAoE;;YACzG,OAAO;gBACL,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,iCAAiC;;YACnD;QACF;QACA,IAAI,CAAC,KAAK;QACV,OAAyB,IAAI,CAAC,CAAC;IACjC;AACF;AAEO,MAAM,uBAAuB;IAClC;;;GAGC,GACD,YAAa,UAAU,EAAE,KAAK,CAAE;QAC9B,KAAK,CAAC;QACN;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;IACX;IAEA;;GAEC,GACD,OAAQ;QACN,IAAI,CAAC,CAAC,IAAI,WAAW,IAAI;QACzB,OAAO,IAAI,CAAC,CAAC;IACf;AACF;AAEO,MAAM,0BAA0B;IACrC;;;GAGC,GACD,YAAa,UAAU,EAAE,KAAK,CAAE;QAC9B,KAAK,CAAC;QACN;;;KAGC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;GAEC,GACD,OAAQ;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,IAAI,CAAC,CAAC,IAAI,WAAW,IAAI;YACzB,IAAI,WAAW,IAAI,GAAG;gBACpB,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI,EAAE,oEAAoE;;YACzG,OAAO;gBACL,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,iCAAiC;;YACnD;QACF;QACA,IAAI,CAAC,KAAK;QACV,OAA8B,IAAI,CAAC,CAAC;IACtC;AACF;AAEO,MAAM,0BAA0B;IACrC;;GAEC,GACD,YAAa,UAAU,CAAE;QACvB,KAAK,CAAC;QACN;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAQ;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,IAAI,CAAC,CAAC,GAAG,WAAW,IAAI;YACxB,uEAAuE;YACvE,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,iBAAmB,AAAD,EAAE,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,YAAY;gBACd,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChB,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI;YACnC;QACF;QACA,IAAI,CAAC,KAAK;QACV,OAA8B,IAAI,CAAC,CAAC;IACtC;AACF;AAEO,MAAM,6BAA6B;IACxC;;GAEC,GACD,YAAa,UAAU,CAAE;QACvB,KAAK,CAAC;QACN;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAQ;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,IAAI,CAAC,CAAC,GAAG,WAAW,IAAI;YACxB,uEAAuE;YACvE,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,iBAAmB,AAAD,EAAE,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,YAAY;gBACd,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChB,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI;YACnC;QACF;QACA,IAAI,CAAC,KAAK;QACV,OAA8B,IAAI,CAAC,CAAC;IACtC;AACF;AAEO,MAAM,6BAA6B;IACxC;;GAEC,GACD,YAAa,UAAU,CAAE;QACvB,KAAK,CAAC;QACN;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IACd;IAEA;;GAEC,GACD,OAAQ;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,MAAM,OAAO,WAAW,IAAI;YAC5B,6CAA6C;YAC7C,MAAM,WAAW,OAAO;YACxB,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,OAAO,GAAG,aAAa;;YAC9C,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,UAAU;gBACZ,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI;YACnC;QACF;QACA,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI;QACnB,IAAI,CAAC,KAAK;QACV,OAAO,IAAI,CAAC,CAAC;IACf;AACF;AAEO,MAAM;IACX;;GAEC,GACD,YAAa,UAAU,CAAE;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAkB;QACrC,IAAI,CAAC,GAAG,GAAG,cAAc,IAAI,CAAC,OAAO;QACrC;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG;IACd;IAEA;;GAEC,GACD,OAAQ;QACN,MAAM,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;QACzC,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;QACtC,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/webcrypto.js"], "sourcesContent": ["/* eslint-env browser */\n\nexport const subtle = crypto.subtle\nexport const getRandomValues = crypto.getRandomValues.bind(crypto)\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AAEf,MAAM,SAAS,OAAO,MAAM;AAC5B,MAAM,kBAAkB,OAAO,eAAe,CAAC,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/random.js"], "sourcesContent": ["/**\n * Isomorphic module for true random numbers / buffers / uuids.\n *\n * Attention: falls back to Math.random if the browser does not support crypto.\n *\n * @module random\n */\n\nimport * as math from './math.js'\nimport * as binary from './binary.js'\nimport { getRandomValues } from 'lib0/webcrypto'\n\nexport const rand = Math.random\n\nexport const uint32 = () => getRandomValues(new Uint32Array(1))[0]\n\nexport const uint53 = () => {\n  const arr = getRandomValues(new Uint32Array(8))\n  return (arr[0] & binary.BITS21) * (binary.BITS32 + 1) + (arr[1] >>> 0)\n}\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {T}\n */\nexport const oneOf = arr => arr[math.floor(rand() * arr.length)]\n\n// @ts-ignore\nconst uuidv4Template = [1e7] + -1e3 + -4e3 + -8e3 + -1e11\n\n/**\n * @return {string}\n */\nexport const uuidv4 = () => uuidv4Template.replace(/[018]/g, /** @param {number} c */ c =>\n  (c ^ uint32() & 15 >> c / 4).toString(16)\n)\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;AAED;AACA;AACA;;;;AAEO,MAAM,OAAO,KAAK,MAAM;AAExB,MAAM,SAAS,IAAM,CAAA,GAAA,uLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,YAAY,GAAG,CAAC,EAAE;AAE3D,MAAM,SAAS;IACpB,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,YAAY;IAC5C,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,oLAAA,CAAA,SAAa,IAAI,CAAC,oLAAA,CAAA,SAAa,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC;AACvE;AAOO,MAAM,QAAQ,CAAA,MAAO,GAAG,CAAC,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,SAAS,IAAI,MAAM,EAAE;AAEhE,aAAa;AACb,MAAM,iBAAiB;IAAC;CAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAK9C,MAAM,SAAS,IAAM,eAAe,OAAO,CAAC,UAAU,sBAAsB,GAAG,CAAA,IACpF,CAAC,IAAI,WAAW,MAAM,IAAI,CAAC,EAAE,QAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/metric.js"], "sourcesContent": ["/**\n * Utility module to convert metric values.\n *\n * @module metric\n */\n\nimport * as math from './math.js'\n\nexport const yotta = 1e24\nexport const zetta = 1e21\nexport const exa = 1e18\nexport const peta = 1e15\nexport const tera = 1e12\nexport const giga = 1e9\nexport const mega = 1e6\nexport const kilo = 1e3\nexport const hecto = 1e2\nexport const deca = 10\nexport const deci = 0.1\nexport const centi = 0.01\nexport const milli = 1e-3\nexport const micro = 1e-6\nexport const nano = 1e-9\nexport const pico = 1e-12\nexport const femto = 1e-15\nexport const atto = 1e-18\nexport const zepto = 1e-21\nexport const yocto = 1e-24\n\nconst prefixUp = ['', 'k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y']\nconst prefixDown = ['', 'm', 'μ', 'n', 'p', 'f', 'a', 'z', 'y']\n\n/**\n * Calculate the metric prefix for a number. Assumes E.g. `prefix(1000) = { n: 1, prefix: 'k' }`\n *\n * @param {number} n\n * @param {number} [baseMultiplier] Multiplier of the base (10^(3*baseMultiplier)). E.g. `convert(time, -3)` if time is already in milli seconds\n * @return {{n:number,prefix:string}}\n */\nexport const prefix = (n, baseMultiplier = 0) => {\n  const nPow = n === 0 ? 0 : math.log10(n)\n  let mult = 0\n  while (nPow < mult * 3 && baseMultiplier > -8) {\n    baseMultiplier--\n    mult--\n  }\n  while (nPow >= 3 + mult * 3 && baseMultiplier < 8) {\n    baseMultiplier++\n    mult++\n  }\n  const prefix = baseMultiplier < 0 ? prefixDown[-baseMultiplier] : prefixUp[baseMultiplier]\n  return {\n    n: math.round((mult > 0 ? n / math.exp10(mult * 3) : n * math.exp10(mult * -3)) * 1e12) / 1e12,\n    prefix\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAEO,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,QAAQ;AAErB,MAAM,WAAW;IAAC;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAC7D,MAAM,aAAa;IAAC;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AASxD,MAAM,SAAS,CAAC,GAAG,iBAAiB,CAAC;IAC1C,MAAM,OAAO,MAAM,IAAI,IAAI,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE;IACtC,IAAI,OAAO;IACX,MAAO,OAAO,OAAO,KAAK,iBAAiB,CAAC,EAAG;QAC7C;QACA;IACF;IACA,MAAO,QAAQ,IAAI,OAAO,KAAK,iBAAiB,EAAG;QACjD;QACA;IACF;IACA,MAAM,SAAS,iBAAiB,IAAI,UAAU,CAAC,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe;IAC1F,OAAO;QACL,GAAG,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,CAAC,OAAO,IAAI,IAAI,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,OAAO,KAAK,IAAI,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,OAAO,CAAC,EAAE,IAAI,QAAQ;QAC1F;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/time.js"], "sourcesContent": ["/**\n * Utility module to work with time.\n *\n * @module time\n */\n\nimport * as metric from './metric.js'\nimport * as math from './math.js'\n\n/**\n * Return current time.\n *\n * @return {Date}\n */\nexport const getDate = () => new Date()\n\n/**\n * Return current unix time.\n *\n * @return {number}\n */\nexport const getUnixTime = Date.now\n\n/**\n * Transform time (in ms) to a human readable format. E.g. 1100 => 1.1s. 60s => 1min. .001 => 10μs.\n *\n * @param {number} d duration in milliseconds\n * @return {string} humanized approximation of time\n */\nexport const humanizeDuration = d => {\n  if (d < 60000) {\n    const p = metric.prefix(d, -1)\n    return math.round(p.n * 100) / 100 + p.prefix + 's'\n  }\n  d = math.floor(d / 1000)\n  const seconds = d % 60\n  const minutes = math.floor(d / 60) % 60\n  const hours = math.floor(d / 3600) % 24\n  const days = math.floor(d / 86400)\n  if (days > 0) {\n    return days + 'd' + ((hours > 0 || minutes > 30) ? ' ' + (minutes > 30 ? hours + 1 : hours) + 'h' : '')\n  }\n  if (hours > 0) {\n    /* c8 ignore next */\n    return hours + 'h' + ((minutes > 0 || seconds > 30) ? ' ' + (seconds > 30 ? minutes + 1 : minutes) + 'min' : '')\n  }\n  return minutes + 'min' + (seconds > 0 ? ' ' + seconds + 's' : '')\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAED;AACA;;;AAOO,MAAM,UAAU,IAAM,IAAI;AAO1B,MAAM,cAAc,KAAK,GAAG;AAQ5B,MAAM,mBAAmB,CAAA;IAC9B,IAAI,IAAI,OAAO;QACb,MAAM,IAAI,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD,EAAE,GAAG,CAAC;QAC5B,OAAO,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,EAAE,CAAC,GAAG,OAAO,MAAM,EAAE,MAAM,GAAG;IAClD;IACA,IAAI,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,IAAI;IACnB,MAAM,UAAU,IAAI;IACpB,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,IAAI,MAAM;IACrC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,IAAI,QAAQ;IACrC,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,IAAI;IAC5B,IAAI,OAAO,GAAG;QACZ,OAAO,OAAO,MAAM,CAAC,AAAC,QAAQ,KAAK,UAAU,KAAM,MAAM,CAAC,UAAU,KAAK,QAAQ,IAAI,KAAK,IAAI,MAAM,EAAE;IACxG;IACA,IAAI,QAAQ,GAAG;QACb,kBAAkB,GAClB,OAAO,QAAQ,MAAM,CAAC,AAAC,UAAU,KAAK,UAAU,KAAM,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI,OAAO,IAAI,QAAQ,EAAE;IACjH;IACA,OAAO,UAAU,QAAQ,CAAC,UAAU,IAAI,MAAM,UAAU,MAAM,EAAE;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/promise.js"], "sourcesContent": ["/**\n * Utility helpers to work with promises.\n *\n * @module promise\n */\n\nimport * as time from './time.js'\n\n/**\n * @template T\n * @callback PromiseResolve\n * @param {T|PromiseLike<T>} [result]\n */\n\n/**\n * @template T\n * @param {function(PromiseResolve<T>,function(Error):void):any} f\n * @return {Promise<T>}\n */\nexport const create = f => /** @type {Promise<T>} */ (new Promise(f))\n\n/**\n * @param {function(function():void,function(Error):void):void} f\n * @return {Promise<void>}\n */\nexport const createEmpty = f => new Promise(f)\n\n/**\n * `Promise.all` wait for all promises in the array to resolve and return the result\n * @template {unknown[] | []} PS\n *\n * @param {PS} ps\n * @return {Promise<{ -readonly [P in keyof PS]: Awaited<PS[P]> }>}\n */\nexport const all = Promise.all.bind(Promise)\n\n/**\n * @param {Error} [reason]\n * @return {Promise<never>}\n */\nexport const reject = reason => Promise.reject(reason)\n\n/**\n * @template T\n * @param {T|void} res\n * @return {Promise<T|void>}\n */\nexport const resolve = res => Promise.resolve(res)\n\n/**\n * @template T\n * @param {T} res\n * @return {Promise<T>}\n */\nexport const resolveWith = res => Promise.resolve(res)\n\n/**\n * @todo Next version, reorder parameters: check, [timeout, [intervalResolution]]\n * @deprecated use untilAsync instead\n *\n * @param {number} timeout\n * @param {function():boolean} check\n * @param {number} [intervalResolution]\n * @return {Promise<void>}\n */\nexport const until = (timeout, check, intervalResolution = 10) => create((resolve, reject) => {\n  const startTime = time.getUnixTime()\n  const hasTimeout = timeout > 0\n  const untilInterval = () => {\n    if (check()) {\n      clearInterval(intervalHandle)\n      resolve()\n    } else if (hasTimeout) {\n      /* c8 ignore else */\n      if (time.getUnixTime() - startTime > timeout) {\n        clearInterval(intervalHandle)\n        reject(new Error('Timeout'))\n      }\n    }\n  }\n  const intervalHandle = setInterval(untilInterval, intervalResolution)\n})\n\n/**\n * @param {()=>Promise<boolean>|boolean} check\n * @param {number} timeout\n * @param {number} intervalResolution\n * @return {Promise<void>}\n */\nexport const untilAsync = async (check, timeout = 0, intervalResolution = 10) => {\n  const startTime = time.getUnixTime()\n  const noTimeout = timeout <= 0\n  // eslint-disable-next-line no-unmodified-loop-condition\n  while (noTimeout || time.getUnixTime() - startTime <= timeout) {\n    if (await check()) return\n    await wait(intervalResolution)\n  }\n  throw new Error('Timeout')\n}\n\n/**\n * @param {number} timeout\n * @return {Promise<undefined>}\n */\nexport const wait = timeout => create((resolve, _reject) => setTimeout(resolve, timeout))\n\n/**\n * Checks if an object is a promise using ducktyping.\n *\n * Promises are often polyfilled, so it makes sense to add some additional guarantees if the user of this\n * library has some insane environment where global Promise objects are overwritten.\n *\n * @param {any} p\n * @return {boolean}\n */\nexport const isPromise = p => p instanceof Promise || (p && p.then && p.catch && p.finally)\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;AAED;;AAaO,MAAM,SAAS,CAAA,IAAgC,IAAI,QAAQ;AAM3D,MAAM,cAAc,CAAA,IAAK,IAAI,QAAQ;AASrC,MAAM,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC;AAM7B,MAAM,SAAS,CAAA,SAAU,QAAQ,MAAM,CAAC;AAOxC,MAAM,UAAU,CAAA,MAAO,QAAQ,OAAO,CAAC;AAOvC,MAAM,cAAc,CAAA,MAAO,QAAQ,OAAO,CAAC;AAW3C,MAAM,QAAQ,CAAC,SAAS,OAAO,qBAAqB,EAAE,GAAK,OAAO,CAAC,SAAS;QACjF,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD;QACjC,MAAM,aAAa,UAAU;QAC7B,MAAM,gBAAgB;YACpB,IAAI,SAAS;gBACX,cAAc;gBACd;YACF,OAAO,IAAI,YAAY;gBACrB,kBAAkB,GAClB,IAAI,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD,MAAM,YAAY,SAAS;oBAC5C,cAAc;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF;QACF;QACA,MAAM,iBAAiB,YAAY,eAAe;IACpD;AAQO,MAAM,aAAa,OAAO,OAAO,UAAU,CAAC,EAAE,qBAAqB,EAAE;IAC1E,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD;IACjC,MAAM,YAAY,WAAW;IAC7B,wDAAwD;IACxD,MAAO,aAAa,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD,MAAM,aAAa,QAAS;QAC7D,IAAI,MAAM,SAAS;QACnB,MAAM,KAAK;IACb;IACA,MAAM,IAAI,MAAM;AAClB;AAMO,MAAM,OAAO,CAAA,UAAW,OAAO,CAAC,SAAS,UAAY,WAAW,SAAS;AAWzE,MAAM,YAAY,CAAA,IAAK,aAAa,WAAY,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/conditions.js"], "sourcesContent": ["/**\n * Often used conditions.\n *\n * @module conditions\n */\n\n/**\n * @template T\n * @param {T|null|undefined} v\n * @return {T|null}\n */\n/* c8 ignore next */\nexport const undefinedToNull = v => v === undefined ? null : v\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;CAIC,GACD,kBAAkB;;;AACX,MAAM,kBAAkB,CAAA,IAAK,MAAM,YAAY,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/storage.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Isomorphic variable storage.\n *\n * Uses LocalStorage in the browser and falls back to in-memory storage.\n *\n * @module storage\n */\n\n/* c8 ignore start */\nclass VarStoragePolyfill {\n  constructor () {\n    this.map = new Map()\n  }\n\n  /**\n   * @param {string} key\n   * @param {any} newValue\n   */\n  setItem (key, newValue) {\n    this.map.set(key, newValue)\n  }\n\n  /**\n   * @param {string} key\n   */\n  getItem (key) {\n    return this.map.get(key)\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @type {any}\n */\nlet _localStorage = new VarStoragePolyfill()\nlet usePolyfill = true\n\n/* c8 ignore start */\ntry {\n  // if the same-origin rule is violated, accessing localStorage might thrown an error\n  if (typeof localStorage !== 'undefined' && localStorage) {\n    _localStorage = localStorage\n    usePolyfill = false\n  }\n} catch (e) { }\n/* c8 ignore stop */\n\n/**\n * This is basically localStorage in browser, or a polyfill in nodejs\n */\n/* c8 ignore next */\nexport const varStorage = _localStorage\n\n/**\n * A polyfill for `addEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nexport const onChange = eventHandler => usePolyfill || addEventListener('storage', /** @type {any} */ (eventHandler))\n\n/**\n * A polyfill for `removeEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nexport const offChange = eventHandler => usePolyfill || removeEventListener('storage', /** @type {any} */ (eventHandler))\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;;;;;CAMC,GAED,mBAAmB;;;;;AACnB,MAAM;IACJ,aAAe;QACb,IAAI,CAAC,GAAG,GAAG,IAAI;IACjB;IAEA;;;GAGC,GACD,QAAS,GAAG,EAAE,QAAQ,EAAE;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK;IACpB;IAEA;;GAEC,GACD,QAAS,GAAG,EAAE;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACtB;AACF;AACA,kBAAkB,GAElB;;CAEC,GACD,IAAI,gBAAgB,IAAI;AACxB,IAAI,cAAc;AAElB,mBAAmB,GACnB,IAAI;IACF,oFAAoF;IACpF,IAAI,OAAO,iBAAiB,eAAe,cAAc;QACvD,gBAAgB;QAChB,cAAc;IAChB;AACF,EAAE,OAAO,GAAG,CAAE;AAOP,MAAM,aAAa;AASnB,MAAM,WAAW,CAAA,eAAgB,eAAe,iBAAiB,WAA+B;AAShG,MAAM,YAAY,CAAA,eAAgB,eAAe,oBAAoB,WAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2019, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/object.js"], "sourcesContent": ["/**\n * Utility functions for working with EcmaScript objects.\n *\n * @module object\n */\n\n/**\n * @return {Object<string,any>} obj\n */\nexport const create = () => Object.create(null)\n\n/**\n * Object.assign\n */\nexport const assign = Object.assign\n\n/**\n * @param {Object<string,any>} obj\n */\nexport const keys = Object.keys\n\n/**\n * @template V\n * @param {{[key:string]: V}} obj\n * @return {Array<V>}\n */\nexport const values = Object.values\n\n/**\n * @template V\n * @param {{[k:string]:V}} obj\n * @param {function(V,string):any} f\n */\nexport const forEach = (obj, f) => {\n  for (const key in obj) {\n    f(obj[key], key)\n  }\n}\n\n/**\n * @todo implement mapToArray & map\n *\n * @template R\n * @param {Object<string,any>} obj\n * @param {function(any,string):R} f\n * @return {Array<R>}\n */\nexport const map = (obj, f) => {\n  const results = []\n  for (const key in obj) {\n    results.push(f(obj[key], key))\n  }\n  return results\n}\n\n/**\n * @deprecated use object.size instead\n * @param {Object<string,any>} obj\n * @return {number}\n */\nexport const length = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @return {number}\n */\nexport const size = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nexport const some = (obj, f) => {\n  for (const key in obj) {\n    if (f(obj[key], key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @param {Object|null|undefined} obj\n */\nexport const isEmpty = obj => {\n  // eslint-disable-next-line no-unreachable-loop\n  for (const _k in obj) {\n    return false\n  }\n  return true\n}\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nexport const every = (obj, f) => {\n  for (const key in obj) {\n    if (!f(obj[key], key)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Calls `Object.prototype.hasOwnProperty`.\n *\n * @param {any} obj\n * @param {string|symbol} key\n * @return {boolean}\n */\nexport const hasProperty = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)\n\n/**\n * @param {Object<string,any>} a\n * @param {Object<string,any>} b\n * @return {boolean}\n */\nexport const equalFlat = (a, b) => a === b || (size(a) === size(b) && every(a, (val, key) => (val !== undefined || hasProperty(b, key)) && b[key] === val))\n\n/**\n * Make an object immutable. This hurts performance and is usually not needed if you perform good\n * coding practices.\n */\nexport const freeze = Object.freeze\n\n/**\n * Make an object and all its children immutable.\n * This *really* hurts performance and is usually not needed if you perform good coding practices.\n *\n * @template {any} T\n * @param {T} o\n * @return {Readonly<T>}\n */\nexport const deepFreeze = (o) => {\n  for (const key in o) {\n    const c = o[key]\n    if (typeof c === 'object' || typeof c === 'function') {\n      deepFreeze(o[key])\n    }\n  }\n  return freeze(o)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;CAEC;;;;;;;;;;;;;;;;;AACM,MAAM,SAAS,IAAM,OAAO,MAAM,CAAC;AAKnC,MAAM,SAAS,OAAO,MAAM;AAK5B,MAAM,OAAO,OAAO,IAAI;AAOxB,MAAM,SAAS,OAAO,MAAM;AAO5B,MAAM,UAAU,CAAC,KAAK;IAC3B,IAAK,MAAM,OAAO,IAAK;QACrB,EAAE,GAAG,CAAC,IAAI,EAAE;IACd;AACF;AAUO,MAAM,MAAM,CAAC,KAAK;IACvB,MAAM,UAAU,EAAE;IAClB,IAAK,MAAM,OAAO,IAAK;QACrB,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE;IAC3B;IACA,OAAO;AACT;AAOO,MAAM,SAAS,CAAA,MAAO,KAAK,KAAK,MAAM;AAMtC,MAAM,OAAO,CAAA,MAAO,KAAK,KAAK,MAAM;AAOpC,MAAM,OAAO,CAAC,KAAK;IACxB,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM;YACpB,OAAO;QACT;IACF;IACA,OAAO;AACT;AAKO,MAAM,UAAU,CAAA;IACrB,+CAA+C;IAC/C,IAAK,MAAM,MAAM,IAAK;QACpB,OAAO;IACT;IACA,OAAO;AACT;AAOO,MAAM,QAAQ,CAAC,KAAK;IACzB,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM;YACrB,OAAO;QACT;IACF;IACA,OAAO;AACT;AASO,MAAM,cAAc,CAAC,KAAK,MAAQ,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AAO5E,MAAM,YAAY,CAAC,GAAG,IAAM,MAAM,KAAM,KAAK,OAAO,KAAK,MAAM,MAAM,GAAG,CAAC,KAAK,MAAQ,CAAC,QAAQ,aAAa,YAAY,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK;AAM/I,MAAM,SAAS,OAAO,MAAM;AAU5B,MAAM,aAAa,CAAC;IACzB,IAAK,MAAM,OAAO,EAAG;QACnB,MAAM,IAAI,CAAC,CAAC,IAAI;QAChB,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,YAAY;YACpD,WAAW,CAAC,CAAC,IAAI;QACnB;IACF;IACA,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/traits.js"], "sourcesContent": ["export const EqualityTraitSymbol = Symbol('Equality')\n\n/**\n * @typedef {{ [EqualityTraitSymbol]:(other:EqualityTrait)=>boolean }} EqualityTrait\n */\n"], "names": [], "mappings": ";;;AAAO,MAAM,sBAAsB,OAAO,YAE1C;;CAEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2113, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/function.js"], "sourcesContent": ["/**\n * Common functions and function call helpers.\n *\n * @module function\n */\n\nimport * as array from './array.js'\nimport * as object from './object.js'\nimport * as traits from './traits.js'\n\n/**\n * Calls all functions in `fs` with args. Only throws after all functions were called.\n *\n * @param {Array<function>} fs\n * @param {Array<any>} args\n */\nexport const callAll = (fs, args, i = 0) => {\n  try {\n    for (; i < fs.length; i++) {\n      fs[i](...args)\n    }\n  } finally {\n    if (i < fs.length) {\n      callAll(fs, args, i + 1)\n    }\n  }\n}\n\nexport const nop = () => {}\n\n/**\n * @template T\n * @param {function():T} f\n * @return {T}\n */\nexport const apply = f => f()\n\n/**\n * @template A\n *\n * @param {A} a\n * @return {A}\n */\nexport const id = a => a\n\n/**\n * @template T\n *\n * @param {T} a\n * @param {T} b\n * @return {boolean}\n */\nexport const equalityStrict = (a, b) => a === b\n\n/**\n * @template T\n *\n * @param {Array<T>|object} a\n * @param {Array<T>|object} b\n * @return {boolean}\n */\nexport const equalityFlat = (a, b) => a === b || (a != null && b != null && a.constructor === b.constructor && ((array.isArray(a) && array.equalFlat(a, /** @type {Array<T>} */ (b))) || (typeof a === 'object' && object.equalFlat(a, b))))\n\n/* c8 ignore start */\n\n/**\n * @param {any} a\n * @param {any} b\n * @return {boolean}\n */\nexport const equalityDeep = (a, b) => {\n  if (a === b) {\n    return true\n  }\n  if (a == null || b == null || a.constructor !== b.constructor) {\n    return false\n  }\n  if (a[traits.EqualityTraitSymbol] != null) {\n    return a[traits.EqualityTraitSymbol](b)\n  }\n  switch (a.constructor) {\n    case ArrayBuffer:\n      a = new Uint8Array(a)\n      b = new Uint8Array(b)\n    // eslint-disable-next-line no-fallthrough\n    case Uint8Array: {\n      if (a.byteLength !== b.byteLength) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n          return false\n        }\n      }\n      break\n    }\n    case Set: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const value of a) {\n        if (!b.has(value)) {\n          return false\n        }\n      }\n      break\n    }\n    case Map: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const key of a.keys()) {\n        if (!b.has(key) || !equalityDeep(a.get(key), b.get(key))) {\n          return false\n        }\n      }\n      break\n    }\n    case Object:\n      if (object.length(a) !== object.length(b)) {\n        return false\n      }\n      for (const key in a) {\n        if (!object.hasProperty(a, key) || !equalityDeep(a[key], b[key])) {\n          return false\n        }\n      }\n      break\n    case Array:\n      if (a.length !== b.length) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (!equalityDeep(a[i], b[i])) {\n          return false\n        }\n      }\n      break\n    default:\n      return false\n  }\n  return true\n}\n\n/**\n * @template V\n * @template {V} OPTS\n *\n * @param {V} value\n * @param {Array<OPTS>} options\n */\n// @ts-ignore\nexport const isOneOf = (value, options) => options.includes(value)\n/* c8 ignore stop */\n\nexport const isArray = array.isArray\n\n/**\n * @param {any} s\n * @return {s is String}\n */\nexport const isString = (s) => s && s.constructor === String\n\n/**\n * @param {any} n\n * @return {n is Number}\n */\nexport const isNumber = n => n != null && n.constructor === Number\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {any} n\n * @param {TYPE} T\n * @return {n is InstanceType<TYPE>}\n */\nexport const is = (n, T) => n && n.constructor === T\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {TYPE} T\n */\nexport const isTemplate = (T) =>\n  /**\n   * @param {any} n\n   * @return {n is InstanceType<TYPE>}\n   **/\n  n => n && n.constructor === T\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;AAED;AACA;AACA;;;;AAQO,MAAM,UAAU,CAAC,IAAI,MAAM,IAAI,CAAC;IACrC,IAAI;QACF,MAAO,IAAI,GAAG,MAAM,EAAE,IAAK;YACzB,EAAE,CAAC,EAAE,IAAI;QACX;IACF,SAAU;QACR,IAAI,IAAI,GAAG,MAAM,EAAE;YACjB,QAAQ,IAAI,MAAM,IAAI;QACxB;IACF;AACF;AAEO,MAAM,MAAM,KAAO;AAOnB,MAAM,QAAQ,CAAA,IAAK;AAQnB,MAAM,KAAK,CAAA,IAAK;AAShB,MAAM,iBAAiB,CAAC,GAAG,IAAM,MAAM;AASvC,MAAM,eAAe,CAAC,GAAG,IAAM,MAAM,KAAM,KAAK,QAAQ,KAAK,QAAQ,EAAE,WAAW,KAAK,EAAE,WAAW,IAAI,CAAC,AAAC,CAAA,GAAA,mLAAA,CAAA,UAAa,AAAD,EAAE,MAAM,CAAA,GAAA,mLAAA,CAAA,YAAe,AAAD,EAAE,GAA4B,MAAS,OAAO,MAAM,YAAY,CAAA,GAAA,oLAAA,CAAA,YAAgB,AAAD,EAAE,GAAG,EAAG;AASnO,MAAM,eAAe,CAAC,GAAG;IAC9B,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IACA,IAAI,KAAK,QAAQ,KAAK,QAAQ,EAAE,WAAW,KAAK,EAAE,WAAW,EAAE;QAC7D,OAAO;IACT;IACA,IAAI,CAAC,CAAC,oLAAA,CAAA,sBAA0B,CAAC,IAAI,MAAM;QACzC,OAAO,CAAC,CAAC,oLAAA,CAAA,sBAA0B,CAAC,CAAC;IACvC;IACA,OAAQ,EAAE,WAAW;QACnB,KAAK;YACH,IAAI,IAAI,WAAW;YACnB,IAAI,IAAI,WAAW;QACrB,0CAA0C;QAC1C,KAAK;YAAY;gBACf,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;oBACjC,OAAO;gBACT;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;oBACjC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;wBACjB,OAAO;oBACT;gBACF;gBACA;YACF;QACA,KAAK;YAAK;gBACR,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;oBACrB,OAAO;gBACT;gBACA,KAAK,MAAM,SAAS,EAAG;oBACrB,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ;wBACjB,OAAO;oBACT;gBACF;gBACA;YACF;QACA,KAAK;YAAK;gBACR,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;oBACrB,OAAO;gBACT;gBACA,KAAK,MAAM,OAAO,EAAE,IAAI,GAAI;oBAC1B,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO;wBACxD,OAAO;oBACT;gBACF;gBACA;YACF;QACA,KAAK;YACH,IAAI,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD,EAAE,IAAI;gBACzC,OAAO;YACT;YACA,IAAK,MAAM,OAAO,EAAG;gBACnB,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,cAAkB,AAAD,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,GAAG;oBAChE,OAAO;gBACT;YACF;YACA;QACF,KAAK;YACH,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;gBACzB,OAAO;YACT;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;gBACjC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG;oBAC7B,OAAO;gBACT;YACF;YACA;QACF;YACE,OAAO;IACX;IACA,OAAO;AACT;AAUO,MAAM,UAAU,CAAC,OAAO,UAAY,QAAQ,QAAQ,CAAC;AAGrD,MAAM,UAAU,mLAAA,CAAA,UAAa;AAM7B,MAAM,WAAW,CAAC,IAAM,KAAK,EAAE,WAAW,KAAK;AAM/C,MAAM,WAAW,CAAA,IAAK,KAAK,QAAQ,EAAE,WAAW,KAAK;AAQrD,MAAM,KAAK,CAAC,GAAG,IAAM,KAAK,EAAE,WAAW,KAAK;AAM5C,MAAM,aAAa,CAAC,IACzB;;;IAGE,GACF,CAAA,IAAK,KAAK,EAAE,WAAW,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/environment.js"], "sourcesContent": ["/**\n * Isomorphic module to work access the environment (query params, env variables).\n *\n * @module environment\n */\n\nimport * as map from './map.js'\nimport * as string from './string.js'\nimport * as conditions from './conditions.js'\nimport * as storage from './storage.js'\nimport * as f from './function.js'\n\n/* c8 ignore next 2 */\n// @ts-ignore\nexport const isNode = typeof process !== 'undefined' && process.release && /node|io\\.js/.test(process.release.name) && Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n\n/* c8 ignore next */\nexport const isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && !isNode\n/* c8 ignore next 3 */\nexport const isMac = typeof navigator !== 'undefined'\n  ? /Mac/.test(navigator.platform)\n  : false\n\n/**\n * @type {Map<string,string>}\n */\nlet params\nconst args = []\n\n/* c8 ignore start */\nconst computeParams = () => {\n  if (params === undefined) {\n    if (isNode) {\n      params = map.create()\n      const pargs = process.argv\n      let currParamName = null\n      for (let i = 0; i < pargs.length; i++) {\n        const parg = pargs[i]\n        if (parg[0] === '-') {\n          if (currParamName !== null) {\n            params.set(currParamName, '')\n          }\n          currParamName = parg\n        } else {\n          if (currParamName !== null) {\n            params.set(currParamName, parg)\n            currParamName = null\n          } else {\n            args.push(parg)\n          }\n        }\n      }\n      if (currParamName !== null) {\n        params.set(currParamName, '')\n      }\n      // in ReactNative for example this would not be true (unless connected to the Remote Debugger)\n    } else if (typeof location === 'object') {\n      params = map.create(); // eslint-disable-next-line no-undef\n      (location.search || '?').slice(1).split('&').forEach((kv) => {\n        if (kv.length !== 0) {\n          const [key, value] = kv.split('=')\n          params.set(`--${string.fromCamelCase(key, '-')}`, value)\n          params.set(`-${string.fromCamelCase(key, '-')}`, value)\n        }\n      })\n    } else {\n      params = map.create()\n    }\n  }\n  return params\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next */\nexport const hasParam = (name) => computeParams().has(name)\n\n/**\n * @param {string} name\n * @param {string} defaultVal\n * @return {string}\n */\n/* c8 ignore next 2 */\nexport const getParam = (name, defaultVal) =>\n  computeParams().get(name) || defaultVal\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 4 */\nexport const getVariable = (name) =>\n  isNode\n    ? conditions.undefinedToNull(process.env[name.toUpperCase().replaceAll('-', '_')])\n    : conditions.undefinedToNull(storage.varStorage.getItem(name))\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 2 */\nexport const getConf = (name) =>\n  computeParams().get('--' + name) || getVariable(name)\n\n/**\n * @param {string} name\n * @return {string}\n */\n/* c8 ignore next 5 */\nexport const ensureConf = (name) => {\n  const c = getConf(name)\n  if (c == null) throw new Error(`Expected configuration \"${name.toUpperCase().replaceAll('-', '_')}\"`)\n  return c\n}\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next 2 */\nexport const hasConf = (name) =>\n  hasParam('--' + name) || getVariable(name) !== null\n\n/* c8 ignore next */\nexport const production = hasConf('production')\n\n/* c8 ignore next 2 */\nconst forceColor = isNode &&\n  f.isOneOf(process.env.FORCE_COLOR, ['true', '1', '2'])\n\n/* c8 ignore start */\n/**\n * Color is enabled by default if the terminal supports it.\n *\n * Explicitly enable color using `--color` parameter\n * Disable color using `--no-color` parameter or using `NO_COLOR=1` environment variable.\n * `FORCE_COLOR=1` enables color and takes precedence over all.\n */\nexport const supportsColor = forceColor || (\n  !hasParam('--no-colors') && // @todo deprecate --no-colors\n  !hasConf('no-color') &&\n  (!isNode || process.stdout.isTTY) && (\n    !isNode ||\n    hasParam('--color') ||\n    getVariable('COLORTERM') !== null ||\n    (getVariable('TERM') || '').includes('color')\n  )\n)\n/* c8 ignore stop */\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;AAU4B;AAR7B;AACA;AACA;AACA;AACA;;;;;;AAIO,MAAM,SAAS,OAAO,0WAAA,CAAA,UAAO,KAAK,eAAe,0WAAA,CAAA,UAAO,CAAC,OAAO,IAAI,cAAc,IAAI,CAAC,0WAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,0WAAA,CAAA,UAAO,KAAK,cAAc,0WAAA,CAAA,UAAO,GAAG,OAAO;AAGjM,MAAM,YAAY,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,CAAC;AAEvF,MAAM,QAAQ,OAAO,cAAc,cACtC,MAAM,IAAI,CAAC,UAAU,QAAQ,IAC7B;AAEJ;;CAEC,GACD,IAAI;AACJ,MAAM,OAAO,EAAE;AAEf,mBAAmB,GACnB,MAAM,gBAAgB;IACpB,IAAI,WAAW,WAAW;QACxB,IAAI,QAAQ;YACV,SAAS,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;YAClB,MAAM,QAAQ,0WAAA,CAAA,UAAO,CAAC,IAAI;YAC1B,IAAI,gBAAgB;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;oBACnB,IAAI,kBAAkB,MAAM;wBAC1B,OAAO,GAAG,CAAC,eAAe;oBAC5B;oBACA,gBAAgB;gBAClB,OAAO;oBACL,IAAI,kBAAkB,MAAM;wBAC1B,OAAO,GAAG,CAAC,eAAe;wBAC1B,gBAAgB;oBAClB,OAAO;wBACL,KAAK,IAAI,CAAC;oBACZ;gBACF;YACF;YACA,IAAI,kBAAkB,MAAM;gBAC1B,OAAO,GAAG,CAAC,eAAe;YAC5B;QACA,8FAA8F;QAChG,OAAO,IAAI,OAAO,aAAa,UAAU;YACvC,SAAS,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,KAAK,oCAAoC;YAC3D,CAAC,SAAS,MAAM,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC;gBACpD,IAAI,GAAG,MAAM,KAAK,GAAG;oBACnB,MAAM,CAAC,KAAK,MAAM,GAAG,GAAG,KAAK,CAAC;oBAC9B,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAA,GAAA,oLAAA,CAAA,gBAAoB,AAAD,EAAE,KAAK,MAAM,EAAE;oBAClD,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA,GAAA,oLAAA,CAAA,gBAAoB,AAAD,EAAE,KAAK,MAAM,EAAE;gBACnD;YACF;QACF,OAAO;YACL,SAAS,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;QACpB;IACF;IACA,OAAO;AACT;AAQO,MAAM,WAAW,CAAC,OAAS,gBAAgB,GAAG,CAAC;AAQ/C,MAAM,WAAW,CAAC,MAAM,aAC7B,gBAAgB,GAAG,CAAC,SAAS;AAOxB,MAAM,cAAc,CAAC,OAC1B,SACI,CAAA,GAAA,wLAAA,CAAA,kBAA0B,AAAD,EAAE,0WAAA,CAAA,UAAO,CAAC,GAAG,CAAC,KAAK,WAAW,GAAG,UAAU,CAAC,KAAK,KAAK,IAC/E,CAAA,GAAA,wLAAA,CAAA,kBAA0B,AAAD,EAAE,qLAAA,CAAA,aAAkB,CAAC,OAAO,CAAC;AAOrD,MAAM,UAAU,CAAC,OACtB,gBAAgB,GAAG,CAAC,OAAO,SAAS,YAAY;AAO3C,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,QAAQ;IAClB,IAAI,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,WAAW,GAAG,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;IACpG,OAAO;AACT;AAOO,MAAM,UAAU,CAAC,OACtB,SAAS,OAAO,SAAS,YAAY,UAAU;AAG1C,MAAM,aAAa,QAAQ;AAElC,oBAAoB,GACpB,MAAM,aAAa,UACjB,CAAA,GAAA,sLAAA,CAAA,UAAS,AAAD,EAAE,0WAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,EAAE;IAAC;IAAQ;IAAK;CAAI;AAUhD,MAAM,gBAAgB,cAC3B,CAAC,SAAS,kBAAkB,8BAA8B;AAC1D,CAAC,QAAQ,eACT,CAAC,CAAC,UAAU,0WAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK,KAAK,CACnC,CAAC,UACD,SAAS,cACT,YAAY,iBAAiB,QAC7B,CAAC,YAAY,WAAW,EAAE,EAAE,QAAQ,CAAC,QACvC,EAEF,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/buffer.js"], "sourcesContent": ["/**\n * Utility functions to work with buffers (Uint8Array).\n *\n * @module buffer\n */\n\nimport * as string from './string.js'\nimport * as env from './environment.js'\nimport * as array from './array.js'\nimport * as math from './math.js'\nimport * as encoding from './encoding.js'\nimport * as decoding from './decoding.js'\n\n/**\n * @param {number} len\n */\nexport const createUint8ArrayFromLen = len => new Uint8Array(len)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n * @param {number} byteOffset\n * @param {number} length\n */\nexport const createUint8ArrayViewFromArrayBuffer = (buffer, byteOffset, length) => new Uint8Array(buffer, byteOffset, length)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n */\nexport const createUint8ArrayFromArrayBuffer = buffer => new Uint8Array(buffer)\n\n/* c8 ignore start */\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Browser = bytes => {\n  let s = ''\n  for (let i = 0; i < bytes.byteLength; i++) {\n    s += string.fromCharCode(bytes[i])\n  }\n  // eslint-disable-next-line no-undef\n  return btoa(s)\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Node = bytes => Buffer.from(bytes.buffer, bytes.byteOffset, bytes.byteLength).toString('base64')\n\n/* c8 ignore start */\n/**\n * @param {string} s\n * @return {Uint8Array}\n */\nconst fromBase64Browser = s => {\n  // eslint-disable-next-line no-undef\n  const a = atob(s)\n  const bytes = createUint8ArrayFromLen(a.length)\n  for (let i = 0; i < a.length; i++) {\n    bytes[i] = a.charCodeAt(i)\n  }\n  return bytes\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} s\n */\nconst fromBase64Node = s => {\n  const buf = Buffer.from(s, 'base64')\n  return createUint8ArrayViewFromArrayBuffer(buf.buffer, buf.byteOffset, buf.byteLength)\n}\n\n/* c8 ignore next */\nexport const toBase64 = env.isBrowser ? toBase64Browser : toBase64Node\n\n/* c8 ignore next */\nexport const fromBase64 = env.isBrowser ? fromBase64Browser : fromBase64Node\n\n/**\n * Implements base64url - see https://datatracker.ietf.org/doc/html/rfc4648#section-5\n * @param {Uint8Array} buf\n */\nexport const toBase64UrlEncoded = buf => toBase64(buf).replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '')\n\n/**\n * @param {string} base64\n */\nexport const fromBase64UrlEncoded = base64 => fromBase64(base64.replaceAll('-', '+').replaceAll('_', '/'))\n\n/**\n * Base64 is always a more efficient choice. This exists for utility purposes only.\n *\n * @param {Uint8Array} buf\n */\nexport const toHexString = buf => array.map(buf, b => b.toString(16).padStart(2, '0')).join('')\n\n/**\n * Note: This function expects that the hex doesn't start with 0x..\n *\n * @param {string} hex\n */\nexport const fromHexString = hex => {\n  const hlen = hex.length\n  const buf = new Uint8Array(math.ceil(hlen / 2))\n  for (let i = 0; i < hlen; i += 2) {\n    buf[buf.length - i / 2 - 1] = Number.parseInt(hex.slice(hlen - i - 2, hlen - i), 16)\n  }\n  return buf\n}\n\n/**\n * Copy the content of an Uint8Array view to a new ArrayBuffer.\n *\n * @param {Uint8Array} uint8Array\n * @return {Uint8Array}\n */\nexport const copyUint8Array = uint8Array => {\n  const newBuf = createUint8ArrayFromLen(uint8Array.byteLength)\n  newBuf.set(uint8Array)\n  return newBuf\n}\n\n/**\n * Encode anything as a UInt8Array. It's a pun on typescripts's `any` type.\n * See encoding.writeAny for more information.\n *\n * @param {any} data\n * @return {Uint8Array}\n */\nexport const encodeAny = data =>\n  encoding.encode(encoder => encoding.writeAny(encoder, data))\n\n/**\n * Decode an any-encoded value.\n *\n * @param {Uint8Array} buf\n * @return {any}\n */\nexport const decodeAny = buf => decoding.readAny(decoding.createDecoder(buf))\n\n/**\n * Shift Byte Array {N} bits to the left. Does not expand byte array.\n *\n * @param {Uint8Array} bs\n * @param {number} N should be in the range of [0-7]\n */\nexport const shiftNBitsLeft = (bs, N) => {\n  if (N === 0) return bs\n  bs = new Uint8Array(bs)\n  bs[0] <<= N\n  for (let i = 1; i < bs.length; i++) {\n    bs[i - 1] |= bs[i] >>> (8 - N)\n    bs[i] <<= N\n  }\n  return bs\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;AAiD6B;AA/C9B;AACA;AACA;AACA;AACA;AACA;;;;;;;AAKO,MAAM,0BAA0B,CAAA,MAAO,IAAI,WAAW;AAStD,MAAM,sCAAsC,CAAC,QAAQ,YAAY,SAAW,IAAI,WAAW,QAAQ,YAAY;AAO/G,MAAM,kCAAkC,CAAA,SAAU,IAAI,WAAW;AAExE,mBAAmB,GACnB;;;CAGC,GACD,MAAM,kBAAkB,CAAA;IACtB,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,UAAU,EAAE,IAAK;QACzC,KAAK,CAAA,GAAA,oLAAA,CAAA,eAAmB,AAAD,EAAE,KAAK,CAAC,EAAE;IACnC;IACA,oCAAoC;IACpC,OAAO,KAAK;AACd;AACA,kBAAkB,GAElB;;;CAGC,GACD,MAAM,eAAe,CAAA,QAAS,wWAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE,MAAM,UAAU,EAAE,MAAM,UAAU,EAAE,QAAQ,CAAC;AAErG,mBAAmB,GACnB;;;CAGC,GACD,MAAM,oBAAoB,CAAA;IACxB,oCAAoC;IACpC,MAAM,IAAI,KAAK;IACf,MAAM,QAAQ,wBAAwB,EAAE,MAAM;IAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,KAAK,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC;IAC1B;IACA,OAAO;AACT;AACA,kBAAkB,GAElB;;CAEC,GACD,MAAM,iBAAiB,CAAA;IACrB,MAAM,MAAM,wWAAA,CAAA,SAAM,CAAC,IAAI,CAAC,GAAG;IAC3B,OAAO,oCAAoC,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI,UAAU;AACvF;AAGO,MAAM,WAAW,yLAAA,CAAA,YAAa,GAAG,kBAAkB;AAGnD,MAAM,aAAa,yLAAA,CAAA,YAAa,GAAG,oBAAoB;AAMvD,MAAM,qBAAqB,CAAA,MAAO,SAAS,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK;AAK1G,MAAM,uBAAuB,CAAA,SAAU,WAAW,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK;AAO9F,MAAM,cAAc,CAAA,MAAO,CAAA,GAAA,mLAAA,CAAA,MAAS,AAAD,EAAE,KAAK,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAOrF,MAAM,gBAAgB,CAAA;IAC3B,MAAM,OAAO,IAAI,MAAM;IACvB,MAAM,MAAM,IAAI,WAAW,CAAA,GAAA,kLAAA,CAAA,OAAS,AAAD,EAAE,OAAO;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,EAAG;QAChC,GAAG,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,GAAG,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,GAAG,OAAO,IAAI;IACnF;IACA,OAAO;AACT;AAQO,MAAM,iBAAiB,CAAA;IAC5B,MAAM,SAAS,wBAAwB,WAAW,UAAU;IAC5D,OAAO,GAAG,CAAC;IACX,OAAO;AACT;AASO,MAAM,YAAY,CAAA,OACvB,CAAA,GAAA,sLAAA,CAAA,SAAe,AAAD,EAAE,CAAA,UAAW,CAAA,GAAA,sLAAA,CAAA,WAAiB,AAAD,EAAE,SAAS;AAQjD,MAAM,YAAY,CAAA,MAAO,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,gBAAsB,AAAD,EAAE;AAQjE,MAAM,iBAAiB,CAAC,IAAI;IACjC,IAAI,MAAM,GAAG,OAAO;IACpB,KAAK,IAAI,WAAW;IACpB,EAAE,CAAC,EAAE,KAAK;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAM,IAAI;QAC5B,EAAE,CAAC,EAAE,KAAK;IACZ;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/pair.js"], "sourcesContent": ["/**\n * Working with value pairs.\n *\n * @module pair\n */\n\n/**\n * @template L,R\n */\nexport class Pair {\n  /**\n   * @param {L} left\n   * @param {R} right\n   */\n  constructor (left, right) {\n    this.left = left\n    this.right = right\n  }\n}\n\n/**\n * @template L,R\n * @param {L} left\n * @param {R} right\n * @return {Pair<L,R>}\n */\nexport const create = (left, right) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {R} right\n * @param {L} left\n * @return {Pair<L,R>}\n */\nexport const createReversed = (right, left) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):any} f\n */\nexport const forEach = (arr, f) => arr.forEach(p => f(p.left, p.right))\n\n/**\n * @template L,R,X\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):X} f\n * @return {Array<X>}\n */\nexport const map = (arr, f) => arr.map(p => f(p.left, p.right))\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;CAEC;;;;;;;AACM,MAAM;IACX;;;GAGC,GACD,YAAa,IAAI,EAAE,KAAK,CAAE;QACxB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;AACF;AAQO,MAAM,SAAS,CAAC,MAAM,QAAU,IAAI,KAAK,MAAM;AAQ/C,MAAM,iBAAiB,CAAC,OAAO,OAAS,IAAI,KAAK,MAAM;AAOvD,MAAM,UAAU,CAAC,KAAK,IAAM,IAAI,OAAO,CAAC,CAAA,IAAK,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK;AAQ9D,MAAM,MAAM,CAAC,KAAK,IAAM,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/dom.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Utility module to work with the DOM.\n *\n * @module dom\n */\n\nimport * as pair from './pair.js'\nimport * as map from './map.js'\n\n/* c8 ignore start */\n/**\n * @type {Document}\n */\nexport const doc = /** @type {Document} */ (typeof document !== 'undefined' ? document : {})\n\n/**\n * @param {string} name\n * @return {HTMLElement}\n */\nexport const createElement = name => doc.createElement(name)\n\n/**\n * @return {DocumentFragment}\n */\nexport const createDocumentFragment = () => doc.createDocumentFragment()\n\n/**\n * @param {string} text\n * @return {Text}\n */\nexport const createTextNode = text => doc.createTextNode(text)\n\nexport const domParser = /** @type {DOMParser} */ (typeof DOMParser !== 'undefined' ? new DOMParser() : null)\n\n/**\n * @param {HTMLElement} el\n * @param {string} name\n * @param {Object} opts\n */\nexport const emitCustomEvent = (el, name, opts) => el.dispatchEvent(new CustomEvent(name, opts))\n\n/**\n * @param {Element} el\n * @param {Array<pair.Pair<string,string|boolean>>} attrs Array of key-value pairs\n * @return {Element}\n */\nexport const setAttributes = (el, attrs) => {\n  pair.forEach(attrs, (key, value) => {\n    if (value === false) {\n      el.removeAttribute(key)\n    } else if (value === true) {\n      el.setAttribute(key, '')\n    } else {\n      // @ts-ignore\n      el.setAttribute(key, value)\n    }\n  })\n  return el\n}\n\n/**\n * @param {Element} el\n * @param {Map<string, string>} attrs Array of key-value pairs\n * @return {Element}\n */\nexport const setAttributesMap = (el, attrs) => {\n  attrs.forEach((value, key) => { el.setAttribute(key, value) })\n  return el\n}\n\n/**\n * @param {Array<Node>|HTMLCollection} children\n * @return {DocumentFragment}\n */\nexport const fragment = children => {\n  const fragment = createDocumentFragment()\n  for (let i = 0; i < children.length; i++) {\n    appendChild(fragment, children[i])\n  }\n  return fragment\n}\n\n/**\n * @param {Element} parent\n * @param {Array<Node>} nodes\n * @return {Element}\n */\nexport const append = (parent, nodes) => {\n  appendChild(parent, fragment(nodes))\n  return parent\n}\n\n/**\n * @param {HTMLElement} el\n */\nexport const remove = el => el.remove()\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nexport const addEventListener = (el, name, f) => el.addEventListener(name, f)\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nexport const removeEventListener = (el, name, f) => el.removeEventListener(name, f)\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nexport const addEventListeners = (node, listeners) => {\n  pair.forEach(listeners, (name, f) => addEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nexport const removeEventListeners = (node, listeners) => {\n  pair.forEach(listeners, (name, f) => removeEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {string} name\n * @param {Array<pair.Pair<string,string>|pair.Pair<string,boolean>>} attrs Array of key-value pairs\n * @param {Array<Node>} children\n * @return {Element}\n */\nexport const element = (name, attrs = [], children = []) =>\n  append(setAttributes(createElement(name), attrs), children)\n\n/**\n * @param {number} width\n * @param {number} height\n */\nexport const canvas = (width, height) => {\n  const c = /** @type {HTMLCanvasElement} */ (createElement('canvas'))\n  c.height = height\n  c.width = width\n  return c\n}\n\n/**\n * @param {string} t\n * @return {Text}\n */\nexport const text = createTextNode\n\n/**\n * @param {pair.Pair<string,string>} pair\n */\nexport const pairToStyleString = pair => `${pair.left}:${pair.right};`\n\n/**\n * @param {Array<pair.Pair<string,string>>} pairs\n * @return {string}\n */\nexport const pairsToStyleString = pairs => pairs.map(pairToStyleString).join('')\n\n/**\n * @param {Map<string,string>} m\n * @return {string}\n */\nexport const mapToStyleString = m => map.map(m, (value, key) => `${key}:${value};`).join('')\n\n/**\n * @todo should always query on a dom element\n *\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {HTMLElement | null}\n */\nexport const querySelector = (el, query) => el.querySelector(query)\n\n/**\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {NodeListOf<HTMLElement>}\n */\nexport const querySelectorAll = (el, query) => el.querySelectorAll(query)\n\n/**\n * @param {string} id\n * @return {HTMLElement}\n */\nexport const getElementById = id => /** @type {HTMLElement} */ (doc.getElementById(id))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nconst _parse = html => domParser.parseFromString(`<html><body>${html}</body></html>`, 'text/html').body\n\n/**\n * @param {string} html\n * @return {DocumentFragment}\n */\nexport const parseFragment = html => fragment(/** @type {any} */ (_parse(html).childNodes))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nexport const parseElement = html => /** @type HTMLElement */ (_parse(html).firstElementChild)\n\n/**\n * @param {HTMLElement} oldEl\n * @param {HTMLElement|DocumentFragment} newEl\n */\nexport const replaceWith = (oldEl, newEl) => oldEl.replaceWith(newEl)\n\n/**\n * @param {HTMLElement} parent\n * @param {HTMLElement} el\n * @param {Node|null} ref\n * @return {HTMLElement}\n */\nexport const insertBefore = (parent, el, ref) => parent.insertBefore(el, ref)\n\n/**\n * @param {Node} parent\n * @param {Node} child\n * @return {Node}\n */\nexport const appendChild = (parent, child) => parent.appendChild(child)\n\nexport const ELEMENT_NODE = doc.ELEMENT_NODE\nexport const TEXT_NODE = doc.TEXT_NODE\nexport const CDATA_SECTION_NODE = doc.CDATA_SECTION_NODE\nexport const COMMENT_NODE = doc.COMMENT_NODE\nexport const DOCUMENT_NODE = doc.DOCUMENT_NODE\nexport const DOCUMENT_TYPE_NODE = doc.DOCUMENT_TYPE_NODE\nexport const DOCUMENT_FRAGMENT_NODE = doc.DOCUMENT_FRAGMENT_NODE\n\n/**\n * @param {any} node\n * @param {number} type\n */\nexport const checkNodeType = (node, type) => node.nodeType === type\n\n/**\n * @param {Node} parent\n * @param {HTMLElement} child\n */\nexport const isParentOf = (parent, child) => {\n  let p = child.parentNode\n  while (p && p !== parent) {\n    p = p.parentNode\n  }\n  return p === parent\n}\n/* c8 ignore stop */\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;AACA;;;AAMO,MAAM,MAA+B,OAAO,aAAa,cAAc,WAAW,CAAC;AAMnF,MAAM,gBAAgB,CAAA,OAAQ,IAAI,aAAa,CAAC;AAKhD,MAAM,yBAAyB,IAAM,IAAI,sBAAsB;AAM/D,MAAM,iBAAiB,CAAA,OAAQ,IAAI,cAAc,CAAC;AAElD,MAAM,YAAsC,OAAO,cAAc,cAAc,IAAI,cAAc;AAOjG,MAAM,kBAAkB,CAAC,IAAI,MAAM,OAAS,GAAG,aAAa,CAAC,IAAI,YAAY,MAAM;AAOnF,MAAM,gBAAgB,CAAC,IAAI;IAChC,CAAA,GAAA,kLAAA,CAAA,UAAY,AAAD,EAAE,OAAO,CAAC,KAAK;QACxB,IAAI,UAAU,OAAO;YACnB,GAAG,eAAe,CAAC;QACrB,OAAO,IAAI,UAAU,MAAM;YACzB,GAAG,YAAY,CAAC,KAAK;QACvB,OAAO;YACL,aAAa;YACb,GAAG,YAAY,CAAC,KAAK;QACvB;IACF;IACA,OAAO;AACT;AAOO,MAAM,mBAAmB,CAAC,IAAI;IACnC,MAAM,OAAO,CAAC,CAAC,OAAO;QAAU,GAAG,YAAY,CAAC,KAAK;IAAO;IAC5D,OAAO;AACT;AAMO,MAAM,WAAW,CAAA;IACtB,MAAM,WAAW;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,YAAY,UAAU,QAAQ,CAAC,EAAE;IACnC;IACA,OAAO;AACT;AAOO,MAAM,SAAS,CAAC,QAAQ;IAC7B,YAAY,QAAQ,SAAS;IAC7B,OAAO;AACT;AAKO,MAAM,SAAS,CAAA,KAAM,GAAG,MAAM;AAO9B,MAAM,mBAAmB,CAAC,IAAI,MAAM,IAAM,GAAG,gBAAgB,CAAC,MAAM;AAOpE,MAAM,sBAAsB,CAAC,IAAI,MAAM,IAAM,GAAG,mBAAmB,CAAC,MAAM;AAO1E,MAAM,oBAAoB,CAAC,MAAM;IACtC,CAAA,GAAA,kLAAA,CAAA,UAAY,AAAD,EAAE,WAAW,CAAC,MAAM,IAAM,iBAAiB,MAAM,MAAM;IAClE,OAAO;AACT;AAOO,MAAM,uBAAuB,CAAC,MAAM;IACzC,CAAA,GAAA,kLAAA,CAAA,UAAY,AAAD,EAAE,WAAW,CAAC,MAAM,IAAM,oBAAoB,MAAM,MAAM;IACrE,OAAO;AACT;AAQO,MAAM,UAAU,CAAC,MAAM,QAAQ,EAAE,EAAE,WAAW,EAAE,GACrD,OAAO,cAAc,cAAc,OAAO,QAAQ;AAM7C,MAAM,SAAS,CAAC,OAAO;IAC5B,MAAM,IAAsC,cAAc;IAC1D,EAAE,MAAM,GAAG;IACX,EAAE,KAAK,GAAG;IACV,OAAO;AACT;AAMO,MAAM,OAAO;AAKb,MAAM,oBAAoB,CAAA,OAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;AAM/D,MAAM,qBAAqB,CAAA,QAAS,MAAM,GAAG,CAAC,mBAAmB,IAAI,CAAC;AAMtE,MAAM,mBAAmB,CAAA,IAAK,CAAA,GAAA,iLAAA,CAAA,MAAO,AAAD,EAAE,GAAG,CAAC,OAAO,MAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;AASlF,MAAM,gBAAgB,CAAC,IAAI,QAAU,GAAG,aAAa,CAAC;AAOtD,MAAM,mBAAmB,CAAC,IAAI,QAAU,GAAG,gBAAgB,CAAC;AAM5D,MAAM,iBAAiB,CAAA,KAAkC,IAAI,cAAc,CAAC;AAEnF;;;CAGC,GACD,MAAM,SAAS,CAAA,OAAQ,UAAU,eAAe,CAAC,CAAC,YAAY,EAAE,KAAK,cAAc,CAAC,EAAE,aAAa,IAAI;AAMhG,MAAM,gBAAgB,CAAA,OAAQ,SAA6B,OAAO,MAAM,UAAU;AAMlF,MAAM,eAAe,CAAA,OAAkC,OAAO,MAAM,iBAAiB;AAMrF,MAAM,cAAc,CAAC,OAAO,QAAU,MAAM,WAAW,CAAC;AAQxD,MAAM,eAAe,CAAC,QAAQ,IAAI,MAAQ,OAAO,YAAY,CAAC,IAAI;AAOlE,MAAM,cAAc,CAAC,QAAQ,QAAU,OAAO,WAAW,CAAC;AAE1D,MAAM,eAAe,IAAI,YAAY;AACrC,MAAM,YAAY,IAAI,SAAS;AAC/B,MAAM,qBAAqB,IAAI,kBAAkB;AACjD,MAAM,eAAe,IAAI,YAAY;AACrC,MAAM,gBAAgB,IAAI,aAAa;AACvC,MAAM,qBAAqB,IAAI,kBAAkB;AACjD,MAAM,yBAAyB,IAAI,sBAAsB;AAMzD,MAAM,gBAAgB,CAAC,MAAM,OAAS,KAAK,QAAQ,KAAK;AAMxD,MAAM,aAAa,CAAC,QAAQ;IACjC,IAAI,IAAI,MAAM,UAAU;IACxB,MAAO,KAAK,MAAM,OAAQ;QACxB,IAAI,EAAE,UAAU;IAClB;IACA,OAAO,MAAM;AACf,EACA,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/json.js"], "sourcesContent": ["/**\n * JSON utility functions.\n *\n * @module json\n */\n\n/**\n * Transform JavaScript object to JSON.\n *\n * @param {any} object\n * @return {string}\n */\nexport const stringify = JSON.stringify\n\n/**\n * Parse JSON object.\n *\n * @param {string} json\n * @return {any}\n */\nexport const parse = JSON.parse\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;CAKC;;;;AACM,MAAM,YAAY,KAAK,SAAS;AAQhC,MAAM,QAAQ,KAAK,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2641, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/eventloop.js"], "sourcesContent": ["/* global requestIdleCallback, requestAnimationFrame, cancelIdleCallback, cancelAnimationFrame */\n\nimport * as time from './time.js'\n\n/**\n * Utility module to work with EcmaScript's event loop.\n *\n * @module eventloop\n */\n\n/**\n * @type {Array<function>}\n */\nlet queue = []\n\nconst _runQueue = () => {\n  for (let i = 0; i < queue.length; i++) {\n    queue[i]()\n  }\n  queue = []\n}\n\n/**\n * @param {function():void} f\n */\nexport const enqueue = f => {\n  queue.push(f)\n  if (queue.length === 1) {\n    setTimeout(_runQueue, 0)\n  }\n}\n\n/**\n * @typedef {Object} TimeoutObject\n * @property {function} TimeoutObject.destroy\n */\n\n/**\n * @param {function(number):void} clearFunction\n */\nconst createTimeoutClass = clearFunction => class TT {\n  /**\n   * @param {number} timeoutId\n   */\n  constructor (timeoutId) {\n    this._ = timeoutId\n  }\n\n  destroy () {\n    clearFunction(this._)\n  }\n}\n\nconst Timeout = createTimeoutClass(clearTimeout)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nexport const timeout = (timeout, callback) => new Timeout(setTimeout(callback, timeout))\n\nconst Interval = createTimeoutClass(clearInterval)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nexport const interval = (timeout, callback) => new Interval(setInterval(callback, timeout))\n\n/* c8 ignore next */\nexport const Animation = createTimeoutClass(arg => typeof requestAnimationFrame !== 'undefined' && cancelAnimationFrame(arg))\n\n/**\n * @param {function(number):void} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next */\nexport const animationFrame = cb => typeof requestAnimationFrame === 'undefined' ? timeout(0, cb) : new Animation(requestAnimationFrame(cb))\n\n/* c8 ignore next */\n// @ts-ignore\nconst Idle = createTimeoutClass(arg => typeof cancelIdleCallback !== 'undefined' && cancelIdleCallback(arg))\n\n/**\n * Note: this is experimental and is probably only useful in browsers.\n *\n * @param {function} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next 2 */\n// @ts-ignore\nexport const idleCallback = cb => typeof requestIdleCallback !== 'undefined' ? new Idle(requestIdleCallback(cb)) : timeout(1000, cb)\n\n/**\n * @param {number} timeout Timeout of the debounce action\n * @param {number} triggerAfter Optional. Trigger callback after a certain amount of time\n *                              without waiting for debounce.\n */\nexport const createDebouncer = (timeout, triggerAfter = -1) => {\n  let timer = -1\n  /**\n   * @type {number?}\n    */\n  let lastCall = null\n  /**\n   * @param {((...args: any)=>void)?} cb function to trigger after debounce. If null, it will reset the\n   *                         debounce.\n   */\n  return cb => {\n    clearTimeout(timer)\n    if (cb) {\n      if (triggerAfter >= 0) {\n        const now = time.getUnixTime()\n        if (lastCall === null) lastCall = now\n        if (now - lastCall > triggerAfter) {\n          lastCall = null\n          timer = /** @type {any} */ (setTimeout(cb, 0))\n          return\n        }\n      }\n      timer = /** @type {any} */ (setTimeout(() => { lastCall = null; cb() }, timeout))\n    } else {\n      lastCall = null\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,+FAA+F;;;;;;;;;AAE/F;;AAEA;;;;CAIC,GAED;;CAEC,GACD,IAAI,QAAQ,EAAE;AAEd,MAAM,YAAY;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,KAAK,CAAC,EAAE;IACV;IACA,QAAQ,EAAE;AACZ;AAKO,MAAM,UAAU,CAAA;IACrB,MAAM,IAAI,CAAC;IACX,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,WAAW,WAAW;IACxB;AACF;AAEA;;;CAGC,GAED;;CAEC,GACD,MAAM,qBAAqB,CAAA,gBAAiB,MAAM;QAChD;;GAEC,GACD,YAAa,SAAS,CAAE;YACtB,IAAI,CAAC,CAAC,GAAG;QACX;QAEA,UAAW;YACT,cAAc,IAAI,CAAC,CAAC;QACtB;IACF;AAEA,MAAM,UAAU,mBAAmB;AAO5B,MAAM,UAAU,CAAC,SAAS,WAAa,IAAI,QAAQ,WAAW,UAAU;AAE/E,MAAM,WAAW,mBAAmB;AAO7B,MAAM,WAAW,CAAC,SAAS,WAAa,IAAI,SAAS,YAAY,UAAU;AAG3E,MAAM,YAAY,mBAAmB,CAAA,MAAO,OAAO,0BAA0B,eAAe,qBAAqB;AAOjH,MAAM,iBAAiB,CAAA,KAAM,OAAO,0BAA0B,cAAc,QAAQ,GAAG,MAAM,IAAI,UAAU,sBAAsB;AAExI,kBAAkB,GAClB,aAAa;AACb,MAAM,OAAO,mBAAmB,CAAA,MAAO,OAAO,uBAAuB,eAAe,mBAAmB;AAUhG,MAAM,eAAe,CAAA,KAAM,OAAO,wBAAwB,cAAc,IAAI,KAAK,oBAAoB,OAAO,QAAQ,MAAM;AAO1H,MAAM,kBAAkB,CAAC,SAAS,eAAe,CAAC,CAAC;IACxD,IAAI,QAAQ,CAAC;IACb;;IAEE,GACF,IAAI,WAAW;IACf;;;GAGC,GACD,OAAO,CAAA;QACL,aAAa;QACb,IAAI,IAAI;YACN,IAAI,gBAAgB,GAAG;gBACrB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD;gBAC3B,IAAI,aAAa,MAAM,WAAW;gBAClC,IAAI,MAAM,WAAW,cAAc;oBACjC,WAAW;oBACX,QAA4B,WAAW,IAAI;oBAC3C;gBACF;YACF;YACA,QAA4B,WAAW;gBAAQ,WAAW;gBAAM;YAAK,GAAG;QAC1E,OAAO;YACL,WAAW;QACb;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2730, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/symbol.js"], "sourcesContent": ["/**\n * Utility module to work with EcmaScript Symbols.\n *\n * @module symbol\n */\n\n/**\n * Return fresh symbol.\n */\nexport const create = Symbol\n\n/**\n * @param {any} s\n * @return {boolean}\n */\nexport const isSymbol = s => typeof s === 'symbol'\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;CAEC;;;;AACM,MAAM,SAAS;AAMf,MAAM,WAAW,CAAA,IAAK,OAAO,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/logging.common.js"], "sourcesContent": ["import * as symbol from './symbol.js'\nimport * as time from './time.js'\nimport * as env from './environment.js'\nimport * as func from './function.js'\nimport * as json from './json.js'\n\nexport const BOLD = symbol.create()\nexport const UNBOLD = symbol.create()\nexport const BLUE = symbol.create()\nexport const GREY = symbol.create()\nexport const GREEN = symbol.create()\nexport const RED = symbol.create()\nexport const PURPLE = symbol.create()\nexport const ORANGE = symbol.create()\nexport const UNCOLOR = symbol.create()\n\n/* c8 ignore start */\n/**\n * @param {Array<undefined|string|Symbol|Object|number|function():any>} args\n * @return {Array<string|object|number|undefined>}\n */\nexport const computeNoColorLoggingArgs = args => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (arg === undefined) {\n      break\n    } else if (arg.constructor === String || arg.constructor === Number) {\n      strBuilder.push(arg)\n    } else if (arg.constructor === Object) {\n      break\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    logArgs.push(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\nconst loggingColors = [GREEN, PURPLE, ORANGE, BLUE]\nlet nextColor = 0\nlet lastLoggingTime = time.getUnixTime()\n\n/* c8 ignore start */\n/**\n * @param {function(...any):void} _print\n * @param {string} moduleName\n * @return {function(...any):void}\n */\nexport const createModuleLogger = (_print, moduleName) => {\n  const color = loggingColors[nextColor]\n  const debugRegexVar = env.getVariable('log')\n  const doLogging = debugRegexVar !== null &&\n    (debugRegexVar === '*' || debugRegexVar === 'true' ||\n      new RegExp(debugRegexVar, 'gi').test(moduleName))\n  nextColor = (nextColor + 1) % loggingColors.length\n  moduleName += ': '\n  return !doLogging\n    ? func.nop\n    : (...args) => {\n        if (args.length === 1 && args[0]?.constructor === Function) {\n          args = args[0]()\n        }\n        const timeNow = time.getUnixTime()\n        const timeDiff = timeNow - lastLoggingTime\n        lastLoggingTime = timeNow\n        _print(\n          color,\n          moduleName,\n          UNCOLOR,\n          ...args.map((arg) => {\n            if (arg != null && arg.constructor === Uint8Array) {\n              arg = Array.from(arg)\n            }\n            const t = typeof arg\n            switch (t) {\n              case 'string':\n              case 'symbol':\n                return arg\n              default: {\n                return json.stringify(arg)\n              }\n            }\n          }),\n          color,\n          ' +' + timeDiff + 'ms'\n        )\n      }\n}\n/* c8 ignore stop */\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AACzB,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AAC3B,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AACzB,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AACzB,MAAM,QAAQ,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AAC1B,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AACxB,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AAC3B,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AAC3B,MAAM,UAAU,CAAA,GAAA,oLAAA,CAAA,SAAa,AAAD;AAO5B,MAAM,4BAA4B,CAAA;IACvC,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,gBAAgB,UAAU;QAC1D,OAA0D,uBAAuB,GAAG,AAAC,IAAK,CAAC,EAAE;IAC/F;IACA,MAAM,aAAa,EAAE;IACrB,MAAM,UAAU,EAAE;IAClB,0DAA0D;IAC1D,IAAI,IAAI;IACR,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,QAAQ,WAAW;YACrB;QACF,OAAO,IAAI,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,QAAQ;YACnE,WAAW,IAAI,CAAC;QAClB,OAAO,IAAI,IAAI,WAAW,KAAK,QAAQ;YACrC;QACF;IACF;IACA,IAAI,IAAI,GAAG;QACT,0CAA0C;QAC1C,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC;IAC/B;IACA,kBAAkB;IAClB,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC,CAAC,eAAe,MAAM,GAAG;YAC5B,QAAQ,IAAI,CAAC;QACf;IACF;IACA,OAAO;AACT;AACA,kBAAkB,GAElB,MAAM,gBAAgB;IAAC;IAAO;IAAQ;IAAQ;CAAK;AACnD,IAAI,YAAY;AAChB,IAAI,kBAAkB,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD;AAQ9B,MAAM,qBAAqB,CAAC,QAAQ;IACzC,MAAM,QAAQ,aAAa,CAAC,UAAU;IACtC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,cAAe,AAAD,EAAE;IACtC,MAAM,YAAY,kBAAkB,QAClC,CAAC,kBAAkB,OAAO,kBAAkB,UAC1C,IAAI,OAAO,eAAe,MAAM,IAAI,CAAC,WAAW;IACpD,YAAY,CAAC,YAAY,CAAC,IAAI,cAAc,MAAM;IAClD,cAAc;IACd,OAAO,CAAC,YACJ,sLAAA,CAAA,MAAQ,GACR,CAAC,GAAG;QACF,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,gBAAgB,UAAU;YAC1D,OAAO,IAAI,CAAC,EAAE;QAChB;QACA,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,cAAgB,AAAD;QAC/B,MAAM,WAAW,UAAU;QAC3B,kBAAkB;QAClB,OACE,OACA,YACA,YACG,KAAK,GAAG,CAAC,CAAC;YACX,IAAI,OAAO,QAAQ,IAAI,WAAW,KAAK,YAAY;gBACjD,MAAM,MAAM,IAAI,CAAC;YACnB;YACA,MAAM,IAAI,OAAO;YACjB,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT;oBAAS;wBACP,OAAO,CAAA,GAAA,kLAAA,CAAA,YAAc,AAAD,EAAE;oBACxB;YACF;QACF,IACA,OACA,OAAO,WAAW;IAEtB;AACN,EACA,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2855, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/logging.js"], "sourcesContent": ["/**\n * Isomorphic logging module with support for colors!\n *\n * @module logging\n */\n\nimport * as env from './environment.js'\nimport * as set from './set.js'\nimport * as pair from './pair.js'\nimport * as dom from './dom.js'\nimport * as json from './json.js'\nimport * as map from './map.js'\nimport * as eventloop from './eventloop.js'\nimport * as math from './math.js'\nimport * as common from './logging.common.js'\n\nexport { BOLD, UNBOLD, BLUE, GREY, GREEN, RED, PURPLE, ORANGE, UNCOLOR } from './logging.common.js'\n\n/**\n * @type {Object<Symbol,pair.Pair<string,string>>}\n */\nconst _browserStyleMap = {\n  [common.BOLD]: pair.create('font-weight', 'bold'),\n  [common.UNBOLD]: pair.create('font-weight', 'normal'),\n  [common.BLUE]: pair.create('color', 'blue'),\n  [common.GREEN]: pair.create('color', 'green'),\n  [common.GREY]: pair.create('color', 'grey'),\n  [common.RED]: pair.create('color', 'red'),\n  [common.PURPLE]: pair.create('color', 'purple'),\n  [common.ORANGE]: pair.create('color', 'orange'), // not well supported in chrome when debugging node with inspector - TODO: deprecate\n  [common.UNCOLOR]: pair.create('color', 'black')\n}\n\n/**\n * @param {Array<string|Symbol|Object|number|function():any>} args\n * @return {Array<string|object|number>}\n */\n/* c8 ignore start */\nconst computeBrowserLoggingArgs = (args) => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const styles = []\n  const currentStyle = map.create()\n  /**\n   * @type {Array<string|Object|number>}\n   */\n  let logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    // @ts-ignore\n    const style = _browserStyleMap[arg]\n    if (style !== undefined) {\n      currentStyle.set(style.left, style.right)\n    } else {\n      if (arg === undefined) {\n        break\n      }\n      if (arg.constructor === String || arg.constructor === Number) {\n        const style = dom.mapToStyleString(currentStyle)\n        if (i > 0 || style.length > 0) {\n          strBuilder.push('%c' + arg)\n          styles.push(style)\n        } else {\n          strBuilder.push(arg)\n        }\n      } else {\n        break\n      }\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    logArgs = styles\n    logArgs.unshift(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\n/* c8 ignore start */\nconst computeLoggingArgs = env.supportsColor\n  ? computeBrowserLoggingArgs\n  : common.computeNoColorLoggingArgs\n/* c8 ignore stop */\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nexport const print = (...args) => {\n  console.log(...computeLoggingArgs(args))\n  /* c8 ignore next */\n  vconsoles.forEach((vc) => vc.print(args))\n}\n\n/* c8 ignore start */\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nexport const warn = (...args) => {\n  console.warn(...computeLoggingArgs(args))\n  args.unshift(common.ORANGE)\n  vconsoles.forEach((vc) => vc.print(args))\n}\n/* c8 ignore stop */\n\n/**\n * @param {Error} err\n */\n/* c8 ignore start */\nexport const printError = (err) => {\n  console.error(err)\n  vconsoles.forEach((vc) => vc.printError(err))\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} url image location\n * @param {number} height height of the image in pixel\n */\n/* c8 ignore start */\nexport const printImg = (url, height) => {\n  if (env.isBrowser) {\n    console.log(\n      '%c                      ',\n      `font-size: ${height}px; background-size: contain; background-repeat: no-repeat; background-image: url(${url})`\n    )\n    // console.log('%c                ', `font-size: ${height}x; background: url(${url}) no-repeat;`)\n  }\n  vconsoles.forEach((vc) => vc.printImg(url, height))\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} base64\n * @param {number} height\n */\n/* c8 ignore next 2 */\nexport const printImgBase64 = (base64, height) =>\n  printImg(`data:image/gif;base64,${base64}`, height)\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nexport const group = (...args) => {\n  console.group(...computeLoggingArgs(args))\n  /* c8 ignore next */\n  vconsoles.forEach((vc) => vc.group(args))\n}\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nexport const groupCollapsed = (...args) => {\n  console.groupCollapsed(...computeLoggingArgs(args))\n  /* c8 ignore next */\n  vconsoles.forEach((vc) => vc.groupCollapsed(args))\n}\n\nexport const groupEnd = () => {\n  console.groupEnd()\n  /* c8 ignore next */\n  vconsoles.forEach((vc) => vc.groupEnd())\n}\n\n/**\n * @param {function():Node} createNode\n */\n/* c8 ignore next 2 */\nexport const printDom = (createNode) =>\n  vconsoles.forEach((vc) => vc.printDom(createNode()))\n\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {number} height\n */\n/* c8 ignore next 2 */\nexport const printCanvas = (canvas, height) =>\n  printImg(canvas.toDataURL(), height)\n\nexport const vconsoles = set.create()\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n * @return {Array<Element>}\n */\n/* c8 ignore start */\nconst _computeLineSpans = (args) => {\n  const spans = []\n  const currentStyle = new Map()\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    let arg = args[i]\n    // @ts-ignore\n    const style = _browserStyleMap[arg]\n    if (style !== undefined) {\n      currentStyle.set(style.left, style.right)\n    } else {\n      if (arg === undefined) {\n        arg = 'undefined '\n      }\n      if (arg.constructor === String || arg.constructor === Number) {\n        // @ts-ignore\n        const span = dom.element('span', [\n          pair.create('style', dom.mapToStyleString(currentStyle))\n        ], [dom.text(arg.toString())])\n        if (span.innerHTML === '') {\n          span.innerHTML = '&nbsp;'\n        }\n        spans.push(span)\n      } else {\n        break\n      }\n    }\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    let content = args[i]\n    if (!(content instanceof Symbol)) {\n      if (content.constructor !== String && content.constructor !== Number) {\n        content = ' ' + json.stringify(content) + ' '\n      }\n      spans.push(\n        dom.element('span', [], [dom.text(/** @type {string} */ (content))])\n      )\n    }\n  }\n  return spans\n}\n/* c8 ignore stop */\n\nconst lineStyle =\n  'font-family:monospace;border-bottom:1px solid #e2e2e2;padding:2px;'\n\n/* c8 ignore start */\nexport class VConsole {\n  /**\n   * @param {Element} dom\n   */\n  constructor (dom) {\n    this.dom = dom\n    /**\n     * @type {Element}\n     */\n    this.ccontainer = this.dom\n    this.depth = 0\n    vconsoles.add(this)\n  }\n\n  /**\n   * @param {Array<string|Symbol|Object|number>} args\n   * @param {boolean} collapsed\n   */\n  group (args, collapsed = false) {\n    eventloop.enqueue(() => {\n      const triangleDown = dom.element('span', [\n        pair.create('hidden', collapsed),\n        pair.create('style', 'color:grey;font-size:120%;')\n      ], [dom.text('▼')])\n      const triangleRight = dom.element('span', [\n        pair.create('hidden', !collapsed),\n        pair.create('style', 'color:grey;font-size:125%;')\n      ], [dom.text('▶')])\n      const content = dom.element(\n        'div',\n        [pair.create(\n          'style',\n          `${lineStyle};padding-left:${this.depth * 10}px`\n        )],\n        [triangleDown, triangleRight, dom.text(' ')].concat(\n          _computeLineSpans(args)\n        )\n      )\n      const nextContainer = dom.element('div', [\n        pair.create('hidden', collapsed)\n      ])\n      const nextLine = dom.element('div', [], [content, nextContainer])\n      dom.append(this.ccontainer, [nextLine])\n      this.ccontainer = nextContainer\n      this.depth++\n      // when header is clicked, collapse/uncollapse container\n      dom.addEventListener(content, 'click', (_event) => {\n        nextContainer.toggleAttribute('hidden')\n        triangleDown.toggleAttribute('hidden')\n        triangleRight.toggleAttribute('hidden')\n      })\n    })\n  }\n\n  /**\n   * @param {Array<string|Symbol|Object|number>} args\n   */\n  groupCollapsed (args) {\n    this.group(args, true)\n  }\n\n  groupEnd () {\n    eventloop.enqueue(() => {\n      if (this.depth > 0) {\n        this.depth--\n        // @ts-ignore\n        this.ccontainer = this.ccontainer.parentElement.parentElement\n      }\n    })\n  }\n\n  /**\n   * @param {Array<string|Symbol|Object|number>} args\n   */\n  print (args) {\n    eventloop.enqueue(() => {\n      dom.append(this.ccontainer, [\n        dom.element('div', [\n          pair.create(\n            'style',\n            `${lineStyle};padding-left:${this.depth * 10}px`\n          )\n        ], _computeLineSpans(args))\n      ])\n    })\n  }\n\n  /**\n   * @param {Error} err\n   */\n  printError (err) {\n    this.print([common.RED, common.BOLD, err.toString()])\n  }\n\n  /**\n   * @param {string} url\n   * @param {number} height\n   */\n  printImg (url, height) {\n    eventloop.enqueue(() => {\n      dom.append(this.ccontainer, [\n        dom.element('img', [\n          pair.create('src', url),\n          pair.create('height', `${math.round(height * 1.5)}px`)\n        ])\n      ])\n    })\n  }\n\n  /**\n   * @param {Node} node\n   */\n  printDom (node) {\n    eventloop.enqueue(() => {\n      dom.append(this.ccontainer, [node])\n    })\n  }\n\n  destroy () {\n    eventloop.enqueue(() => {\n      vconsoles.delete(this)\n    })\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @param {Element} dom\n */\n/* c8 ignore next */\nexport const createVConsole = (dom) => new VConsole(dom)\n\n/**\n * @param {string} moduleName\n * @return {function(...any):void}\n */\nexport const createModuleLogger = (moduleName) => common.createModuleLogger(print, moduleName)\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAIA;;CAEC,GACD,MAAM,mBAAmB;IACvB,CAAC,+LAAA,CAAA,OAAW,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,eAAe;IAC1C,CAAC,+LAAA,CAAA,SAAa,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,eAAe;IAC5C,CAAC,+LAAA,CAAA,OAAW,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACpC,CAAC,+LAAA,CAAA,QAAY,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACrC,CAAC,+LAAA,CAAA,OAAW,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACpC,CAAC,+LAAA,CAAA,MAAU,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACnC,CAAC,+LAAA,CAAA,SAAa,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACtC,CAAC,+LAAA,CAAA,SAAa,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;IACtC,CAAC,+LAAA,CAAA,UAAc,CAAC,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;AACzC;AAEA;;;CAGC,GACD,mBAAmB,GACnB,MAAM,4BAA4B,CAAC;IACjC,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,gBAAgB,UAAU;QAC1D,OAA0D,uBAAuB,GAAG,AAAC,IAAK,CAAC,EAAE;IAC/F;IACA,MAAM,aAAa,EAAE;IACrB,MAAM,SAAS,EAAE;IACjB,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;IAC9B;;GAEC,GACD,IAAI,UAAU,EAAE;IAChB,0DAA0D;IAC1D,IAAI,IAAI;IACR,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,aAAa;QACb,MAAM,QAAQ,gBAAgB,CAAC,IAAI;QACnC,IAAI,UAAU,WAAW;YACvB,aAAa,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK;QAC1C,OAAO;YACL,IAAI,QAAQ,WAAW;gBACrB;YACF;YACA,IAAI,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,QAAQ;gBAC5D,MAAM,QAAQ,CAAA,GAAA,iLAAA,CAAA,mBAAoB,AAAD,EAAE;gBACnC,IAAI,IAAI,KAAK,MAAM,MAAM,GAAG,GAAG;oBAC7B,WAAW,IAAI,CAAC,OAAO;oBACvB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,WAAW,IAAI,CAAC;gBAClB;YACF,OAAO;gBACL;YACF;QACF;IACF;IACA,IAAI,IAAI,GAAG;QACT,0CAA0C;QAC1C,UAAU;QACV,QAAQ,OAAO,CAAC,WAAW,IAAI,CAAC;IAClC;IACA,kBAAkB;IAClB,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC,CAAC,eAAe,MAAM,GAAG;YAC5B,QAAQ,IAAI,CAAC;QACf;IACF;IACA,OAAO;AACT;AACA,kBAAkB,GAElB,mBAAmB,GACnB,MAAM,qBAAqB,yLAAA,CAAA,gBAAiB,GACxC,4BACA,+LAAA,CAAA,4BAAgC;AAM7B,MAAM,QAAQ,CAAC,GAAG;IACvB,QAAQ,GAAG,IAAI,mBAAmB;IAClC,kBAAkB,GAClB,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,KAAK,CAAC;AACrC;AAMO,MAAM,OAAO,CAAC,GAAG;IACtB,QAAQ,IAAI,IAAI,mBAAmB;IACnC,KAAK,OAAO,CAAC,+LAAA,CAAA,SAAa;IAC1B,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,KAAK,CAAC;AACrC;AAOO,MAAM,aAAa,CAAC;IACzB,QAAQ,KAAK,CAAC;IACd,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU,CAAC;AAC1C;AAQO,MAAM,WAAW,CAAC,KAAK;IAC5B,IAAI,yLAAA,CAAA,YAAa,EAAE;QACjB,QAAQ,GAAG,CACT,4BACA,CAAC,WAAW,EAAE,OAAO,kFAAkF,EAAE,IAAI,CAAC,CAAC;IAEjH,iGAAiG;IACnG;IACA,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK;AAC7C;AAQO,MAAM,iBAAiB,CAAC,QAAQ,SACrC,SAAS,CAAC,sBAAsB,EAAE,QAAQ,EAAE;AAKvC,MAAM,QAAQ,CAAC,GAAG;IACvB,QAAQ,KAAK,IAAI,mBAAmB;IACpC,kBAAkB,GAClB,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,KAAK,CAAC;AACrC;AAKO,MAAM,iBAAiB,CAAC,GAAG;IAChC,QAAQ,cAAc,IAAI,mBAAmB;IAC7C,kBAAkB,GAClB,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,cAAc,CAAC;AAC9C;AAEO,MAAM,WAAW;IACtB,QAAQ,QAAQ;IAChB,kBAAkB,GAClB,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,QAAQ;AACvC;AAMO,MAAM,WAAW,CAAC,aACvB,UAAU,OAAO,CAAC,CAAC,KAAO,GAAG,QAAQ,CAAC;AAOjC,MAAM,cAAc,CAAC,QAAQ,SAClC,SAAS,OAAO,SAAS,IAAI;AAExB,MAAM,YAAY,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;AAElC;;;CAGC,GACD,mBAAmB,GACnB,MAAM,oBAAoB,CAAC;IACzB,MAAM,QAAQ,EAAE;IAChB,MAAM,eAAe,IAAI;IACzB,0DAA0D;IAC1D,IAAI,IAAI;IACR,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,aAAa;QACb,MAAM,QAAQ,gBAAgB,CAAC,IAAI;QACnC,IAAI,UAAU,WAAW;YACvB,aAAa,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK;QAC1C,OAAO;YACL,IAAI,QAAQ,WAAW;gBACrB,MAAM;YACR;YACA,IAAI,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,QAAQ;gBAC5D,aAAa;gBACb,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,QAAQ;oBAC/B,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS,CAAA,GAAA,iLAAA,CAAA,mBAAoB,AAAD,EAAE;iBAC3C,EAAE;oBAAC,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAE,IAAI,QAAQ;iBAAI;gBAC7B,IAAI,KAAK,SAAS,KAAK,IAAI;oBACzB,KAAK,SAAS,GAAG;gBACnB;gBACA,MAAM,IAAI,CAAC;YACb,OAAO;gBACL;YACF;QACF;IACF;IACA,kBAAkB;IAClB,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QAC3B,IAAI,UAAU,IAAI,CAAC,EAAE;QACrB,IAAI,CAAC,CAAC,mBAAmB,MAAM,GAAG;YAChC,IAAI,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,KAAK,QAAQ;gBACpE,UAAU,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAc,AAAD,EAAE,WAAW;YAC5C;YACA,MAAM,IAAI,CACR,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,EAAE,EAAE;gBAAC,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAyB;aAAU;QAEvE;IACF;IACA,OAAO;AACT;AACA,kBAAkB,GAElB,MAAM,YACJ;AAGK,MAAM;IACX;;GAEC,GACD,YAAa,GAAG,CAAE;QAChB,IAAI,CAAC,GAAG,GAAG;QACX;;KAEC,GACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,UAAU,GAAG,CAAC,IAAI;IACpB;IAEA;;;GAGC,GACD,MAAO,IAAI,EAAE,YAAY,KAAK,EAAE;QAC9B,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,QAAQ;gBACvC,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,UAAU;gBACtB,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;aACtB,EAAE;gBAAC,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAE;aAAK;YAClB,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,QAAQ;gBACxC,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,UAAU,CAAC;gBACvB,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,SAAS;aACtB,EAAE;gBAAC,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAE;aAAK;YAClB,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EACxB,OACA;gBAAC,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EACT,SACA,GAAG,UAAU,cAAc,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;aAChD,EACF;gBAAC;gBAAc;gBAAe,CAAA,GAAA,iLAAA,CAAA,OAAQ,AAAD,EAAE;aAAK,CAAC,MAAM,CACjD,kBAAkB;YAGtB,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,OAAO;gBACvC,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,UAAU;aACvB;YACD,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,OAAO,EAAE,EAAE;gBAAC;gBAAS;aAAc;YAChE,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;gBAAC;aAAS;YACtC,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,CAAC,KAAK;YACV,wDAAwD;YACxD,CAAA,GAAA,iLAAA,CAAA,mBAAoB,AAAD,EAAE,SAAS,SAAS,CAAC;gBACtC,cAAc,eAAe,CAAC;gBAC9B,aAAa,eAAe,CAAC;gBAC7B,cAAc,eAAe,CAAC;YAChC;QACF;IACF;IAEA;;GAEC,GACD,eAAgB,IAAI,EAAE;QACpB,IAAI,CAAC,KAAK,CAAC,MAAM;IACnB;IAEA,WAAY;QACV,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;gBAClB,IAAI,CAAC,KAAK;gBACV,aAAa;gBACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa;YAC/D;QACF;IACF;IAEA;;GAEC,GACD,MAAO,IAAI,EAAE;QACX,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC1B,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,OAAO;oBACjB,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EACR,SACA,GAAG,UAAU,cAAc,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;iBAEnD,EAAE,kBAAkB;aACtB;QACH;IACF;IAEA;;GAEC,GACD,WAAY,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,CAAC;YAAC,+LAAA,CAAA,MAAU;YAAE,+LAAA,CAAA,OAAW;YAAE,IAAI,QAAQ;SAAG;IACtD;IAEA;;;GAGC,GACD,SAAU,GAAG,EAAE,MAAM,EAAE;QACrB,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC1B,CAAA,GAAA,iLAAA,CAAA,UAAW,AAAD,EAAE,OAAO;oBACjB,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,OAAO;oBACnB,CAAA,GAAA,kLAAA,CAAA,SAAW,AAAD,EAAE,UAAU,GAAG,CAAA,GAAA,kLAAA,CAAA,QAAU,AAAD,EAAE,SAAS,KAAK,EAAE,CAAC;iBACtD;aACF;QACH;IACF;IAEA;;GAEC,GACD,SAAU,IAAI,EAAE;QACd,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;gBAAC;aAAK;QACpC;IACF;IAEA,UAAW;QACT,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE;YAChB,UAAU,MAAM,CAAC,IAAI;QACvB;IACF;AACF;AAOO,MAAM,iBAAiB,CAAC,MAAQ,IAAI,SAAS;AAM7C,MAAM,qBAAqB,CAAC,aAAe,CAAA,GAAA,+LAAA,CAAA,qBAAyB,AAAD,EAAE,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/iterator.js"], "sourcesContent": ["/**\n * Utility module to create and manipulate Iterators.\n *\n * @module iterator\n */\n\n/**\n * @template T,R\n * @param {Iterator<T>} iterator\n * @param {function(T):R} f\n * @return {IterableIterator<R>}\n */\nexport const mapIterator = (iterator, f) => ({\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next () {\n    const r = iterator.next()\n    return { value: r.done ? undefined : f(r.value), done: r.done }\n  }\n})\n\n/**\n * @template T\n * @param {function():IteratorResult<T>} next\n * @return {IterableIterator<T>}\n */\nexport const createIterator = next => ({\n  /**\n   * @return {IterableIterator<T>}\n   */\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next\n})\n\n/**\n * @template T\n * @param {Iterator<T>} iterator\n * @param {function(T):boolean} filter\n */\nexport const iteratorFilter = (iterator, filter) => createIterator(() => {\n  let res\n  do {\n    res = iterator.next()\n  } while (!res.done && !filter(res.value))\n  return res\n})\n\n/**\n * @template T,M\n * @param {Iterator<T>} iterator\n * @param {function(T):M} fmap\n */\nexport const iteratorMap = (iterator, fmap) => createIterator(() => {\n  const { done, value } = iterator.next()\n  return { done, value: done ? undefined : fmap(value) }\n})\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;CAKC;;;;;;AACM,MAAM,cAAc,CAAC,UAAU,IAAM,CAAC;QAC3C,CAAC,OAAO,QAAQ,CAAC;YACf,OAAO,IAAI;QACb;QACA,aAAa;QACb;YACE,MAAM,IAAI,SAAS,IAAI;YACvB,OAAO;gBAAE,OAAO,EAAE,IAAI,GAAG,YAAY,EAAE,EAAE,KAAK;gBAAG,MAAM,EAAE,IAAI;YAAC;QAChE;IACF,CAAC;AAOM,MAAM,iBAAiB,CAAA,OAAQ,CAAC;QACrC;;GAEC,GACD,CAAC,OAAO,QAAQ,CAAC;YACf,OAAO,IAAI;QACb;QACA,aAAa;QACb;IACF,CAAC;AAOM,MAAM,iBAAiB,CAAC,UAAU,SAAW,eAAe;QACjE,IAAI;QACJ,GAAG;YACD,MAAM,SAAS,IAAI;QACrB,QAAS,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;QACzC,OAAO;IACT;AAOO,MAAM,cAAc,CAAC,UAAU,OAAS,eAAe;QAC5D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;QACrC,OAAO;YAAE;YAAM,OAAO,OAAO,YAAY,KAAK;QAAO;IACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3227, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/mutex.js"], "sourcesContent": ["/**\n * Mutual exclude for JavaScript.\n *\n * @module mutex\n */\n\n/**\n * @callback mutex\n * @param {function():void} cb Only executed when this mutex is not in the current stack\n * @param {function():void} [elseCb] Executed when this mutex is in the current stack\n */\n\n/**\n * Creates a mutual exclude function with the following property:\n *\n * ```js\n * const mutex = createMutex()\n * mutex(() => {\n *   // This function is immediately executed\n *   mutex(() => {\n *     // This function is not executed, as the mutex is already active.\n *   })\n * })\n * ```\n *\n * @return {mutex} A mutual exclude function\n * @public\n */\nexport const createMutex = () => {\n  let token = true\n  return (f, g) => {\n    if (token) {\n      token = false\n      try {\n        f()\n      } finally {\n        token = true\n      }\n    } else if (g !== undefined) {\n      g()\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;CAeC;;;AACM,MAAM,cAAc;IACzB,IAAI,QAAQ;IACZ,OAAO,CAAC,GAAG;QACT,IAAI,OAAO;YACT,QAAQ;YACR,IAAI;gBACF;YACF,SAAU;gBACR,QAAQ;YACV;QACF,OAAO,IAAI,MAAM,WAAW;YAC1B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3274, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/diff.js"], "sourcesContent": ["/**\n * Efficient diffs.\n *\n * @module diff\n */\n\nimport { equalityStrict } from './function.js'\n\n/**\n * A SimpleDiff describes a change on a String.\n *\n * ```js\n * console.log(a) // the old value\n * console.log(b) // the updated value\n * // Apply changes of diff (pseudocode)\n * a.remove(diff.index, diff.remove) // Remove `diff.remove` characters\n * a.insert(diff.index, diff.insert) // Insert `diff.insert`\n * a === b // values match\n * ```\n *\n * @typedef {Object} SimpleDiff\n * @property {Number} index The index where changes were applied\n * @property {Number} remove The number of characters to delete starting\n *                                  at `index`.\n * @property {T} insert The new text to insert at `index` after applying\n *                           `delete`\n *\n * @template T\n */\n\nconst highSurrogateRegex = /[\\uD800-\\uDBFF]/\nconst lowSurrogateRegex = /[\\uDC00-\\uDFFF]/\n\n/**\n * Create a diff between two strings. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * @function\n *\n * @param {string} a The old version of the string\n * @param {string} b The updated version of the string\n * @return {SimpleDiff<string>} The diff description.\n */\nexport const simpleDiffString = (a, b) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && a[left] === b[left]) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  while (right + left < a.length && right + left < b.length && a[a.length - right - 1] === b[b.length - right - 1]) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * @todo Remove in favor of simpleDiffString\n * @deprecated\n */\nexport const simpleDiff = simpleDiffString\n\n/**\n * Create a diff between two arrays. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * Note: This is basically the same function as above. Another function was created so that the runtime\n * can better optimize these function calls.\n *\n * @function\n * @template T\n *\n * @param {Array<T>} a The old version of the array\n * @param {Array<T>} b The updated version of the array\n * @param {function(T, T):boolean} [compare]\n * @return {SimpleDiff<Array<T>>} The diff description.\n */\nexport const simpleDiffArray = (a, b, compare = equalityStrict) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && compare(a[left], b[left])) {\n    left++\n  }\n  while (right + left < a.length && right + left < b.length && compare(a[a.length - right - 1], b[b.length - right - 1])) {\n    right++\n  }\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * Diff text and try to diff at the current cursor position.\n *\n * @param {string} a\n * @param {string} b\n * @param {number} cursor This should refer to the current left cursor-range position\n */\nexport const simpleDiffStringWithCursor = (a, b, cursor) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  // Iterate left to the right until we find a changed character\n  // First iteration considers the current cursor position\n  while (\n    left < a.length &&\n    left < b.length &&\n    a[left] === b[left] &&\n    left < cursor\n  ) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  // Iterate right to the left until we find a changed character\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[a.length - right - 1] === b[b.length - right - 1]\n  ) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  // Try to iterate left further to the right without caring about the current cursor position\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[left] === b[left]\n  ) {\n    left++\n  }\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GAED,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAYnB,MAAM,mBAAmB,CAAC,GAAG;IAClC,IAAI,OAAO,EAAE,+CAA+C;;IAC5D,IAAI,QAAQ,EAAE,gDAAgD;;IAC9D,MAAO,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAE;QAChE;IACF;IACA,gGAAgG;IAChG,IAAI,OAAO,KAAK,mBAAmB,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG;IACtD,MAAO,QAAQ,OAAO,EAAE,MAAM,IAAI,QAAQ,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,CAAE;QAChH;IACF;IACA,+FAA+F;IAC/F,IAAI,QAAQ,KAAK,kBAAkB,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG;IAC9D,OAAO;QACL,OAAO;QACP,QAAQ,EAAE,MAAM,GAAG,OAAO;QAC1B,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG;IACnC;AACF;AAMO,MAAM,aAAa;AAiBnB,MAAM,kBAAkB,CAAC,GAAG,GAAG,UAAU,sLAAA,CAAA,iBAAc;IAC5D,IAAI,OAAO,EAAE,+CAA+C;;IAC5D,IAAI,QAAQ,EAAE,gDAAgD;;IAC9D,MAAO,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,IAAI,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAG;QACtE;IACF;IACA,MAAO,QAAQ,OAAO,EAAE,MAAM,IAAI,QAAQ,OAAO,EAAE,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,EAAG;QACtH;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,EAAE,MAAM,GAAG,OAAO;QAC1B,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG;IACnC;AACF;AASO,MAAM,6BAA6B,CAAC,GAAG,GAAG;IAC/C,IAAI,OAAO,EAAE,+CAA+C;;IAC5D,IAAI,QAAQ,EAAE,gDAAgD;;IAC9D,8DAA8D;IAC9D,wDAAwD;IACxD,MACE,OAAO,EAAE,MAAM,IACf,OAAO,EAAE,MAAM,IACf,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,IACnB,OAAO,OACP;QACA;IACF;IACA,gGAAgG;IAChG,IAAI,OAAO,KAAK,mBAAmB,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG;IACtD,8DAA8D;IAC9D,MACE,QAAQ,OAAO,EAAE,MAAM,IACvB,QAAQ,OAAO,EAAE,MAAM,IACvB,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,CACnD;QACA;IACF;IACA,+FAA+F;IAC/F,IAAI,QAAQ,KAAK,kBAAkB,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG;IAC9D,4FAA4F;IAC5F,MACE,QAAQ,OAAO,EAAE,MAAM,IACvB,QAAQ,OAAO,EAAE,MAAM,IACvB,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CACnB;QACA;IACF;IACA,IAAI,OAAO,KAAK,mBAAmB,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG;IACtD,OAAO;QACL,OAAO;QACP,QAAQ,EAAE,MAAM,GAAG,OAAO;QAC1B,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG;IACnC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/lib0%400.2.109/node_modules/lib0/hash/sha256.js"], "sourcesContent": ["/**\n * @module sha256\n * Spec: https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\n * Resources:\n * - https://web.archive.org/web/20150315061807/http://csrc.nist.gov/groups/STM/cavp/documents/shs/sha256-384-512.pdf\n */\n\nimport * as binary from '../binary.js'\n\n/**\n * @param {number} w - a 32bit uint\n * @param {number} shift\n */\nconst rotr = (w, shift) => (w >>> shift) | (w << (32 - shift))\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sum0to256 = x => rotr(x, 2) ^ rotr(x, 13) ^ rotr(x, 22)\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sum1to256 = x => rotr(x, 6) ^ rotr(x, 11) ^ rotr(x, 25)\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sigma0to256 = x => rotr(x, 7) ^ rotr(x, 18) ^ x >>> 3\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sigma1to256 = x => rotr(x, 17) ^ rotr(x, 19) ^ x >>> 10\n\n// @todo don't init these variables globally\n\n/**\n * See 4.2.2: Constant for sha256 & sha224\n * These words represent the first thirty-two bits of the fractional parts of\n * the cube roots of the first sixty-four prime numbers. In hex, these constant words are (from left to\n * right)\n */\nconst K = new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n])\n\n/**\n * See 5.3.3. Initial hash value.\n *\n * These words were obtained by taking the first thirty-two bits of the fractional parts of the\n * square roots of the first eight prime numbers.\n *\n * @todo shouldn't be a global variable\n */\nconst HINIT = new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n])\n\n// time to beat: (large value < 4.35s)\n\nclass Hasher {\n  constructor () {\n    const buf = new ArrayBuffer(64 + 64 * 4)\n    // Init working variables using a single arraybuffer\n    this._H = new Uint32Array(buf, 0, 8)\n    this._H.set(HINIT)\n    // \"Message schedule\" - a working variable\n    this._W = new Uint32Array(buf, 64, 64)\n  }\n\n  _updateHash () {\n    const H = this._H\n    const W = this._W\n    for (let t = 16; t < 64; t++) {\n      W[t] = sigma1to256(W[t - 2]) + W[t - 7] + sigma0to256(W[t - 15]) + W[t - 16]\n    }\n    let a = H[0]\n    let b = H[1]\n    let c = H[2]\n    let d = H[3]\n    let e = H[4]\n    let f = H[5]\n    let g = H[6]\n    let h = H[7]\n    for (let tt = 0, T1, T2; tt < 64; tt++) {\n      T1 = (h + sum1to256(e) + ((e & f) ^ (~e & g)) + K[tt] + W[tt]) >>> 0\n      T2 = (sum0to256(a) + ((a & b) ^ (a & c) ^ (b & c))) >>> 0\n      h = g\n      g = f\n      f = e\n      e = (d + T1) >>> 0\n      d = c\n      c = b\n      b = a\n      a = (T1 + T2) >>> 0\n    }\n    H[0] += a\n    H[1] += b\n    H[2] += c\n    H[3] += d\n    H[4] += e\n    H[5] += f\n    H[6] += g\n    H[7] += h\n  }\n\n  /**\n   * Returns a 32-byte hash.\n   *\n   * @param {Uint8Array} data\n   */\n  digest (data) {\n    let i = 0\n    for (; i + 56 <= data.length;) {\n      // write data in big endianess\n      let j = 0\n      for (; j < 16 && i + 3 < data.length; j++) {\n        this._W[j] = data[i++] << 24 | data[i++] << 16 | data[i++] << 8 | data[i++]\n      }\n      if (i % 64 !== 0) { // there is still room to write partial content and the ending bit.\n        this._W.fill(0, j, 16)\n        while (i < data.length) {\n          this._W[j] |= data[i] << ((3 - (i % 4)) * 8)\n          i++\n        }\n        this._W[j] |= binary.BIT8 << ((3 - (i % 4)) * 8)\n      }\n      this._updateHash()\n    }\n    // same check as earlier - the ending bit has been written\n    const isPaddedWith1 = i % 64 !== 0\n    this._W.fill(0, 0, 16)\n    let j = 0\n    for (; i < data.length; j++) {\n      for (let ci = 3; ci >= 0 && i < data.length; ci--) {\n        this._W[j] |= data[i++] << (ci * 8)\n      }\n    }\n    // Write padding of the message. See 5.1.2.\n    if (!isPaddedWith1) {\n      this._W[j - (i % 4 === 0 ? 0 : 1)] |= binary.BIT8 << ((3 - (i % 4)) * 8)\n    }\n    // write length of message (size in bits) as 64 bit uint\n    // @todo test that this works correctly\n    this._W[14] = data.byteLength / binary.BIT30 // same as data.byteLength >>> 30 - but works on floats\n    this._W[15] = data.byteLength * 8\n    this._updateHash()\n    // correct H endianness to use big endiannes and return a Uint8Array\n    const dv = new Uint8Array(32)\n    for (let i = 0; i < this._H.length; i++) {\n      for (let ci = 0; ci < 4; ci++) {\n        dv[i * 4 + ci] = this._H[i] >>> (3 - ci) * 8\n      }\n    }\n    return dv\n  }\n}\n\n/**\n * Returns a 32-byte hash.\n *\n * @param {Uint8Array} data\n */\nexport const digest = data => new Hasher().digest(data)\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;;AAEA;;;CAGC,GACD,MAAM,OAAO,CAAC,GAAG,QAAU,AAAC,MAAM,QAAU,KAAM,KAAK;AAEvD;;;CAGC,GACD,MAAM,YAAY,CAAA,IAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG;AAE1D;;;CAGC,GACD,MAAM,YAAY,CAAA,IAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG;AAE1D;;;CAGC,GACD,MAAM,cAAc,CAAA,IAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,MAAM;AAE1D;;;CAGC,GACD,MAAM,cAAc,CAAA,IAAK,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,MAAM;AAE3D,4CAA4C;AAE5C;;;;;CAKC,GACD,MAAM,IAAI,IAAI,YAAY;IACxB;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IACpF;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;CACrF;AAED;;;;;;;CAOC,GACD,MAAM,QAAQ,IAAI,YAAY;IAC5B;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;IAAY;CACrF;AAED,sCAAsC;AAEtC,MAAM;IACJ,aAAe;QACb,MAAM,MAAM,IAAI,YAAY,KAAK,KAAK;QACtC,oDAAoD;QACpD,IAAI,CAAC,EAAE,GAAG,IAAI,YAAY,KAAK,GAAG;QAClC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;QACZ,0CAA0C;QAC1C,IAAI,CAAC,EAAE,GAAG,IAAI,YAAY,KAAK,IAAI;IACrC;IAEA,cAAe;QACb,MAAM,IAAI,IAAI,CAAC,EAAE;QACjB,MAAM,IAAI,IAAI,CAAC,EAAE;QACjB,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAK;YAC5B,CAAC,CAAC,EAAE,GAAG,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,YAAY,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG;QAC9E;QACA,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,IAAK,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,KAAM;YACtC,KAAK,AAAC,IAAI,UAAU,KAAK,CAAC,AAAC,IAAI,IAAM,CAAC,IAAI,CAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM;YACnE,KAAK,AAAC,UAAU,KAAK,CAAC,AAAC,IAAI,IAAM,IAAI,IAAM,IAAI,CAAE,MAAO;YACxD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI,AAAC,IAAI,OAAQ;YACjB,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI,AAAC,KAAK,OAAQ;QACpB;QACA,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,EAAE,IAAI;IACV;IAEA;;;;GAIC,GACD,OAAQ,IAAI,EAAE;QACZ,IAAI,IAAI;QACR,MAAO,IAAI,MAAM,KAAK,MAAM,EAAG;YAC7B,8BAA8B;YAC9B,IAAI,IAAI;YACR,MAAO,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE,IAAK;gBACzC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;YAC7E;YACA,IAAI,IAAI,OAAO,GAAG;gBAChB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG;gBACnB,MAAO,IAAI,KAAK,MAAM,CAAE;oBACtB,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAK,CAAC,IAAK,IAAI,CAAE,IAAI;oBAC1C;gBACF;gBACA,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,oLAAA,CAAA,OAAW,IAAK,CAAC,IAAK,IAAI,CAAE,IAAI;YAChD;YACA,IAAI,CAAC,WAAW;QAClB;QACA,0DAA0D;QAC1D,MAAM,gBAAgB,IAAI,OAAO;QACjC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG;QACnB,IAAI,IAAI;QACR,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;YAC3B,IAAK,IAAI,KAAK,GAAG,MAAM,KAAK,IAAI,KAAK,MAAM,EAAE,KAAM;gBACjD,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,IAAK,KAAK;YACnC;QACF;QACA,2CAA2C;QAC3C,IAAI,CAAC,eAAe;YAClB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,IAAI,oLAAA,CAAA,OAAW,IAAK,CAAC,IAAK,IAAI,CAAE,IAAI;QACxE;QACA,wDAAwD;QACxD,uCAAuC;QACvC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,UAAU,GAAG,qLAAa,uDAAuD;QAApE,CAAA,QAAY;QAC5C,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,UAAU,GAAG;QAChC,IAAI,CAAC,WAAW;QAChB,oEAAoE;QACpE,MAAM,KAAK,IAAI,WAAW;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;YACvC,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAM;gBAC7B,EAAE,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI;YAC7C;QACF;QACA,OAAO;IACT;AACF;AAOO,MAAM,SAAS,CAAA,OAAQ,IAAI,SAAS,MAAM,CAAC", "ignoreList": [0], "debugId": null}}]}