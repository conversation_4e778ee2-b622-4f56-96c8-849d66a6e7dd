{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,wbAAC,8XAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,uPAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,+YAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,wbAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,wbAAC,uPAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,+YAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,+YAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,+YAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,+YAAA,CAAA,QAAW,AAAD;IAErB,qBACE,wbAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,wbAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,wbAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,wbAAC,oSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,wbAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,wbAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/editor/blocknote-editor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useMemo } from 'react';\nimport { BlockNoteEditor, PartialBlock } from '@blocknote/core';\nimport { BlockNoteView } from '@blocknote/mantine';\nimport { useCreateBlockNote } from '@blocknote/react';\nimport '@blocknote/core/fonts/inter.css';\nimport '@blocknote/mantine/style.css';\n\ninterface BlockNoteEditorProps {\n  /**\n   * Initial content for the editor\n   */\n  initialContent?: PartialBlock[];\n  \n  /**\n   * Called when the editor content changes\n   */\n  onChange?: (blocks: PartialBlock[]) => void;\n  \n  /**\n   * Whether the editor is editable\n   */\n  editable?: boolean;\n  \n  /**\n   * Placeholder text to display when editor is empty\n   */\n  placeholder?: string;\n  \n  /**\n   * Additional CSS classes\n   */\n  className?: string;\n}\n\nexport function BlockNoteEditorComponent({\n  initialContent,\n  onChange,\n  editable = true,\n  placeholder = \"Эчтәлек языгыз...\",\n  className = \"\",\n}: BlockNoteEditorProps) {\n  // Create the editor instance\n  const editor = useCreateBlockNote({\n    initialContent,\n  });\n\n  // Handle content changes\n  const handleChange = () => {\n    if (onChange) {\n      onChange(editor.document);\n    }\n  };\n\n  return (\n    <div className={`blocknote-editor ${className}`}>\n      <BlockNoteView\n        editor={editor}\n        onChange={handleChange}\n        editable={editable}\n      />\n    </div>\n  );\n}\n\n// Export a simpler interface for form usage\nexport interface EditorFieldProps {\n  /**\n   * Current value of the editor (array of blocks)\n   */\n  value?: PartialBlock[];\n  \n  /**\n   * Called when the editor value changes\n   */\n  onChange?: (value: PartialBlock[]) => void;\n  \n  /**\n   * Placeholder text to display when editor is empty\n   */\n  placeholder?: string;\n  \n  /**\n   * Additional CSS classes\n   */\n  className?: string;\n}\n\nexport function BlockNoteEditorField({\n  value,\n  onChange,\n  placeholder = \"Эчтәлек языгыз...\",\n  className = \"\",\n}: EditorFieldProps) {\n  // Ensure we have a default value\n  const initialContent = useMemo(() => {\n    if (value && value.length > 0) {\n      return value;\n    }\n    return [\n      {\n        type: 'paragraph',\n        content: [],\n      },\n    ] as PartialBlock[];\n  }, [value]);\n\n  return (\n    <BlockNoteEditorComponent\n      initialContent={initialContent}\n      onChange={onChange}\n      placeholder={placeholder}\n      className={className}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;;;AAoCO,SAAS,yBAAyB,EACvC,cAAc,EACd,QAAQ,EACR,WAAW,IAAI,EACf,cAAc,mBAAmB,EACjC,YAAY,EAAE,EACO;IACrB,6BAA6B;IAC7B,MAAM,SAAS,CAAA,GAAA,qXAAA,CAAA,qBAAkB,AAAD,EAAE;QAChC;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,OAAO,QAAQ;QAC1B;IACF;IAEA,qBACE,wbAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;kBAC7C,cAAA,wbAAC,mXAAA,CAAA,gBAAa;YACZ,QAAQ;YACR,UAAU;YACV,UAAU;;;;;;;;;;;AAIlB;AAyBO,SAAS,qBAAqB,EACnC,KAAK,EACL,QAAQ,EACR,cAAc,mBAAmB,EACjC,YAAY,EAAE,EACG;IACjB,iCAAiC;IACjC,MAAM,iBAAiB,CAAA,GAAA,+YAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,OAAO;QACT;QACA,OAAO;YACL;gBACE,MAAM;gBACN,SAAS,EAAE;YACb;SACD;IACH,GAAG;QAAC;KAAM;IAEV,qBACE,wbAAC;QACC,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,WAAW;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,wbAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,wbAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/api/works.ts"], "sourcesContent": ["import { getSession } from \"next-auth/react\";\r\nimport { gql } from \"graphql-tag\";\r\nimport {\r\n  GetWorksQuery,\r\n  GetWorksQueryVariables,\r\n  GetWorkQuery,\r\n  GetWorkQueryVariables,\r\n  CreateWorkMutation,\r\n  CreateWorkMutationVariables,\r\n  UpdateWorkMutation,\r\n  UpdateWorkMutationVariables,\r\n  DeleteWorkMutation,\r\n  DeleteWorkMutationVariables,\r\n  Work,\r\n  CreateWorkInput,\r\n  UpdateWorkInput\r\n} from \"@/lib/graphql/generated/graphql\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3556\";\r\n\r\nasync function fetchWithAuth(url: string, options: RequestInit = {}) {\r\n  const session = await getSession();\r\n\r\n  return fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...options.headers,\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${session?.accessToken}`,\r\n    },\r\n  });\r\n}\r\n\r\n// GraphQL запросы и мутации\r\nconst GET_WORKS = gql`\r\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\r\n    works(skip: $skip, take: $take, where: $where) {\r\n      items {\r\n        id\r\n        urlPart\r\n        authorId\r\n        title\r\n        publishedDate\r\n        modifiedDate\r\n      }\r\n      totalCount\r\n    }\r\n  }\r\n`;\r\n\r\nconst GET_WORK = gql`\r\n  query GetWork($id: String!) {\r\n    work(id: $id) {\r\n      id\r\n      urlPart\r\n      authorId\r\n      title\r\n      content\r\n      genres\r\n      comments\r\n      source\r\n      publishedDate\r\n      publishedBy\r\n      modifiedDate\r\n      modifiedBy\r\n    }\r\n  }\r\n`;\r\n\r\nconst CREATE_WORK = gql`\r\n  mutation CreateWork($input: CreateWorkInput!) {\r\n    createWork(input: $input) {\r\n      work {\r\n        id\r\n        urlPart\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst UPDATE_WORK = gql`\r\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\r\n    updateWork(id: $id, input: $input) {\r\n      work {\r\n        id\r\n        urlPart\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst DELETE_WORK = gql`\r\n  mutation DeleteWork($id: ID!) {\r\n    deleteWork(id: $id) {\r\n      success\r\n    }\r\n  }\r\n`;\r\n\r\nexport async function getWorks(\r\n  page: number = 1,\r\n  pageSize: number = 10,\r\n  filter?: string,\r\n  authorId?: string\r\n): Promise<{ works: Work[], totalCount: number }> {\r\n  const skip = (page - 1) * pageSize;\r\n\r\n  // Создаем объект фильтрации для GraphQL запроса\r\n  let where: any = undefined;\r\n\r\n  if (filter || authorId) {\r\n    where = {};\r\n\r\n    if (filter) {\r\n      where.title = { contains: filter };\r\n    }\r\n\r\n    if (authorId) {\r\n      where.authorId = { eq: authorId };\r\n    }\r\n  }\r\n\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_WORKS.loc?.source.body,\r\n      variables: { skip, take: pageSize, where }\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetWorksQuery;\r\n  return {\r\n    works: result.works.items as Work[],\r\n    totalCount: result.works.totalCount\r\n  };\r\n}\r\n\r\nexport async function getWork(id: string): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_WORK.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetWorkQuery;\r\n  return result.work as Work;\r\n}\r\n\r\nexport async function createWork(input: CreateWorkInput): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: CREATE_WORK.loc?.source.body,\r\n      variables: { input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as CreateWorkMutation;\r\n  return result.createWork.work as Work;\r\n}\r\n\r\nexport async function updateWork(id: string, input: UpdateWorkInput): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: UPDATE_WORK.loc?.source.body,\r\n      variables: { id, input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as UpdateWorkMutation;\r\n  return result.updateWork.work as Work;\r\n}\r\n\r\nexport async function deleteWork(id: string): Promise<boolean> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: DELETE_WORK.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as DeleteWorkMutation;\r\n  return result.deleteWork.success;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAiBA,MAAM,UAAU,6DAAmC;AAEnD,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACjE,MAAM,UAAU,MAAM,CAAA,GAAA,mWAAA,CAAA,aAAU,AAAD;IAE/B,OAAO,MAAM,KAAK;QAChB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,QAAQ,OAAO;YAClB,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,SAAS,aAAa;QACjD;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,YAAY,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;AActB,CAAC;AAED,MAAM,WAAW,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;AAiBrB,CAAC;AAED,MAAM,cAAc,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAUxB,CAAC;AAED,MAAM,cAAc,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAUxB,CAAC;AAED,MAAM,cAAc,8IAAA,CAAA,MAAG,CAAC;;;;;;AAMxB,CAAC;AAEM,eAAe,SACpB,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,MAAe,EACf,QAAiB;IAEjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,gDAAgD;IAChD,IAAI,QAAa;IAEjB,IAAI,UAAU,UAAU;QACtB,QAAQ,CAAC;QAET,IAAI,QAAQ;YACV,MAAM,KAAK,GAAG;gBAAE,UAAU;YAAO;QACnC;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;gBAAE,IAAI;YAAS;QAClC;IACF;IAEA,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,UAAU,GAAG,EAAE,OAAO;YAC7B,WAAW;gBAAE;gBAAM,MAAM;gBAAU;YAAM;QAC3C;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO;QACL,OAAO,OAAO,KAAK,CAAC,KAAK;QACzB,YAAY,OAAO,KAAK,CAAC,UAAU;IACrC;AACF;AAEO,eAAe,QAAQ,EAAU;IACtC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,SAAS,GAAG,EAAE,OAAO;YAC5B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,IAAI;AACpB;AAEO,eAAe,WAAW,KAAsB;IACrD,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;YAAM;QACrB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,IAAI;AAC/B;AAEO,eAAe,WAAW,EAAU,EAAE,KAAsB;IACjE,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;gBAAI;YAAM;QACzB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,IAAI;AAC/B;AAEO,eAAe,WAAW,EAAU;IACzC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,OAAO;AAClC", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/hooks/use-works.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getWorks, getWork, createWork, updateWork, deleteWork } from \"@/lib/api/works\";\r\nimport { Work, CreateWorkInput, UpdateWorkInput } from \"@/lib/graphql/generated/graphql\";\r\n\r\nexport function useWorks(page: number = 1, pageSize: number = 10, filter?: string, authorId?: string) {\r\n  return useQuery({\r\n    queryKey: [\"works\", { page, pageSize, filter, authorId }],\r\n    queryFn: () => getWorks(page, pageSize, filter, authorId),\r\n    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд\r\n    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель\r\n  });\r\n}\r\n\r\nexport function useWork(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"works\", id],\r\n    queryFn: () => getWork(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateWorkInput) => createWork(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: string; data: UpdateWorkInput }) =>\r\n      updateWork(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"works\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => deleteWork(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;AAGO,SAAS,SAAS,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe,EAAE,QAAiB;IAClG,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;gBAAE;gBAAM;gBAAU;gBAAQ;YAAS;SAAE;QACzD,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU,QAAQ;QAChD,WAAW;QACX,iBAAiB,CAAC,eAAiB;IACrC;AACF;AAEO,SAAS,QAAQ,EAAU;IAChC,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAG;QACvB,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE;QACvB,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA0B,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;QAClD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;QACtD;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAAyC,GAC9D,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,IAAI;QACjB,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;YACpD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAS,UAAU,EAAE;iBAAC;YAAC;QACpE;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;QACvC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;QACtD;IACF;AACF", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/schemas/work-schema.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const workSchema = z.object({\n  authorId: z.string().min(1, \"Автор сайлагыз\"),\n  title: z.string().min(1, \"Исем кирәк\"),\n  content: z.string().min(1, \"Эчтәлек кирәк\"),\n  genres: z.array(z.string()).optional(),\n  comments: z.string().optional(),\n  source: z.string().optional(),\n  publishedDate: z.string().optional(),\n});\n\nexport type WorkFormValues = z.infer<typeof workSchema>;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,UAAU,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,qLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,qLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACpC,UAAU,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,eAAe,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACpC", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/format-date.ts"], "sourcesContent": ["// Названия месяцев на татарском языке\nconst tatarMonths = [\n  'гыйнвар', // январь\n  'февраль', // февраль\n  'март', // март\n  'апрель', // апрель\n  'май', // май\n  'июнь', // июнь\n  'июль', // июль\n  'август', // август\n  'сентябрь', // сентябрь\n  'октябрь', // октябрь\n  'ноябрь', // ноябрь\n  'декабрь', // декабрь\n];\n\nexport function formatDate(dateString: string): string {\n  if (!dateString) return \"-\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"-\";\n  }\n\n  // Форматируем дату в формате \"день месяц год\"\n  const day = date.getDate();\n  const month = tatarMonths[date.getMonth()];\n  const year = date.getFullYear();\n\n  return `${day} ${month} ${year}`;\n}\n\nexport function formatDateForInput(dateString: string): string {\n  if (!dateString) return \"\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"\";\n  }\n\n  return date.toISOString().split(\"T\")[0];\n}\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,QAAQ,WAAW,CAAC,KAAK,QAAQ,GAAG;IAC1C,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAEO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/api/authors.ts"], "sourcesContent": ["import { getSession } from \"next-auth/react\";\r\nimport { gql } from \"graphql-tag\";\r\nimport {\r\n  GetAuthorsQuery,\r\n  GetAuthorsQueryVariables,\r\n  GetAuthorQuery,\r\n  GetAuthorQueryVariables,\r\n  CreateAuthorMutation,\r\n  CreateAuthorMutationVariables,\r\n  UpdateAuthorMutation,\r\n  UpdateAuthorMutationVariables,\r\n  DeleteAuthorMutation,\r\n  DeleteAuthorMutationVariables,\r\n  Author,\r\n  CreateAuthorInput,\r\n  UpdateAuthorInput\r\n} from \"@/lib/graphql/generated/graphql\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3556\";\r\n\r\nasync function fetchWithAuth(url: string, options: RequestInit = {}) {\r\n  const session = await getSession();\r\n\r\n  return fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...options.headers,\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${session?.accessToken}`,\r\n    },\r\n  });\r\n}\r\n\r\n// GraphQL запросы и мутации\r\nconst GET_AUTHORS = gql`\r\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\r\n    authors(skip: $skip, take: $take, where: $where) {\r\n      items {\r\n        id\r\n        urlPart\r\n        displayName\r\n        birthDate\r\n        deathDate\r\n        addedDate\r\n        modifiedDate\r\n      }\r\n      totalCount\r\n    }\r\n  }\r\n`;\r\n\r\nconst GET_AUTHOR = gql`\r\n  query GetAuthor($id: String!) {\r\n    author(id: $id) {\r\n      id\r\n      urlPart\r\n      name\r\n      surName\r\n      lastName\r\n      displayName\r\n      biography\r\n      birthDate\r\n      deathDate\r\n      addedDate\r\n      modifiedDate\r\n    }\r\n  }\r\n`;\r\n\r\nconst CREATE_AUTHOR = gql`\r\n  mutation CreateAuthor($input: CreateAuthorInput!) {\r\n    createAuthor(input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst UPDATE_AUTHOR = gql`\r\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\r\n    updateAuthor(id: $id, input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst DELETE_AUTHOR = gql`\r\n  mutation DeleteAuthor($id: String!) {\r\n    deleteAuthor(id: $id) {\r\n      success\r\n    }\r\n  }\r\n`;\r\n\r\nexport async function getAuthors(page: number = 1, pageSize: number = 10, filter?: string): Promise<{ authors: Author[], totalCount: number }> {\r\n  const skip = (page - 1) * pageSize;\r\n\r\n  // Создаем объект фильтрации для GraphQL запроса\r\n  const where = filter ? { displayName: { contains: filter } } : undefined;\r\n\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHORS.loc?.source.body,\r\n      variables: { skip, take: pageSize, where }\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorsQuery;\r\n  return {\r\n    authors: result.authors.items as Author[],\r\n    totalCount: result.authors.totalCount\r\n  };\r\n}\r\n\r\nexport async function getAuthor(id: string): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorQuery;\r\n  return result.author as Author;\r\n}\r\n\r\nexport async function createAuthor(input: CreateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: CREATE_AUTHOR.loc?.source.body,\r\n      variables: { input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as CreateAuthorMutation;\r\n  return result.createAuthor.author as Author;\r\n}\r\n\r\nexport async function updateAuthor(id: string, input: UpdateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: UPDATE_AUTHOR.loc?.source.body,\r\n      variables: { id, input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as UpdateAuthorMutation;\r\n  return result.updateAuthor.author as Author;\r\n}\r\n\r\nexport async function deleteAuthor(id: string): Promise<boolean> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: DELETE_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as DeleteAuthorMutation;\r\n  return result.deleteAuthor.success;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAiBA,MAAM,UAAU,6DAAmC;AAEnD,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACjE,MAAM,UAAU,MAAM,CAAA,GAAA,mWAAA,CAAA,aAAU,AAAD;IAE/B,OAAO,MAAM,KAAK;QAChB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,QAAQ,OAAO;YAClB,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,SAAS,aAAa;QACjD;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,cAAc,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;AAexB,CAAC;AAED,MAAM,aAAa,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;AAgBvB,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;AAM1B,CAAC;AAEM,eAAe,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;IACvF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,gDAAgD;IAChD,MAAM,QAAQ,SAAS;QAAE,aAAa;YAAE,UAAU;QAAO;IAAE,IAAI;IAE/D,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;gBAAM,MAAM;gBAAU;YAAM;QAC3C;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO;QACL,SAAS,OAAO,OAAO,CAAC,KAAK;QAC7B,YAAY,OAAO,OAAO,CAAC,UAAU;IACvC;AACF;AAEO,eAAe,UAAU,EAAU;IACxC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,WAAW,GAAG,EAAE,OAAO;YAC9B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,MAAM;AACtB;AAEO,eAAe,aAAa,KAAwB;IACzD,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAM;QACrB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU,EAAE,KAAwB;IACrE,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;gBAAI;YAAM;QACzB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,OAAO;AACpC", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/hooks/use-authors.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getAuthors, getAuthor, createAuthor, updateAuthor, deleteAuthor } from \"@/lib/api/authors\";\r\nimport { Author, CreateAuthorInput, UpdateAuthorInput } from \"@/lib/graphql/generated/graphql\";\r\n\r\nexport function useAuthors(page: number = 1, pageSize: number = 10, filter?: string) {\r\n  return useQuery({\r\n    queryKey: [\"authors\", { page, pageSize, filter }],\r\n    queryFn: () => getAuthors(page, pageSize, filter),\r\n    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд\r\n    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель\r\n  });\r\n}\r\n\r\nexport function useAuthor(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"author\", id],\r\n    queryFn: () => getAuthor(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateAuthorInput) => createAuthor(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: string; data: UpdateAuthorInput }) =>\r\n      updateAuthor(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => deleteAuthor(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;AAGO,SAAS,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;IACjF,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;gBAAE;gBAAM;gBAAU;YAAO;SAAE;QACjD,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;QAC1C,WAAW;QACX,iBAAiB,CAAC,eAAiB;IACrC;AACF;AAEO,SAAS,UAAU,EAAU;IAClC,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAU;SAAG;QACxB,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE;QACzB,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA4B,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;QACtD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE,IAAI;QACnB,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,UAAU,EAAE;iBAAC;YAAC;QACtE;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;QACzC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,0XAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,0XAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,+YAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,wbAAC,0XAAA,CAAA,SAAuB;kBACtB,cAAA,wbAAC,0XAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,0XAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/authors/author-select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useAuthors } from \"@/lib/hooks/use-authors\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\r\nimport { Check, ChevronsUpDown, Search, X } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface AuthorSelectProps {\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function AuthorSelect({ value, onChange, disabled }: AuthorSelectProps) {\r\n  const [authorSearchTerm, setAuthorSearchTerm] = useState(\"\");\r\n  const [debouncedAuthorSearchTerm, setDebouncedAuthorSearchTerm] = useState(\"\");\r\n  const [openAuthorPopover, setOpenAuthorPopover] = useState(false);\r\n  const [selectedAuthor, setSelectedAuthor] = useState<{ id: string, displayName: string } | null>(null);\r\n  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);\r\n  const authorSearchInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Дебаунсинг для поиска авторов\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedAuthorSearchTerm(authorSearchTerm);\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [authorSearchTerm]);\r\n\r\n  const { data: authorsData, isLoading: isLoadingAuthors } = useAuthors(1, 50, debouncedAuthorSearchTerm);\r\n  const authors = authorsData?.authors || [];\r\n\r\n  // Автоматический фокус на поле поиска автора при открытии поповера\r\n  useEffect(() => {\r\n    if (openAuthorPopover && authorSearchInputRef.current) {\r\n      setTimeout(() => {\r\n        authorSearchInputRef.current?.focus();\r\n      }, 100);\r\n    }\r\n  }, [openAuthorPopover]);\r\n\r\n  // Установка выбранного автора при изменении value\r\n  useEffect(() => {\r\n    if (value && authors.length > 0) {\r\n      const author = authors.find(a => a.id === value);\r\n      if (author) {\r\n        setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n      }\r\n    } else {\r\n      setSelectedAuthor(null);\r\n    }\r\n  }, [value, authors]);\r\n\r\n  return (\r\n    <Popover open={openAuthorPopover} onOpenChange={setOpenAuthorPopover}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={openAuthorPopover}\r\n          className=\"w-full justify-between\"\r\n          disabled={disabled || isLoadingAuthors}\r\n        >\r\n          {isLoadingAuthors ? (\r\n            \"Авторлар йөкләнә...\"\r\n          ) : value ? (\r\n            selectedAuthor?.displayName || authorsData?.authors.find((author) => author.id === value)?.displayName || \"Автор сайланган\"\r\n          ) : (\r\n            \"Автор сайлагыз\"\r\n          )}\r\n          <div className=\"flex\">\r\n            {value && (\r\n              <div\r\n                className=\"h-4 w-4 mr-1 flex items-center justify-center cursor-pointer rounded-sm hover:bg-accent hover:text-accent-foreground\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  onChange(\"\");\r\n                  setSelectedAuthor(null);\r\n                  setOpenAuthorPopover(false);\r\n                }}\r\n                style={{ opacity: isLoadingAuthors ? 0.5 : 1, pointerEvents: isLoadingAuthors ? 'none' : 'auto' }}\r\n              >\r\n                <X className=\"h-3 w-3\" />\r\n              </div>\r\n            )}\r\n            <ChevronsUpDown className=\"ml-1 h-4 w-4 shrink-0 opacity-50\" />\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-full p-0\" align=\"start\">\r\n        <div className=\"flex flex-col w-full\">\r\n          <div className=\"flex items-center border-b px-3\">\r\n            <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\r\n            <input\r\n              ref={authorSearchInputRef}\r\n              className=\"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground\"\r\n              placeholder=\"Автор эзләү...\"\r\n              value={authorSearchTerm}\r\n              onChange={(e) => {\r\n                setAuthorSearchTerm(e.target.value);\r\n                setHighlightedIndex(0);\r\n              }}\r\n              onKeyDown={(e) => {\r\n                if (!authors.length) return;\r\n\r\n                if (e.key === \"ArrowDown\") {\r\n                  e.preventDefault();\r\n                  setHighlightedIndex((prev) =>\r\n                    prev < authors.length - 1 ? prev + 1 : prev\r\n                  );\r\n                } else if (e.key === \"ArrowUp\") {\r\n                  e.preventDefault();\r\n                  setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : 0));\r\n                } else if (e.key === \"Enter\" && highlightedIndex >= 0) {\r\n                  e.preventDefault();\r\n                  const author = authors[highlightedIndex];\r\n                  if (author) {\r\n                    const newAuthorId = author.id === value ? \"\" : author.id;\r\n                    onChange(newAuthorId);\r\n                    if (newAuthorId) {\r\n                      setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n                    } else {\r\n                      setSelectedAuthor(null);\r\n                    }\r\n                    setAuthorSearchTerm(\"\");\r\n                    setOpenAuthorPopover(false);\r\n                  }\r\n                } else if (e.key === \"Escape\") {\r\n                  e.preventDefault();\r\n                  setOpenAuthorPopover(false);\r\n                }\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"max-h-[300px] overflow-y-auto overflow-x-hidden\">\r\n            {isLoadingAuthors ? (\r\n              <div className=\"py-6 text-center text-sm\">Авторлар йөкләнә...</div>\r\n            ) : authors.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm\">Авторлар табылмады</div>\r\n            ) : (\r\n              <div className=\"p-1\">\r\n                {authors.map((author, index) => (\r\n                  <div\r\n                    key={author.id}\r\n                    className={cn(\r\n                      \"relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground\",\r\n                      (value === author.id || index === highlightedIndex) && \"bg-accent text-accent-foreground\"\r\n                    )}\r\n                    onClick={() => {\r\n                      const newAuthorId = author.id === value ? \"\" : author.id;\r\n                      onChange(newAuthorId);\r\n                      if (newAuthorId) {\r\n                        setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n                      } else {\r\n                        setSelectedAuthor(null);\r\n                      }\r\n                      setAuthorSearchTerm(\"\");\r\n                      setOpenAuthorPopover(false);\r\n                    }}\r\n                    onMouseEnter={() => setHighlightedIndex(index)}\r\n                  >\r\n                    <Check\r\n                      className={cn(\r\n                        \"mr-2 h-4 w-4\",\r\n                        value === author.id ? \"opacity-100\" : \"opacity-0\"\r\n                      )}\r\n                    />\r\n                    {author.displayName}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;AAgBO,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAqB;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAA8C;IACjG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAU,CAAC;IAClE,MAAM,uBAAuB,CAAA,GAAA,+YAAA,CAAA,SAAM,AAAD,EAAoB;IAEtD,gCAAgC;IAChC,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,6BAA6B;QAC/B,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAiB;IAErB,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD,EAAE,GAAG,IAAI;IAC7E,MAAM,UAAU,aAAa,WAAW,EAAE;IAE1C,mEAAmE;IACnE,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB,qBAAqB,OAAO,EAAE;YACrD,WAAW;gBACT,qBAAqB,OAAO,EAAE;YAChC,GAAG;QACL;IACF,GAAG;QAAC;KAAkB;IAEtB,kDAAkD;IAClD,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ,MAAM,GAAG,GAAG;YAC/B,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1C,IAAI,QAAQ;gBACV,kBAAkB;oBAAE,IAAI,OAAO,EAAE;oBAAE,aAAa,OAAO,WAAW;gBAAC;YACrE;QACF,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,qBACE,wbAAC,mIAAA,CAAA,UAAO;QAAC,MAAM;QAAmB,cAAc;;0BAC9C,wbAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,wbAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;oBACV,UAAU,YAAY;;wBAErB,mBACC,wBACE,QACF,gBAAgB,eAAe,aAAa,QAAQ,KAAK,CAAC,SAAW,OAAO,EAAE,KAAK,QAAQ,eAAe,oBAE1G;sCAEF,wbAAC;4BAAI,WAAU;;gCACZ,uBACC,wbAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,SAAS;wCACT,kBAAkB;wCAClB,qBAAqB;oCACvB;oCACA,OAAO;wCAAE,SAAS,mBAAmB,MAAM;wCAAG,eAAe,mBAAmB,SAAS;oCAAO;8CAEhG,cAAA,wbAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAGjB,wbAAC,kTAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAIhC,wbAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAa,OAAM;0BAC3C,cAAA,wbAAC;oBAAI,WAAU;;sCACb,wbAAC;4BAAI,WAAU;;8CACb,wbAAC,0RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,wbAAC;oCACC,KAAK;oCACL,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC;wCACT,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAClC,oBAAoB;oCACtB;oCACA,WAAW,CAAC;wCACV,IAAI,CAAC,QAAQ,MAAM,EAAE;wCAErB,IAAI,EAAE,GAAG,KAAK,aAAa;4CACzB,EAAE,cAAc;4CAChB,oBAAoB,CAAC,OACnB,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;wCAE3C,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;4CAC9B,EAAE,cAAc;4CAChB,oBAAoB,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI;wCACvD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,oBAAoB,GAAG;4CACrD,EAAE,cAAc;4CAChB,MAAM,SAAS,OAAO,CAAC,iBAAiB;4CACxC,IAAI,QAAQ;gDACV,MAAM,cAAc,OAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,EAAE;gDACxD,SAAS;gDACT,IAAI,aAAa;oDACf,kBAAkB;wDAAE,IAAI,OAAO,EAAE;wDAAE,aAAa,OAAO,WAAW;oDAAC;gDACrE,OAAO;oDACL,kBAAkB;gDACpB;gDACA,oBAAoB;gDACpB,qBAAqB;4CACvB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,EAAE,cAAc;4CAChB,qBAAqB;wCACvB;oCACF;;;;;;;;;;;;sCAIJ,wbAAC;4BAAI,WAAU;sCACZ,iCACC,wbAAC;gCAAI,WAAU;0CAA2B;;;;;uCACxC,QAAQ,MAAM,KAAK,kBACrB,wbAAC;gCAAI,WAAU;0CAA2B;;;;;qDAE1C,wbAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,wbAAC;wCAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kJACA,CAAC,UAAU,OAAO,EAAE,IAAI,UAAU,gBAAgB,KAAK;wCAEzD,SAAS;4CACP,MAAM,cAAc,OAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,EAAE;4CACxD,SAAS;4CACT,IAAI,aAAa;gDACf,kBAAkB;oDAAE,IAAI,OAAO,EAAE;oDAAE,aAAa,OAAO,WAAW;gDAAC;4CACrE,OAAO;gDACL,kBAAkB;4CACpB;4CACA,oBAAoB;4CACpB,qBAAqB;wCACvB;wCACA,cAAc,IAAM,oBAAoB;;0DAExC,wbAAC,wRAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,OAAO,EAAE,GAAG,gBAAgB;;;;;;4CAGzC,OAAO,WAAW;;uCAxBd,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkClC", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/content-format.ts"], "sourcesContent": ["import { Block, PartialBlock } from '@blocknote/core';\n\n/**\n * Checks if a string is HTML by looking for HTML tags\n * @param str String to check\n * @returns True if the string appears to be HTML\n */\nfunction isHtml(str: string): boolean {\n  // Simple heuristic: check if string contains HTML tags\n  const htmlTagRegex = /<[^>]*>/;\n  return htmlTagRegex.test(str.trim());\n}\n\n/**\n * Checks if a string is JSON by trying to parse it\n * @param str String to check\n * @returns True if the string is valid JSON\n */\nfunction isJson(str: string): boolean {\n  try {\n    JSON.parse(str);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Checks if a string is a BlockNote JSON value\n * @param str String to check\n * @returns True if the string appears to be BlockNote JSON\n */\nfunction isBlockNoteJson(str: string): boolean {\n  if (!isJson(str)) return false;\n  \n  try {\n    const parsed = JSON.parse(str);\n    // BlockNote values are arrays of blocks with id, type, and content properties\n    return Array.isArray(parsed) && parsed.every(block => \n      typeof block === 'object' && \n      block !== null && \n      'id' in block && \n      'type' in block && \n      'content' in block\n    );\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Detects the format of content string\n * @param content Content string to analyze\n * @param contentFormat Optional format hint from server\n * @returns 'html' or 'json'\n */\nexport function detectContentFormat(content: string, contentFormat?: string): 'html' | 'json' {\n  // If we have a format hint from server, use it\n  if (contentFormat === 'Html') return 'html';\n  if (contentFormat === 'Blocks') return 'json';\n  \n  // Use heuristics to detect format\n  if (isBlockNoteJson(content)) return 'json';\n  if (isHtml(content)) return 'html';\n  \n  // Default to HTML for plain text or unknown format\n  return 'html';\n}\n\n/**\n * Converts HTML to BlockNote blocks\n * @param html HTML string\n * @returns Array of BlockNote blocks (partial blocks)\n */\nexport function htmlToBlockNote(html: string): PartialBlock[] {\n  // Simple conversion - create a paragraph block with the HTML content\n  // For more sophisticated conversion, you might want to parse HTML and create appropriate blocks\n  const cleanText = html.replace(/<[^>]*>/g, '').trim();\n\n  if (!cleanText) {\n    return [{\n      type: 'paragraph',\n      content: []\n    }];\n  }\n\n  return [{\n    type: 'paragraph',\n    content: [{ type: 'text', text: cleanText, styles: {} }]\n  }];\n}\n\n/**\n * Converts BlockNote blocks to HTML\n * @param blocks Array of BlockNote blocks\n * @returns HTML string\n */\nexport function blockNoteToHtml(blocks: Block[]): string {\n  return blocks.map(block => {\n    switch (block.type) {\n      case 'paragraph':\n        const text = block.content?.map(content => \n          content.type === 'text' ? content.text : ''\n        ).join('') || '';\n        return `<p>${text}</p>`;\n      case 'heading':\n        const headingText = block.content?.map(content => \n          content.type === 'text' ? content.text : ''\n        ).join('') || '';\n        const level = (block.props as any)?.level || 1;\n        return `<h${level}>${headingText}</h${level}>`;\n      default:\n        return '';\n    }\n  }).join('\\n');\n}\n\n/**\n * Prepares content for saving to server\n * @param blocks BlockNote blocks (can be partial)\n * @returns JSON string representation of blocks\n */\nexport function prepareContentForSave(blocks: PartialBlock[]): string {\n  return JSON.stringify(blocks);\n}\n\n/**\n * Prepares content for loading into BlockNote editor\n * @param content Content string from server\n * @param contentFormat Optional format hint from server\n * @returns Array of BlockNote blocks (partial blocks for editor)\n */\nexport function prepareContentForEditor(content: string, contentFormat?: string): PartialBlock[] {\n  if (!content || content.trim() === '') {\n    return [{\n      type: 'paragraph',\n      content: []\n    }];\n  }\n\n  const format = detectContentFormat(content, contentFormat);\n\n  if (format === 'json') {\n    try {\n      return JSON.parse(content) as PartialBlock[];\n    } catch (error) {\n      console.error('Error parsing BlockNote JSON:', error);\n      // Fallback to HTML conversion\n      return htmlToBlockNote(content);\n    }\n  } else {\n    return htmlToBlockNote(content);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;CAIC,GACD,SAAS,OAAO,GAAW;IACzB,uDAAuD;IACvD,MAAM,eAAe;IACrB,OAAO,aAAa,IAAI,CAAC,IAAI,IAAI;AACnC;AAEA;;;;CAIC,GACD,SAAS,OAAO,GAAW;IACzB,IAAI;QACF,KAAK,KAAK,CAAC;QACX,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA;;;;CAIC,GACD,SAAS,gBAAgB,GAAW;IAClC,IAAI,CAAC,OAAO,MAAM,OAAO;IAEzB,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,8EAA8E;QAC9E,OAAO,MAAM,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC,CAAA,QAC3C,OAAO,UAAU,YACjB,UAAU,QACV,QAAQ,SACR,UAAU,SACV,aAAa;IAEjB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAQO,SAAS,oBAAoB,OAAe,EAAE,aAAsB;IACzE,+CAA+C;IAC/C,IAAI,kBAAkB,QAAQ,OAAO;IACrC,IAAI,kBAAkB,UAAU,OAAO;IAEvC,kCAAkC;IAClC,IAAI,gBAAgB,UAAU,OAAO;IACrC,IAAI,OAAO,UAAU,OAAO;IAE5B,mDAAmD;IACnD,OAAO;AACT;AAOO,SAAS,gBAAgB,IAAY;IAC1C,qEAAqE;IACrE,gGAAgG;IAChG,MAAM,YAAY,KAAK,OAAO,CAAC,YAAY,IAAI,IAAI;IAEnD,IAAI,CAAC,WAAW;QACd,OAAO;YAAC;gBACN,MAAM;gBACN,SAAS,EAAE;YACb;SAAE;IACJ;IAEA,OAAO;QAAC;YACN,MAAM;YACN,SAAS;gBAAC;oBAAE,MAAM;oBAAQ,MAAM;oBAAW,QAAQ,CAAC;gBAAE;aAAE;QAC1D;KAAE;AACJ;AAOO,SAAS,gBAAgB,MAAe;IAC7C,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,MAAM,OAAO,MAAM,OAAO,EAAE,IAAI,CAAA,UAC9B,QAAQ,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,IACzC,KAAK,OAAO;gBACd,OAAO,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;YACzB,KAAK;gBACH,MAAM,cAAc,MAAM,OAAO,EAAE,IAAI,CAAA,UACrC,QAAQ,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,IACzC,KAAK,OAAO;gBACd,MAAM,QAAQ,AAAC,MAAM,KAAK,EAAU,SAAS;gBAC7C,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,YAAY,GAAG,EAAE,MAAM,CAAC,CAAC;YAChD;gBACE,OAAO;QACX;IACF,GAAG,IAAI,CAAC;AACV;AAOO,SAAS,sBAAsB,MAAsB;IAC1D,OAAO,KAAK,SAAS,CAAC;AACxB;AAQO,SAAS,wBAAwB,OAAe,EAAE,aAAsB;IAC7E,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;QACrC,OAAO;YAAC;gBACN,MAAM;gBACN,SAAS,EAAE;YACb;SAAE;IACJ;IAEA,MAAM,SAAS,oBAAoB,SAAS;IAE5C,IAAI,WAAW,QAAQ;QACrB,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,8BAA8B;YAC9B,OAAO,gBAAgB;QACzB;IACF,OAAO;QACL,OAAO,gBAAgB;IACzB;AACF", "debugId": null}}, {"offset": {"line": 1407, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/works/work-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { BlockNoteEditorField } from \"@/components/editor/blocknote-editor\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useCreateWork, useUpdateWork } from \"@/lib/hooks/use-works\";\r\nimport { workSchema } from \"@/lib/schemas/work-schema\";\r\nimport { Work } from \"@/lib/types\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { formatDateForInput } from \"@/lib/utils/format-date\";\r\nimport { useAuthors } from \"@/lib/hooks/use-authors\";\r\nimport { AuthorSelect } from \"@/components/authors/author-select\";\r\nimport { prepareContentForSave, prepareContentForEditor } from \"@/lib/utils/content-format\";\r\nimport { PartialBlock } from \"@blocknote/core\";\r\n\r\ninterface WorkFormProps {\r\n  work?: Work;\r\n  authorId?: string;\r\n}\r\n\r\nexport function WorkForm({ work, authorId }: WorkFormProps) {\r\n  const router = useRouter();\r\n  const createWork = useCreateWork();\r\n  const updateWork = useUpdateWork();\r\n  const { isLoading: isLoadingAuthors } = useAuthors();\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const form = useForm<typeof workSchema._type>({\r\n    resolver: zodResolver(workSchema),\r\n    defaultValues: work\r\n      ? {\r\n        authorId: work.authorId,\r\n        title: work.title,\r\n        content: work.content || \"\",\r\n        genres: work.genres || [],\r\n        comments: work.comments || \"\",\r\n        source: work.source || \"\",\r\n        publishedDate: work.publishedDate ? formatDateForInput(work.publishedDate) : \"\",\r\n      }\r\n      : {\r\n        authorId: authorId || \"\",\r\n        title: \"\",\r\n        content: \"\",\r\n        genres: [],\r\n        comments: \"\",\r\n        source: \"\",\r\n        publishedDate: \"\",\r\n      },\r\n  });\r\n\r\n  async function onSubmit(values: typeof workSchema._type) {\r\n    setIsSubmitting(true);\r\n    try {\r\n      console.log(\"onSubmit\", values.content);\r\n\r\n      // Преобразуем строковые даты в объекты Date или null\r\n      // Сохраняем контент как JSON для BlockNote\r\n      const formattedValues = {\r\n        ...values,\r\n        content: values.content, // Сохраняем JSON напрямую\r\n        publishedDate: values.publishedDate ? new Date(values.publishedDate).toISOString() : null,\r\n      };\r\n\r\n      if (work) {\r\n        await updateWork.mutateAsync({\r\n          id: work.id,\r\n          data: formattedValues,\r\n        });\r\n        toast.success(\"Әсәр мәгълүматлары уңышлы үзгәртелде\");\r\n      } else {\r\n        await createWork.mutateAsync(formattedValues);\r\n        toast.success(\"Яңа әсәр уңышлы өстәлде\");\r\n      }\r\n      router.push(\"/works\");\r\n    } catch (error) {\r\n      console.error(\"Error submitting form:\", error);\r\n      toast.error(\"Хата: \" + (error instanceof Error ? error.message : \"Билгесез хата\"));\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Form {...form}>\r\n        <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"authorId\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Автор</FormLabel>\r\n                  <FormControl>\r\n                    <AuthorSelect\r\n                      value={field.value}\r\n                      onChange={field.onChange}\r\n                      disabled={isLoadingAuthors}\r\n                    />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"title\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Исем</FormLabel>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Әсәр исеме\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </div>\r\n\r\n          <FormField\r\n            control={form.control}\r\n            name=\"content\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Эчтәлек</FormLabel>\r\n                <FormControl>\r\n                  <div className=\"min-h-[400px] w-full\">\r\n                    <BlockNoteEditorField\r\n                      value={(() => {\r\n                        // Если поле пустое, возвращаем undefined для использования значения по умолчанию\r\n                        if (!field.value) {\r\n                          return undefined;\r\n                        }\r\n\r\n                        // Преобразуем контент для редактора\r\n                        return prepareContentForEditor(field.value);\r\n                      })()}\r\n                      onChange={(blocks: PartialBlock[]) => {\r\n                        // Сохраняем JSON представление для формы\r\n                        const jsonContent = prepareContentForSave(blocks);\r\n                        field.onChange(jsonContent);\r\n                      }}\r\n                      placeholder=\"Шигыйрь эчтәлеге...\"\r\n                      className=\"border rounded-md\"\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"source\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Чыганак</FormLabel>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Китап исеме, журнал һ.б.\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"publishedDate\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Басылган көне</FormLabel>\r\n                  <FormControl>\r\n                    <Input type=\"date\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </div>\r\n\r\n          <FormField\r\n            control={form.control}\r\n            name=\"comments\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Искәрмәләр</FormLabel>\r\n                <FormControl>\r\n                  <Textarea\r\n                    placeholder=\"Әсәр турында өстәмә мәгълүмат\"\r\n                    className=\"min-h-[100px]\"\r\n                    {...field}\r\n                  />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <div className=\"flex justify-end gap-4\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={() => router.push(\"/works\")}\r\n            >\r\n              Кире кайту\r\n            </Button>\r\n            <Button type=\"submit\" disabled={isSubmitting}>\r\n              {isSubmitting\r\n                ? \"Саклана...\"\r\n                : work\r\n                  ? \"Үзгәртүләрне саклау\"\r\n                  : \"Әсәрне өстәү\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </Form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;;;;;;;;AAiCO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAiB;IACxD,MAAM,SAAS,CAAA,GAAA,4UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAA2B;QAC5C,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE,uIAAA,CAAA,aAAU;QAChC,eAAe,OACX;YACA,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,KAAK,MAAM,IAAI,EAAE;YACzB,UAAU,KAAK,QAAQ,IAAI;YAC3B,QAAQ,KAAK,MAAM,IAAI;YACvB,eAAe,KAAK,aAAa,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,aAAa,IAAI;QAC/E,IACE;YACA,UAAU,YAAY;YACtB,OAAO;YACP,SAAS;YACT,QAAQ,EAAE;YACV,UAAU;YACV,QAAQ;YACR,eAAe;QACjB;IACJ;IAEA,eAAe,SAAS,MAA+B;QACrD,gBAAgB;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC,YAAY,OAAO,OAAO;YAEtC,qDAAqD;YACrD,2CAA2C;YAC3C,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,SAAS,OAAO,OAAO;gBACvB,eAAe,OAAO,aAAa,GAAG,IAAI,KAAK,OAAO,aAAa,EAAE,WAAW,KAAK;YACvF;YAEA,IAAI,MAAM;gBACR,MAAM,WAAW,WAAW,CAAC;oBAC3B,IAAI,KAAK,EAAE;oBACX,MAAM;gBACR;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,WAAW,WAAW,CAAC;gBAC7B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,eAAe;QAClF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,wbAAC,gIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACV,cAAA,wbAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAW,WAAU;;8BACrD,wbAAC;oBAAI,WAAU;;sCACb,wbAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;sDACP,wbAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,wbAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,wbAAC,iJAAA,CAAA,eAAY;gDACX,OAAO,MAAM,KAAK;gDAClB,UAAU,MAAM,QAAQ;gDACxB,UAAU;;;;;;;;;;;sDAGd,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAIlB,wbAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;sDACP,wbAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,wbAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;gDAAC,aAAY;gDAAc,GAAG,KAAK;;;;;;;;;;;sDAE3C,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8BAMpB,wbAAC,gIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;8CACP,wbAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,wbAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,wbAAC;wCAAI,WAAU;kDACb,cAAA,wbAAC,mJAAA,CAAA,uBAAoB;4CACnB,OAAO,CAAC;gDACN,iFAAiF;gDACjF,IAAI,CAAC,MAAM,KAAK,EAAE;oDAChB,OAAO;gDACT;gDAEA,oCAAoC;gDACpC,OAAO,CAAA,GAAA,wIAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,KAAK;4CAC5C,CAAC;4CACD,UAAU,CAAC;gDACT,yCAAyC;gDACzC,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;gDAC1C,MAAM,QAAQ,CAAC;4CACjB;4CACA,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;8CAIhB,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,wbAAC;oBAAI,WAAU;;sCACb,wbAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;sDACP,wbAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,wbAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;gDAAC,aAAY;gDAA4B,GAAG,KAAK;;;;;;;;;;;sDAEzD,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAIlB,wbAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;sDACP,wbAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,wbAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;gDAAC,MAAK;gDAAQ,GAAG,KAAK;;;;;;;;;;;sDAE9B,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8BAMpB,wbAAC,gIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;8CACP,wbAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,wbAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,wbAAC,oIAAA,CAAA,WAAQ;wCACP,aAAY;wCACZ,WAAU;wCACT,GAAG,KAAK;;;;;;;;;;;8CAGb,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,wbAAC;oBAAI,WAAU;;sCACb,wbAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI,CAAC;sCAC5B;;;;;;sCAGD,wbAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU;sCAC7B,eACG,eACA,OACE,wBACA;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/use-dynamic-title.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\n/**\n * Хук для динамического изменения заголовка страницы в клиентских компонентах\n *\n * @param title Заголовок страницы\n * @param suffix Суффикс заголовка (по умолчанию \"Шигърият.ру Админ\")\n */\nexport function useDynamicTitle(title: string | null | undefined, suffix: string = \"Шигърият.ру Админ\") {\n  // Используем ref для хранения предыдущего заголовка\n  const originalTitleRef = useRef<string | null>(null);\n\n  // Используем ref для отслеживания, был ли уже установлен заголовок\n  const isMountedRef = useRef(false);\n\n  useEffect(() => {\n    // Пропускаем выполнение на сервере\n    if (typeof document === 'undefined') return;\n\n    // Сохраняем оригинальный заголовок только при первом рендере\n    if (!isMountedRef.current) {\n      originalTitleRef.current = document.title;\n      isMountedRef.current = true;\n    }\n\n    // Не меняем заголовок, если title не определен\n    if (!title) return;\n\n    // Устанавливаем новый заголовок\n    const newTitle = suffix ? `${title} | ${suffix}` : title;\n\n    // Меняем заголовок только если он отличается от текущего\n    if (document.title !== newTitle) {\n      document.title = newTitle;\n    }\n\n    // Восстанавливаем оригинальный заголовок при размонтировании компонента\n    return () => {\n      if (originalTitleRef.current) {\n        document.title = originalTitleRef.current;\n      }\n    };\n  }, [title, suffix]);\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS,gBAAgB,KAAgC,EAAE,SAAiB,mBAAmB;IACpG,oDAAoD;IACpD,MAAM,mBAAmB,CAAA,GAAA,+YAAA,CAAA,SAAM,AAAD,EAAiB;IAE/C,mEAAmE;IACnE,MAAM,eAAe,CAAA,GAAA,+YAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,mCAAmC;QACnC,IAAI,OAAO,aAAa,aAAa;QAErC,6DAA6D;QAC7D,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,iBAAiB,OAAO,GAAG,SAAS,KAAK;YACzC,aAAa,OAAO,GAAG;QACzB;QAEA,+CAA+C;QAC/C,IAAI,CAAC,OAAO;QAEZ,gCAAgC;QAChC,MAAM,WAAW,SAAS,GAAG,MAAM,GAAG,EAAE,QAAQ,GAAG;QAEnD,yDAAyD;QACzD,IAAI,SAAS,KAAK,KAAK,UAAU;YAC/B,SAAS,KAAK,GAAG;QACnB;QAEA,wEAAwE;QACxE,OAAO;YACL,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,SAAS,KAAK,GAAG,iBAAiB,OAAO;YAC3C;QACF;IACF,GAAG;QAAC;QAAO;KAAO;AACpB", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/app/%28admin%29/works/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { WorkForm } from \"@/components/works/work-form\";\nimport { useWork } from \"@/lib/hooks/use-works\";\nimport { useParams } from \"next/navigation\";\nimport { useDynamicTitle } from \"@/lib/utils/use-dynamic-title\";\nimport { useMemo } from \"react\";\n\nexport default function EditWorkPage() {\n  const params = useParams();\n  const id = params.id as string;\n  const { data: work, isLoading, error } = useWork(id);\n\n  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере\n  const pageTitle = useMemo(() => {\n    return work ? `${work.title} үзгәртү` : \"Әсәр үзгәртү\";\n  }, [work?.title]);\n\n  // Устанавливаем заголовок страницы\n  useDynamicTitle(pageTitle);\n\n  if (isLoading) {\n    return <div className=\"text-center py-4\">Йөкләнә...</div>;\n  }\n\n  if (error || !work) {\n    return (\n      <div className=\"text-center py-4 text-red-500\">\n        Хата: {error?.message || \"Әсәр табылмады\"}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <h1 className=\"text-3xl font-bold\">Әсәрне үзгәртү: {work.title}</h1>\n      <WorkForm work={work} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,4UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAAE;IAEjD,gEAAgE;IAChE,MAAM,YAAY,CAAA,GAAA,+YAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,GAAG;IAC1C,GAAG;QAAC,MAAM;KAAM;IAEhB,mCAAmC;IACnC,CAAA,GAAA,8IAAA,CAAA,kBAAe,AAAD,EAAE;IAEhB,IAAI,WAAW;QACb,qBAAO,wbAAC;YAAI,WAAU;sBAAmB;;;;;;IAC3C;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,wbAAC;YAAI,WAAU;;gBAAgC;gBACtC,OAAO,WAAW;;;;;;;IAG/B;IAEA,qBACE,wbAAC;QAAI,WAAU;;0BACb,wbAAC;gBAAG,WAAU;;oBAAqB;oBAAiB,KAAK,KAAK;;;;;;;0BAC9D,wbAAC,2IAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;;AAGtB", "debugId": null}}]}