#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/bin/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules:/mnt/d/Projects/shigriyat/node_modules:/mnt/d/Projects/node_modules:/mnt/d/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/bin/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules:/mnt/d/Projects/shigriyat/node_modules:/mnt/d/Projects/node_modules:/mnt/d/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" "$@"
else
  exec node  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" "$@"
fi
