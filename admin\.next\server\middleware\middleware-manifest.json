{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_35401bb2._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_b36fea90.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/authors/:path*{(\\\\.json)}?", "originalSource": "/authors/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/works/:path*{(\\\\.json)}?", "originalSource": "/works/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jIBJsTS5W+Q9SPDF+Y9qVlmAvdl4pgmfLaawoITWOIU=", "__NEXT_PREVIEW_MODE_ID": "43373561ffc1851eb6c0dfee664140fb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1499b86c6a20ea845a0856a0d0a650bef068c242e0ed9359c3fe7f97a18fa0d6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f4d47307ab0782acf8bdf9ca6ff5dff7f77c51270a44ffa2df7acf9be47f6746"}}}, "instrumentation": null, "functions": {}}