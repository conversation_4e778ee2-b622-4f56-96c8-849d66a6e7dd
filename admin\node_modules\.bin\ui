#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/ui/dist/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/ui/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/ui/dist/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/ui/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules/@shadcn/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/@shadcn+ui@0.0.4/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@shadcn/ui/dist/index.js" "$@"
else
  exec node  "$basedir/../@shadcn/ui/dist/index.js" "$@"
fi
