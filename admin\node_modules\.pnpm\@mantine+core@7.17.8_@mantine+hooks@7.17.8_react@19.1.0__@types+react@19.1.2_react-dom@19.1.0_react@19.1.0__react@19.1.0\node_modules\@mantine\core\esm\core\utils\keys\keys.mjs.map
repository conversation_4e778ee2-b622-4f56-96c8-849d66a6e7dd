{"version": 3, "file": "keys.mjs", "sources": ["../../../../src/core/utils/keys/keys.ts"], "sourcesContent": ["export function keys<T extends object, K extends keyof T>(object: T): K[] {\n  return Object.keys(object) as K[];\n}\n"], "names": [], "mappings": ";AAAO,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA0C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA;AACxE,CAAA,CAAO,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,KAAK,MAAM,CAAA,CAAA;AAC3B,CAAA;;"}