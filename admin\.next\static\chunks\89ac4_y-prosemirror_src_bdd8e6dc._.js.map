{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/plugins/keys.js"], "sourcesContent": ["import { Plugin<PERSON>ey } from 'prosemirror-state' // eslint-disable-line\n\n/**\n * The unique prosemirror plugin key for syncPlugin\n *\n * @public\n */\nexport const ySyncPluginKey = new PluginKey('y-sync')\n\n/**\n * The unique prosemirror plugin key for undoPlugin\n *\n * @public\n * @type {PluginKey<import('./undo-plugin').UndoPluginState>}\n */\nexport const yUndoPluginKey = new PluginKey('y-undo')\n\n/**\n * The unique prosemirror plugin key for cursorPlugin\n *\n * @public\n */\nexport const yCursorPluginKey = new PluginKey('yjs-cursor')\n"], "names": [], "mappings": ";;;;;AAAA,wXAA8C,sBAAsB;;AAO7D,MAAM,iBAAiB,IAAI,yNAAA,CAAA,YAAS,CAAC;AAQrC,MAAM,iBAAiB,IAAI,yNAAA,CAAA,YAAS,CAAC;AAOrC,MAAM,mBAAmB,IAAI,yNAAA,CAAA,YAAS,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/utils.js"], "sourcesContent": ["import * as sha256 from 'lib0/hash/sha256'\nimport * as buf from 'lib0/buffer'\n\n/**\n * Custom function to transform sha256 hash to N byte\n *\n * @param {Uint8Array} digest\n */\nconst _convolute = digest => {\n  const N = 6\n  for (let i = N; i < digest.length; i++) {\n    digest[i % N] = digest[i % N] ^ digest[i]\n  }\n  return digest.slice(0, N)\n}\n\n/**\n * @param {any} json\n */\nexport const hashOfJSON = (json) => buf.toBase64(_convolute(sha256.digest(buf.encodeAny(json))))\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;;CAIC,GACD,MAAM,aAAa,CAAA;IACjB,MAAM,IAAI;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE;IAC3C;IACA,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB;AAKO,MAAM,aAAa,CAAC,OAAS,CAAA,GAAA,oLAAA,CAAA,WAAY,AAAD,EAAE,WAAW,CAAA,GAAA,4LAAA,CAAA,SAAa,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,YAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/plugins/sync-plugin.js"], "sourcesContent": ["/**\n * @module bindings/prosemirror\n */\n\nimport { createMutex } from 'lib0/mutex'\nimport * as PModel from 'prosemirror-model'\nimport { AllSelection, Plugin, TextSelection, NodeSelection } from \"prosemirror-state\"; // eslint-disable-line\nimport * as math from 'lib0/math'\nimport * as object from 'lib0/object'\nimport * as set from 'lib0/set'\nimport { simpleDiff } from 'lib0/diff'\nimport * as error from 'lib0/error'\nimport { ySyncPluginKey, yUndoPluginKey } from './keys.js'\nimport * as Y from 'yjs'\nimport {\n  absolutePositionToRelativePosition,\n  relativePositionToAbsolutePosition\n} from '../lib.js'\nimport * as random from 'lib0/random'\nimport * as environment from 'lib0/environment'\nimport * as dom from 'lib0/dom'\nimport * as eventloop from 'lib0/eventloop'\nimport * as map from 'lib0/map'\nimport * as utils from '../utils.js'\n\n/**\n * @typedef {Object} BindingMetadata\n * @property {ProsemirrorMapping} BindingMetadata.mapping\n * @property {Map<import('prosemirror-model').MarkType, boolean>} BindingMetadata.isOMark - is overlapping mark\n */\n\n/**\n * @return {BindingMetadata}\n */\nexport const createEmptyMeta = () => ({\n  mapping: new Map(),\n  isOMark: new Map()\n})\n\n/**\n * @param {Y.Item} item\n * @param {Y.Snapshot} [snapshot]\n */\nexport const isVisible = (item, snapshot) =>\n  snapshot === undefined\n    ? !item.deleted\n    : (snapshot.sv.has(item.id.client) && /** @type {number} */\n      (snapshot.sv.get(item.id.client)) > item.id.clock &&\n      !Y.isDeleted(snapshot.ds, item.id))\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType<any>, PModel.Node | Array<PModel.Node>>} ProsemirrorMapping\n */\n\n/**\n * @typedef {Object} ColorDef\n * @property {string} ColorDef.light\n * @property {string} ColorDef.dark\n */\n\n/**\n * @typedef {Object} YSyncOpts\n * @property {Array<ColorDef>} [YSyncOpts.colors]\n * @property {Map<string,ColorDef>} [YSyncOpts.colorMapping]\n * @property {Y.PermanentUserData|null} [YSyncOpts.permanentUserData]\n * @property {ProsemirrorMapping} [YSyncOpts.mapping]\n * @property {function} [YSyncOpts.onFirstRender] Fired when the content from Yjs is initially rendered to ProseMirror\n */\n\n/**\n * @type {Array<ColorDef>}\n */\nconst defaultColors = [{ light: '#ecd44433', dark: '#ecd444' }]\n\n/**\n * @param {Map<string,ColorDef>} colorMapping\n * @param {Array<ColorDef>} colors\n * @param {string} user\n * @return {ColorDef}\n */\nconst getUserColor = (colorMapping, colors, user) => {\n  // @todo do not hit the same color twice if possible\n  if (!colorMapping.has(user)) {\n    if (colorMapping.size < colors.length) {\n      const usedColors = set.create()\n      colorMapping.forEach((color) => usedColors.add(color))\n      colors = colors.filter((color) => !usedColors.has(color))\n    }\n    colorMapping.set(user, random.oneOf(colors))\n  }\n  return /** @type {ColorDef} */ (colorMapping.get(user))\n}\n\n/**\n * This plugin listens to changes in prosemirror view and keeps yXmlState and view in sync.\n *\n * This plugin also keeps references to the type and the shared document so other plugins can access it.\n * @param {Y.XmlFragment} yXmlFragment\n * @param {YSyncOpts} opts\n * @return {any} Returns a prosemirror plugin that binds to this type\n */\nexport const ySyncPlugin = (yXmlFragment, {\n  colors = defaultColors,\n  colorMapping = new Map(),\n  permanentUserData = null,\n  onFirstRender = () => {},\n  mapping\n} = {}) => {\n  let initialContentChanged = false\n  const binding = new ProsemirrorBinding(yXmlFragment, mapping)\n  const plugin = new Plugin({\n    props: {\n      editable: (state) => {\n        const syncState = ySyncPluginKey.getState(state)\n        return syncState.snapshot == null && syncState.prevSnapshot == null\n      }\n    },\n    key: ySyncPluginKey,\n    state: {\n      /**\n       * @returns {any}\n       */\n      init: (_initargs, _state) => {\n        return {\n          type: yXmlFragment,\n          doc: yXmlFragment.doc,\n          binding,\n          snapshot: null,\n          prevSnapshot: null,\n          isChangeOrigin: false,\n          isUndoRedoOperation: false,\n          addToHistory: true,\n          colors,\n          colorMapping,\n          permanentUserData\n        }\n      },\n      apply: (tr, pluginState) => {\n        const change = tr.getMeta(ySyncPluginKey)\n        if (change !== undefined) {\n          pluginState = Object.assign({}, pluginState)\n          for (const key in change) {\n            pluginState[key] = change[key]\n          }\n        }\n        pluginState.addToHistory = tr.getMeta('addToHistory') !== false\n        // always set isChangeOrigin. If undefined, this is not change origin.\n        pluginState.isChangeOrigin = change !== undefined &&\n          !!change.isChangeOrigin\n        pluginState.isUndoRedoOperation = change !== undefined && !!change.isChangeOrigin && !!change.isUndoRedoOperation\n        if (binding.prosemirrorView !== null) {\n          if (\n            change !== undefined &&\n            (change.snapshot != null || change.prevSnapshot != null)\n          ) {\n            // snapshot changed, rerender next\n            eventloop.timeout(0, () => {\n              if (binding.prosemirrorView == null) {\n                return\n              }\n              if (change.restore == null) {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.prevSnapshot,\n                  pluginState\n                )\n              } else {\n                binding._renderSnapshot(\n                  change.snapshot,\n                  change.snapshot,\n                  pluginState\n                )\n                // reset to current prosemirror state\n                delete pluginState.restore\n                delete pluginState.snapshot\n                delete pluginState.prevSnapshot\n                binding.mux(() => {\n                  binding._prosemirrorChanged(\n                    binding.prosemirrorView.state.doc\n                  )\n                })\n              }\n            })\n          }\n        }\n        return pluginState\n      }\n    },\n    view: (view) => {\n      binding.initView(view)\n      if (mapping == null) {\n        // force rerender to update the bindings mapping\n        binding._forceRerender()\n      }\n      onFirstRender()\n      return {\n        update: () => {\n          const pluginState = plugin.getState(view.state)\n          if (\n            pluginState.snapshot == null && pluginState.prevSnapshot == null\n          ) {\n            if (\n              // If the content doesn't change initially, we don't render anything to Yjs\n              // If the content was cleared by a user action, we want to catch the change and\n              // represent it in Yjs\n              initialContentChanged ||\n              view.state.doc.content.findDiffStart(\n                view.state.doc.type.createAndFill().content\n              ) !== null\n            ) {\n              initialContentChanged = true\n              if (\n                pluginState.addToHistory === false &&\n                !pluginState.isChangeOrigin\n              ) {\n                const yUndoPluginState = yUndoPluginKey.getState(view.state)\n                /**\n                 * @type {Y.UndoManager}\n                 */\n                const um = yUndoPluginState && yUndoPluginState.undoManager\n                if (um) {\n                  um.stopCapturing()\n                }\n              }\n              binding.mux(() => {\n                /** @type {Y.Doc} */ (pluginState.doc).transact((tr) => {\n                  tr.meta.set('addToHistory', pluginState.addToHistory)\n                  binding._prosemirrorChanged(view.state.doc)\n                }, ySyncPluginKey)\n              })\n            }\n          }\n        },\n        destroy: () => {\n          binding.destroy()\n        }\n      }\n    }\n  })\n  return plugin\n}\n\n/**\n * @param {import('prosemirror-state').Transaction} tr\n * @param {ReturnType<typeof getRelativeSelection>} relSel\n * @param {ProsemirrorBinding} binding\n */\nconst restoreRelativeSelection = (tr, relSel, binding) => {\n  if (relSel !== null && relSel.anchor !== null && relSel.head !== null) {\n    if (relSel.type === 'all') {\n      tr.setSelection(new AllSelection(tr.doc))\n    } else if (relSel.type === 'node') {\n      const anchor = relativePositionToAbsolutePosition(\n        binding.doc,\n        binding.type,\n        relSel.anchor,\n        binding.mapping\n      )\n      tr.setSelection(NodeSelection.create(tr.doc, anchor))\n    } else {\n      const anchor = relativePositionToAbsolutePosition(\n        binding.doc,\n        binding.type,\n        relSel.anchor,\n        binding.mapping\n      )\n      const head = relativePositionToAbsolutePosition(\n        binding.doc,\n        binding.type,\n        relSel.head,\n        binding.mapping\n      )\n      if (anchor !== null && head !== null) {\n        const sel = TextSelection.between(tr.doc.resolve(anchor), tr.doc.resolve(head))\n        tr.setSelection(sel)\n      }\n    }\n  }\n}\n\n/**\n * @param {ProsemirrorBinding} pmbinding\n * @param {import('prosemirror-state').EditorState} state\n */\nexport const getRelativeSelection = (pmbinding, state) => ({\n  type: /** @type {any} */ (state.selection).jsonID,\n  anchor: absolutePositionToRelativePosition(\n    state.selection.anchor,\n    pmbinding.type,\n    pmbinding.mapping\n  ),\n  head: absolutePositionToRelativePosition(\n    state.selection.head,\n    pmbinding.type,\n    pmbinding.mapping\n  )\n})\n\n/**\n * Binding for prosemirror.\n *\n * @protected\n */\nexport class ProsemirrorBinding {\n  /**\n   * @param {Y.XmlFragment} yXmlFragment The bind source\n   * @param {ProsemirrorMapping} mapping\n   */\n  constructor (yXmlFragment, mapping = new Map()) {\n    this.type = yXmlFragment\n    /**\n     * this will be set once the view is created\n     * @type {any}\n     */\n    this.prosemirrorView = null\n    this.mux = createMutex()\n    this.mapping = mapping\n    /**\n     * Is overlapping mark - i.e. mark does not exclude itself.\n     *\n     * @type {Map<import('prosemirror-model').MarkType, boolean>}\n     */\n    this.isOMark = new Map()\n    this._observeFunction = this._typeChanged.bind(this)\n    /**\n     * @type {Y.Doc}\n     */\n    // @ts-ignore\n    this.doc = yXmlFragment.doc\n    /**\n     * current selection as relative positions in the Yjs model\n     */\n    this.beforeTransactionSelection = null\n    this.beforeAllTransactions = () => {\n      if (this.beforeTransactionSelection === null && this.prosemirrorView != null) {\n        this.beforeTransactionSelection = getRelativeSelection(\n          this,\n          this.prosemirrorView.state\n        )\n      }\n    }\n    this.afterAllTransactions = () => {\n      this.beforeTransactionSelection = null\n    }\n    this._domSelectionInView = null\n  }\n\n  /**\n   * Create a transaction for changing the prosemirror state.\n   *\n   * @returns\n   */\n  get _tr () {\n    return this.prosemirrorView.state.tr.setMeta('addToHistory', false)\n  }\n\n  _isLocalCursorInView () {\n    if (!this.prosemirrorView.hasFocus()) return false\n    if (environment.isBrowser && this._domSelectionInView === null) {\n      // Calculate the domSelectionInView and clear by next tick after all events are finished\n      eventloop.timeout(0, () => {\n        this._domSelectionInView = null\n      })\n      this._domSelectionInView = this._isDomSelectionInView()\n    }\n    return this._domSelectionInView\n  }\n\n  _isDomSelectionInView () {\n    const selection = this.prosemirrorView._root.getSelection()\n\n    if (selection == null || selection.anchorNode == null) return false\n\n    const range = this.prosemirrorView._root.createRange()\n    range.setStart(selection.anchorNode, selection.anchorOffset)\n    range.setEnd(selection.focusNode, selection.focusOffset)\n\n    // This is a workaround for an edgecase where getBoundingClientRect will\n    // return zero values if the selection is collapsed at the start of a newline\n    // see reference here: https://stackoverflow.com/a/59780954\n    const rects = range.getClientRects()\n    if (rects.length === 0) {\n      // probably buggy newline behavior, explicitly select the node contents\n      if (range.startContainer && range.collapsed) {\n        range.selectNodeContents(range.startContainer)\n      }\n    }\n\n    const bounding = range.getBoundingClientRect()\n    const documentElement = dom.doc.documentElement\n\n    return bounding.bottom >= 0 && bounding.right >= 0 &&\n      bounding.left <=\n        (window.innerWidth || documentElement.clientWidth || 0) &&\n      bounding.top <= (window.innerHeight || documentElement.clientHeight || 0)\n  }\n\n  /**\n   * @param {Y.Snapshot} snapshot\n   * @param {Y.Snapshot} prevSnapshot\n   */\n  renderSnapshot (snapshot, prevSnapshot) {\n    if (!prevSnapshot) {\n      prevSnapshot = Y.createSnapshot(Y.createDeleteSet(), new Map())\n    }\n    this.prosemirrorView.dispatch(\n      this._tr.setMeta(ySyncPluginKey, { snapshot, prevSnapshot })\n    )\n  }\n\n  unrenderSnapshot () {\n    this.mapping.clear()\n    this.mux(() => {\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new PModel.Slice(PModel.Fragment.from(fragmentContent), 0, 0)\n      )\n      tr.setMeta(ySyncPluginKey, { snapshot: null, prevSnapshot: null })\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  _forceRerender () {\n    this.mapping.clear()\n    this.mux(() => {\n      // If this is a forced rerender, this might neither happen as a pm change nor within a Yjs\n      // transaction. Then the \"before selection\" doesn't exist. In this case, we need to create a\n      // relative position before replacing content. Fixes #126\n      const sel = this.beforeTransactionSelection !== null ? null : this.prosemirrorView.state.selection\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeFromYElement(\n          /** @type {Y.XmlElement} */ (t),\n          this.prosemirrorView.state.schema,\n          this\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      const tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new PModel.Slice(PModel.Fragment.from(fragmentContent), 0, 0)\n      )\n      if (sel) {\n        /**\n         * If the Prosemirror document we just created from this.type is\n         * smaller than the previous document, the selection might be\n         * out of bound, which would make Prosemirror throw an error.\n         */\n        const clampedAnchor = math.min(math.max(sel.anchor, 0), tr.doc.content.size)\n        const clampedHead = math.min(math.max(sel.head, 0), tr.doc.content.size)\n\n        tr.setSelection(TextSelection.create(tr.doc, clampedAnchor, clampedHead))\n      }\n      this.prosemirrorView.dispatch(\n        tr.setMeta(ySyncPluginKey, { isChangeOrigin: true, binding: this })\n      )\n    })\n  }\n\n  /**\n   * @param {Y.Snapshot|Uint8Array} snapshot\n   * @param {Y.Snapshot|Uint8Array} prevSnapshot\n   * @param {Object} pluginState\n   */\n  _renderSnapshot (snapshot, prevSnapshot, pluginState) {\n    /**\n     * The document that contains the full history of this document.\n     * @type {Y.Doc}\n     */\n    let historyDoc = this.doc\n    let historyType = this.type\n    if (!snapshot) {\n      snapshot = Y.snapshot(this.doc)\n    }\n    if (snapshot instanceof Uint8Array || prevSnapshot instanceof Uint8Array) {\n      if (!(snapshot instanceof Uint8Array) || !(prevSnapshot instanceof Uint8Array)) {\n        // expected both snapshots to be v2 updates\n        error.unexpectedCase()\n      }\n      historyDoc = new Y.Doc({ gc: false })\n      Y.applyUpdateV2(historyDoc, prevSnapshot)\n      prevSnapshot = Y.snapshot(historyDoc)\n      Y.applyUpdateV2(historyDoc, snapshot)\n      snapshot = Y.snapshot(historyDoc)\n      if (historyType._item === null) {\n        /**\n         * If is a root type, we need to find the root key in the initial document\n         * and use it to get the history type.\n         */\n        const rootKey = Array.from(this.doc.share.keys()).find(\n          (key) => this.doc.share.get(key) === this.type\n        )\n        historyType = historyDoc.getXmlFragment(rootKey)\n      } else {\n        /**\n         * If it is a sub type, we use the item id to find the history type.\n         */\n        const historyStructs =\n          historyDoc.store.clients.get(historyType._item.id.client) ?? []\n        const itemIndex = Y.findIndexSS(\n          historyStructs,\n          historyType._item.id.clock\n        )\n        const item = /** @type {Y.Item} */ (historyStructs[itemIndex])\n        const content = /** @type {Y.ContentType} */ (item.content)\n        historyType = /** @type {Y.XmlFragment} */ (content.type)\n      }\n    }\n    // clear mapping because we are going to rerender\n    this.mapping.clear()\n    this.mux(() => {\n      historyDoc.transact((transaction) => {\n        // before rendering, we are going to sanitize ops and split deleted ops\n        // if they were deleted by seperate users.\n        /**\n         * @type {Y.PermanentUserData}\n         */\n        const pud = pluginState.permanentUserData\n        if (pud) {\n          pud.dss.forEach((ds) => {\n            Y.iterateDeletedStructs(transaction, ds, (_item) => {})\n          })\n        }\n        /**\n         * @param {'removed'|'added'} type\n         * @param {Y.ID} id\n         */\n        const computeYChange = (type, id) => {\n          const user = type === 'added'\n            ? pud.getUserByClientId(id.client)\n            : pud.getUserByDeletedId(id)\n          return {\n            user,\n            type,\n            color: getUserColor(\n              pluginState.colorMapping,\n              pluginState.colors,\n              user\n            )\n          }\n        }\n        // Create document fragment and render\n        const fragmentContent = Y.typeListToArraySnapshot(\n          historyType,\n          new Y.Snapshot(prevSnapshot.ds, snapshot.sv)\n        ).map((t) => {\n          if (\n            !t._item.deleted || isVisible(t._item, snapshot) ||\n            isVisible(t._item, prevSnapshot)\n          ) {\n            return createNodeFromYElement(\n              t,\n              this.prosemirrorView.state.schema,\n              { mapping: new Map(), isOMark: new Map() },\n              snapshot,\n              prevSnapshot,\n              computeYChange\n            )\n          } else {\n            // No need to render elements that are not visible by either snapshot.\n            // If a client adds and deletes content in the same snapshot the element is not visible by either snapshot.\n            return null\n          }\n        }).filter((n) => n !== null)\n        // @ts-ignore\n        const tr = this._tr.replace(\n          0,\n          this.prosemirrorView.state.doc.content.size,\n          new PModel.Slice(PModel.Fragment.from(fragmentContent), 0, 0)\n        )\n        this.prosemirrorView.dispatch(\n          tr.setMeta(ySyncPluginKey, { isChangeOrigin: true })\n        )\n      }, ySyncPluginKey)\n    })\n  }\n\n  /**\n   * @param {Array<Y.YEvent<any>>} events\n   * @param {Y.Transaction} transaction\n   */\n  _typeChanged (events, transaction) {\n    if (this.prosemirrorView == null) return\n    const syncState = ySyncPluginKey.getState(this.prosemirrorView.state)\n    if (\n      events.length === 0 || syncState.snapshot != null ||\n      syncState.prevSnapshot != null\n    ) {\n      // drop out if snapshot is active\n      this.renderSnapshot(syncState.snapshot, syncState.prevSnapshot)\n      return\n    }\n    this.mux(() => {\n      /**\n       * @param {any} _\n       * @param {Y.AbstractType<any>} type\n       */\n      const delType = (_, type) => this.mapping.delete(type)\n      Y.iterateDeletedStructs(\n        transaction,\n        transaction.deleteSet,\n        (struct) => {\n          if (struct.constructor === Y.Item) {\n            const type = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (struct).content).type\n            type && this.mapping.delete(type)\n          }\n        }\n      )\n      transaction.changed.forEach(delType)\n      transaction.changedParentTypes.forEach(delType)\n      const fragmentContent = this.type.toArray().map((t) =>\n        createNodeIfNotExists(\n          /** @type {Y.XmlElement | Y.XmlHook} */ (t),\n          this.prosemirrorView.state.schema,\n          this\n        )\n      ).filter((n) => n !== null)\n      // @ts-ignore\n      let tr = this._tr.replace(\n        0,\n        this.prosemirrorView.state.doc.content.size,\n        new PModel.Slice(PModel.Fragment.from(fragmentContent), 0, 0)\n      )\n      restoreRelativeSelection(tr, this.beforeTransactionSelection, this)\n      tr = tr.setMeta(ySyncPluginKey, { isChangeOrigin: true, isUndoRedoOperation: transaction.origin instanceof Y.UndoManager })\n      if (\n        this.beforeTransactionSelection !== null && this._isLocalCursorInView()\n      ) {\n        tr.scrollIntoView()\n      }\n      this.prosemirrorView.dispatch(tr)\n    })\n  }\n\n  /**\n   * @param {import('prosemirror-model').Node} doc\n   */\n  _prosemirrorChanged (doc) {\n    this.doc.transact(() => {\n      updateYFragment(this.doc, this.type, doc, this)\n      this.beforeTransactionSelection = getRelativeSelection(\n        this,\n        this.prosemirrorView.state\n      )\n    }, ySyncPluginKey)\n  }\n\n  /**\n   * View is ready to listen to changes. Register observers.\n   * @param {any} prosemirrorView\n   */\n  initView (prosemirrorView) {\n    if (this.prosemirrorView != null) this.destroy()\n    this.prosemirrorView = prosemirrorView\n    this.doc.on('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.on('afterAllTransactions', this.afterAllTransactions)\n    this.type.observeDeep(this._observeFunction)\n  }\n\n  destroy () {\n    if (this.prosemirrorView == null) return\n    this.prosemirrorView = null\n    this.type.unobserveDeep(this._observeFunction)\n    this.doc.off('beforeAllTransactions', this.beforeAllTransactions)\n    this.doc.off('afterAllTransactions', this.afterAllTransactions)\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlElement | Y.XmlHook} el\n * @param {PModel.Schema} schema\n * @param {BindingMetadata} meta\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null}\n */\nconst createNodeIfNotExists = (\n  el,\n  schema,\n  meta,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const node = /** @type {PModel.Node} */ (meta.mapping.get(el))\n  if (node === undefined) {\n    if (el instanceof Y.XmlElement) {\n      return createNodeFromYElement(\n        el,\n        schema,\n        meta,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n    } else {\n      throw error.methodUnimplemented() // we are currently not handling hooks\n    }\n  }\n  return node\n}\n\n/**\n * @private\n * @param {Y.XmlElement} el\n * @param {any} schema\n * @param {BindingMetadata} meta\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {PModel.Node | null} Returns node if node could be created. Otherwise it deletes the yjs type and returns null\n */\nexport const createNodeFromYElement = (\n  el,\n  schema,\n  meta,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const children = []\n  /**\n   * @param {Y.XmlElement | Y.XmlText} type\n   */\n  const createChildren = (type) => {\n    if (type instanceof Y.XmlElement) {\n      const n = createNodeIfNotExists(\n        type,\n        schema,\n        meta,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (n !== null) {\n        children.push(n)\n      }\n    } else {\n      // If the next ytext exists and was created by us, move the content to the current ytext.\n      // This is a fix for #160 -- duplication of characters when two Y.Text exist next to each\n      // other.\n      const nextytext = /** @type {Y.ContentType} */ (type._item.right?.content)?.type\n      if (nextytext instanceof Y.Text && !nextytext._item.deleted && nextytext._item.id.client === nextytext.doc.clientID) {\n        type.applyDelta([\n          { retain: type.length },\n          ...nextytext.toDelta()\n        ])\n        nextytext.doc.transact(tr => {\n          nextytext._item.delete(tr)\n        })\n      }\n      // now create the prosemirror text nodes\n      const ns = createTextNodesFromYText(\n        type,\n        schema,\n        meta,\n        snapshot,\n        prevSnapshot,\n        computeYChange\n      )\n      if (ns !== null) {\n        ns.forEach((textchild) => {\n          if (textchild !== null) {\n            children.push(textchild)\n          }\n        })\n      }\n    }\n  }\n  if (snapshot === undefined || prevSnapshot === undefined) {\n    el.toArray().forEach(createChildren)\n  } else {\n    Y.typeListToArraySnapshot(el, new Y.Snapshot(prevSnapshot.ds, snapshot.sv))\n      .forEach(createChildren)\n  }\n  try {\n    const attrs = el.getAttributes(snapshot)\n    if (snapshot !== undefined) {\n      if (!isVisible(/** @type {Y.Item} */ (el._item), snapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('removed', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'removed' }\n      } else if (!isVisible(/** @type {Y.Item} */ (el._item), prevSnapshot)) {\n        attrs.ychange = computeYChange\n          ? computeYChange('added', /** @type {Y.Item} */ (el._item).id)\n          : { type: 'added' }\n      }\n    }\n    const node = schema.node(el.nodeName, attrs, children)\n    meta.mapping.set(el, node)\n    return node\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (el.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (el._item).delete(transaction)\n    }, ySyncPluginKey)\n    meta.mapping.delete(el)\n    return null\n  }\n}\n\n/**\n * @private\n * @param {Y.XmlText} text\n * @param {import('prosemirror-model').Schema} schema\n * @param {BindingMetadata} _meta\n * @param {Y.Snapshot} [snapshot]\n * @param {Y.Snapshot} [prevSnapshot]\n * @param {function('removed' | 'added', Y.ID):any} [computeYChange]\n * @return {Array<PModel.Node>|null}\n */\nconst createTextNodesFromYText = (\n  text,\n  schema,\n  _meta,\n  snapshot,\n  prevSnapshot,\n  computeYChange\n) => {\n  const nodes = []\n  const deltas = text.toDelta(snapshot, prevSnapshot, computeYChange)\n  try {\n    for (let i = 0; i < deltas.length; i++) {\n      const delta = deltas[i]\n      nodes.push(schema.text(delta.insert, attributesToMarks(delta.attributes, schema)))\n    }\n  } catch (e) {\n    // an error occured while creating the node. This is probably a result of a concurrent action.\n    /** @type {Y.Doc} */ (text.doc).transact((transaction) => {\n      /** @type {Y.Item} */ (text._item).delete(transaction)\n    }, ySyncPluginKey)\n    return null\n  }\n  // @ts-ignore\n  return nodes\n}\n\n/**\n * @private\n * @param {Array<any>} nodes prosemirror node\n * @param {BindingMetadata} meta\n * @return {Y.XmlText}\n */\nconst createTypeFromTextNodes = (nodes, meta) => {\n  const type = new Y.XmlText()\n  const delta = nodes.map((node) => ({\n    // @ts-ignore\n    insert: node.text,\n    attributes: marksToAttributes(node.marks, meta)\n  }))\n  type.applyDelta(delta)\n  meta.mapping.set(type, nodes)\n  return type\n}\n\n/**\n * @private\n * @param {any} node prosemirror node\n * @param {BindingMetadata} meta\n * @return {Y.XmlElement}\n */\nconst createTypeFromElementNode = (node, meta) => {\n  const type = new Y.XmlElement(node.type.name)\n  for (const key in node.attrs) {\n    const val = node.attrs[key]\n    if (val !== null && key !== 'ychange') {\n      type.setAttribute(key, val)\n    }\n  }\n  type.insert(\n    0,\n    normalizePNodeContent(node).map((n) =>\n      createTypeFromTextOrElementNode(n, meta)\n    )\n  )\n  meta.mapping.set(type, node)\n  return type\n}\n\n/**\n * @private\n * @param {PModel.Node|Array<PModel.Node>} node prosemirror text node\n * @param {BindingMetadata} meta\n * @return {Y.XmlElement|Y.XmlText}\n */\nconst createTypeFromTextOrElementNode = (node, meta) =>\n  node instanceof Array\n    ? createTypeFromTextNodes(node, meta)\n    : createTypeFromElementNode(node, meta)\n\n/**\n * @param {any} val\n */\nconst isObject = (val) => typeof val === 'object' && val !== null\n\n/**\n * @param {any} pattrs\n * @param {any} yattrs\n */\nconst equalAttrs = (pattrs, yattrs) => {\n  const keys = Object.keys(pattrs).filter((key) => pattrs[key] !== null)\n  let eq =\n    keys.length ===\n      (yattrs == null ? 0 : Object.keys(yattrs).filter((key) => yattrs[key] !== null).length)\n  for (let i = 0; i < keys.length && eq; i++) {\n    const key = keys[i]\n    const l = pattrs[key]\n    const r = yattrs[key]\n    eq = key === 'ychange' || l === r ||\n      (isObject(l) && isObject(r) && equalAttrs(l, r))\n  }\n  return eq\n}\n\n/**\n * @typedef {Array<Array<PModel.Node>|PModel.Node>} NormalizedPNodeContent\n */\n\n/**\n * @param {any} pnode\n * @return {NormalizedPNodeContent}\n */\nconst normalizePNodeContent = (pnode) => {\n  const c = pnode.content.content\n  const res = []\n  for (let i = 0; i < c.length; i++) {\n    const n = c[i]\n    if (n.isText) {\n      const textNodes = []\n      for (let tnode = c[i]; i < c.length && tnode.isText; tnode = c[++i]) {\n        textNodes.push(tnode)\n      }\n      i--\n      res.push(textNodes)\n    } else {\n      res.push(n)\n    }\n  }\n  return res\n}\n\n/**\n * @param {Y.XmlText} ytext\n * @param {Array<any>} ptexts\n */\nconst equalYTextPText = (ytext, ptexts) => {\n  const delta = ytext.toDelta()\n  return delta.length === ptexts.length &&\n    delta.every(/** @type {(d:any,i:number) => boolean} */ (d, i) =>\n      d.insert === /** @type {any} */ (ptexts[i]).text &&\n      object.keys(d.attributes || {}).length === ptexts[i].marks.length &&\n      object.every(d.attributes, (attr, yattrname) => {\n        const markname = yattr2markname(yattrname)\n        const pmarks = ptexts[i].marks\n        return equalAttrs(attr, pmarks.find(/** @param {any} mark */ mark => mark.type.name === markname)?.attrs)\n      })\n    )\n}\n\n/**\n * @param {Y.XmlElement|Y.XmlText|Y.XmlHook} ytype\n * @param {any|Array<any>} pnode\n */\nconst equalYTypePNode = (ytype, pnode) => {\n  if (\n    ytype instanceof Y.XmlElement && !(pnode instanceof Array) &&\n    matchNodeName(ytype, pnode)\n  ) {\n    const normalizedContent = normalizePNodeContent(pnode)\n    return ytype._length === normalizedContent.length &&\n      equalAttrs(ytype.getAttributes(), pnode.attrs) &&\n      ytype.toArray().every((ychild, i) =>\n        equalYTypePNode(ychild, normalizedContent[i])\n      )\n  }\n  return ytype instanceof Y.XmlText && pnode instanceof Array &&\n    equalYTextPText(ytype, pnode)\n}\n\n/**\n * @param {PModel.Node | Array<PModel.Node> | undefined} mapped\n * @param {PModel.Node | Array<PModel.Node>} pcontent\n */\nconst mappedIdentity = (mapped, pcontent) =>\n  mapped === pcontent ||\n  (mapped instanceof Array && pcontent instanceof Array &&\n    mapped.length === pcontent.length && mapped.every((a, i) =>\n    pcontent[i] === a\n  ))\n\n/**\n * @param {Y.XmlElement} ytype\n * @param {PModel.Node} pnode\n * @param {BindingMetadata} meta\n * @return {{ foundMappedChild: boolean, equalityFactor: number }}\n */\nconst computeChildEqualityFactor = (ytype, pnode, meta) => {\n  const yChildren = ytype.toArray()\n  const pChildren = normalizePNodeContent(pnode)\n  const pChildCnt = pChildren.length\n  const yChildCnt = yChildren.length\n  const minCnt = math.min(yChildCnt, pChildCnt)\n  let left = 0\n  let right = 0\n  let foundMappedChild = false\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (mappedIdentity(meta.mapping.get(leftY), leftP)) {\n      foundMappedChild = true // definite (good) match!\n    } else if (!equalYTypePNode(leftY, leftP)) {\n      break\n    }\n  }\n  for (; left + right < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (mappedIdentity(meta.mapping.get(rightY), rightP)) {\n      foundMappedChild = true\n    } else if (!equalYTypePNode(rightY, rightP)) {\n      break\n    }\n  }\n  return {\n    equalityFactor: left + right,\n    foundMappedChild\n  }\n}\n\n/**\n * @param {Y.Text} ytext\n */\nconst ytextTrans = (ytext) => {\n  let str = ''\n  /**\n   * @type {Y.Item|null}\n   */\n  let n = ytext._start\n  const nAttrs = {}\n  while (n !== null) {\n    if (!n.deleted) {\n      if (n.countable && n.content instanceof Y.ContentString) {\n        str += n.content.str\n      } else if (n.content instanceof Y.ContentFormat) {\n        nAttrs[n.content.key] = null\n      }\n    }\n    n = n.right\n  }\n  return {\n    str,\n    nAttrs\n  }\n}\n\n/**\n * @todo test this more\n *\n * @param {Y.Text} ytext\n * @param {Array<any>} ptexts\n * @param {BindingMetadata} meta\n */\nconst updateYText = (ytext, ptexts, meta) => {\n  meta.mapping.set(ytext, ptexts)\n  const { nAttrs, str } = ytextTrans(ytext)\n  const content = ptexts.map((p) => ({\n    insert: /** @type {any} */ (p).text,\n    attributes: Object.assign({}, nAttrs, marksToAttributes(p.marks, meta))\n  }))\n  const { insert, remove, index } = simpleDiff(\n    str,\n    content.map((c) => c.insert).join('')\n  )\n  ytext.delete(index, remove)\n  ytext.insert(index, insert)\n  ytext.applyDelta(\n    content.map((c) => ({ retain: c.insert.length, attributes: c.attributes }))\n  )\n}\n\nconst hashedMarkNameRegex = /(.*)(--[a-zA-Z0-9+/=]{8})$/\n/**\n * @param {string} attrName\n */\nexport const yattr2markname = attrName => hashedMarkNameRegex.exec(attrName)?.[1] ?? attrName\n\n/**\n * @todo move this to markstoattributes\n *\n * @param {Object<string, any>} attrs\n * @param {import('prosemirror-model').Schema} schema\n */\nexport const attributesToMarks = (attrs, schema) => {\n  /**\n   * @type {Array<import('prosemirror-model').Mark>}\n   */\n  const marks = []\n  for (const markName in attrs) {\n    // remove hashes if necessary\n    marks.push(schema.mark(yattr2markname(markName), attrs[markName]))\n  }\n  return marks\n}\n\n/**\n * @param {Array<import('prosemirror-model').Mark>} marks\n * @param {BindingMetadata} meta\n */\nconst marksToAttributes = (marks, meta) => {\n  const pattrs = {}\n  marks.forEach((mark) => {\n    if (mark.type.name !== 'ychange') {\n      const isOverlapping = map.setIfUndefined(meta.isOMark, mark.type, () => !mark.type.excludes(mark.type))\n      pattrs[isOverlapping ? `${mark.type.name}--${utils.hashOfJSON(mark.toJSON())}` : mark.type.name] = mark.attrs\n    }\n  })\n  return pattrs\n}\n\n/**\n * Update a yDom node by syncing the current content of the prosemirror node.\n *\n * This is a y-prosemirror internal feature that you can use at your own risk.\n *\n * @private\n * @unstable\n *\n * @param {{transact: Function}} y\n * @param {Y.XmlFragment} yDomFragment\n * @param {any} pNode\n * @param {BindingMetadata} meta\n */\nexport const updateYFragment = (y, yDomFragment, pNode, meta) => {\n  if (\n    yDomFragment instanceof Y.XmlElement &&\n    yDomFragment.nodeName !== pNode.type.name\n  ) {\n    throw new Error('node name mismatch!')\n  }\n  meta.mapping.set(yDomFragment, pNode)\n  // update attributes\n  if (yDomFragment instanceof Y.XmlElement) {\n    const yDomAttrs = yDomFragment.getAttributes()\n    const pAttrs = pNode.attrs\n    for (const key in pAttrs) {\n      if (pAttrs[key] !== null) {\n        if (yDomAttrs[key] !== pAttrs[key] && key !== 'ychange') {\n          yDomFragment.setAttribute(key, pAttrs[key])\n        }\n      } else {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n    // remove all keys that are no longer in pAttrs\n    for (const key in yDomAttrs) {\n      if (pAttrs[key] === undefined) {\n        yDomFragment.removeAttribute(key)\n      }\n    }\n  }\n  // update children\n  const pChildren = normalizePNodeContent(pNode)\n  const pChildCnt = pChildren.length\n  const yChildren = yDomFragment.toArray()\n  const yChildCnt = yChildren.length\n  const minCnt = math.min(pChildCnt, yChildCnt)\n  let left = 0\n  let right = 0\n  // find number of matching elements from left\n  for (; left < minCnt; left++) {\n    const leftY = yChildren[left]\n    const leftP = pChildren[left]\n    if (!mappedIdentity(meta.mapping.get(leftY), leftP)) {\n      if (equalYTypePNode(leftY, leftP)) {\n        // update mapping\n        meta.mapping.set(leftY, leftP)\n      } else {\n        break\n      }\n    }\n  }\n  // find number of matching elements from right\n  for (; right + left < minCnt; right++) {\n    const rightY = yChildren[yChildCnt - right - 1]\n    const rightP = pChildren[pChildCnt - right - 1]\n    if (!mappedIdentity(meta.mapping.get(rightY), rightP)) {\n      if (equalYTypePNode(rightY, rightP)) {\n        // update mapping\n        meta.mapping.set(rightY, rightP)\n      } else {\n        break\n      }\n    }\n  }\n  y.transact(() => {\n    // try to compare and update\n    while (yChildCnt - left - right > 0 && pChildCnt - left - right > 0) {\n      const leftY = yChildren[left]\n      const leftP = pChildren[left]\n      const rightY = yChildren[yChildCnt - right - 1]\n      const rightP = pChildren[pChildCnt - right - 1]\n      if (leftY instanceof Y.XmlText && leftP instanceof Array) {\n        if (!equalYTextPText(leftY, leftP)) {\n          updateYText(leftY, leftP, meta)\n        }\n        left += 1\n      } else {\n        let updateLeft = leftY instanceof Y.XmlElement &&\n          matchNodeName(leftY, leftP)\n        let updateRight = rightY instanceof Y.XmlElement &&\n          matchNodeName(rightY, rightP)\n        if (updateLeft && updateRight) {\n          // decide which which element to update\n          const equalityLeft = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            meta\n          )\n          const equalityRight = computeChildEqualityFactor(\n            /** @type {Y.XmlElement} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            meta\n          )\n          if (\n            equalityLeft.foundMappedChild && !equalityRight.foundMappedChild\n          ) {\n            updateRight = false\n          } else if (\n            !equalityLeft.foundMappedChild && equalityRight.foundMappedChild\n          ) {\n            updateLeft = false\n          } else if (\n            equalityLeft.equalityFactor < equalityRight.equalityFactor\n          ) {\n            updateLeft = false\n          } else {\n            updateRight = false\n          }\n        }\n        if (updateLeft) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (leftY),\n            /** @type {PModel.Node} */ (leftP),\n            meta\n          )\n          left += 1\n        } else if (updateRight) {\n          updateYFragment(\n            y,\n            /** @type {Y.XmlFragment} */ (rightY),\n            /** @type {PModel.Node} */ (rightP),\n            meta\n          )\n          right += 1\n        } else {\n          meta.mapping.delete(yDomFragment.get(left))\n          yDomFragment.delete(left, 1)\n          yDomFragment.insert(left, [\n            createTypeFromTextOrElementNode(leftP, meta)\n          ])\n          left += 1\n        }\n      }\n    }\n    const yDelLen = yChildCnt - left - right\n    if (\n      yChildCnt === 1 && pChildCnt === 0 && yChildren[0] instanceof Y.XmlText\n    ) {\n      meta.mapping.delete(yChildren[0])\n      // Edge case handling https://github.com/yjs/y-prosemirror/issues/108\n      // Only delete the content of the Y.Text to retain remote changes on the same Y.Text object\n      yChildren[0].delete(0, yChildren[0].length)\n    } else if (yDelLen > 0) {\n      yDomFragment.slice(left, left + yDelLen).forEach(type => meta.mapping.delete(type))\n      yDomFragment.delete(left, yDelLen)\n    }\n    if (left + right < pChildCnt) {\n      const ins = []\n      for (let i = left; i < pChildCnt - right; i++) {\n        ins.push(createTypeFromTextOrElementNode(pChildren[i], meta))\n      }\n      yDomFragment.insert(left, ins)\n    }\n  }, ySyncPluginKey)\n}\n\n/**\n * @function\n * @param {Y.XmlElement} yElement\n * @param {any} pNode Prosemirror Node\n */\nconst matchNodeName = (yElement, pNode) =>\n  !(pNode instanceof Array) && yElement.nodeName === pNode.type.name\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAED;AACA;AACA,wXAAwF,sBAAsB;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAWO,MAAM,kBAAkB,IAAM,CAAC;QACpC,SAAS,IAAI;QACb,SAAS,IAAI;IACf,CAAC;AAMM,MAAM,YAAY,CAAC,MAAM,WAC9B,aAAa,YACT,CAAC,KAAK,OAAO,GACZ,SAAS,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK,mBAAmB,GACvD,AAAC,SAAS,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,IAAK,KAAK,EAAE,CAAC,KAAK,IACjD,CAAC,CAAA,GAAA,wLAAA,CAAA,YAAW,AAAD,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE;AAEvC;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;CAOC,GAED;;CAEC,GACD,MAAM,gBAAgB;IAAC;QAAE,OAAO;QAAa,MAAM;IAAU;CAAE;AAE/D;;;;;CAKC,GACD,MAAM,eAAe,CAAC,cAAc,QAAQ;IAC1C,oDAAoD;IACpD,IAAI,CAAC,aAAa,GAAG,CAAC,OAAO;QAC3B,IAAI,aAAa,IAAI,GAAG,OAAO,MAAM,EAAE;YACrC,MAAM,aAAa,CAAA,GAAA,iLAAA,CAAA,SAAU,AAAD;YAC5B,aAAa,OAAO,CAAC,CAAC,QAAU,WAAW,GAAG,CAAC;YAC/C,SAAS,OAAO,MAAM,CAAC,CAAC,QAAU,CAAC,WAAW,GAAG,CAAC;QACpD;QACA,aAAa,GAAG,CAAC,MAAM,CAAA,GAAA,oLAAA,CAAA,QAAY,AAAD,EAAE;IACtC;IACA,OAAgC,aAAa,GAAG,CAAC;AACnD;AAUO,MAAM,cAAc,CAAC,cAAc,EACxC,SAAS,aAAa,EACtB,eAAe,IAAI,KAAK,EACxB,oBAAoB,IAAI,EACxB,gBAAgB,KAAO,CAAC,EACxB,OAAO,EACR,GAAG,CAAC,CAAC;IACJ,IAAI,wBAAwB;IAC5B,MAAM,UAAU,IAAI,mBAAmB,cAAc;IACrD,MAAM,SAAS,IAAI,yNAAA,CAAA,SAAM,CAAC;QACxB,OAAO;YACL,UAAU,CAAC;gBACT,MAAM,YAAY,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC;gBAC1C,OAAO,UAAU,QAAQ,IAAI,QAAQ,UAAU,YAAY,IAAI;YACjE;QACF;QACA,KAAK,sWAAA,CAAA,iBAAc;QACnB,OAAO;YACL;;OAEC,GACD,MAAM,CAAC,WAAW;gBAChB,OAAO;oBACL,MAAM;oBACN,KAAK,aAAa,GAAG;oBACrB;oBACA,UAAU;oBACV,cAAc;oBACd,gBAAgB;oBAChB,qBAAqB;oBACrB,cAAc;oBACd;oBACA;oBACA;gBACF;YACF;YACA,OAAO,CAAC,IAAI;gBACV,MAAM,SAAS,GAAG,OAAO,CAAC,sWAAA,CAAA,iBAAc;gBACxC,IAAI,WAAW,WAAW;oBACxB,cAAc,OAAO,MAAM,CAAC,CAAC,GAAG;oBAChC,IAAK,MAAM,OAAO,OAAQ;wBACxB,WAAW,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;oBAChC;gBACF;gBACA,YAAY,YAAY,GAAG,GAAG,OAAO,CAAC,oBAAoB;gBAC1D,sEAAsE;gBACtE,YAAY,cAAc,GAAG,WAAW,aACtC,CAAC,CAAC,OAAO,cAAc;gBACzB,YAAY,mBAAmB,GAAG,WAAW,aAAa,CAAC,CAAC,OAAO,cAAc,IAAI,CAAC,CAAC,OAAO,mBAAmB;gBACjH,IAAI,QAAQ,eAAe,KAAK,MAAM;oBACpC,IACE,WAAW,aACX,CAAC,OAAO,QAAQ,IAAI,QAAQ,OAAO,YAAY,IAAI,IAAI,GACvD;wBACA,kCAAkC;wBAClC,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE,GAAG;4BACnB,IAAI,QAAQ,eAAe,IAAI,MAAM;gCACnC;4BACF;4BACA,IAAI,OAAO,OAAO,IAAI,MAAM;gCAC1B,QAAQ,eAAe,CACrB,OAAO,QAAQ,EACf,OAAO,YAAY,EACnB;4BAEJ,OAAO;gCACL,QAAQ,eAAe,CACrB,OAAO,QAAQ,EACf,OAAO,QAAQ,EACf;gCAEF,qCAAqC;gCACrC,OAAO,YAAY,OAAO;gCAC1B,OAAO,YAAY,QAAQ;gCAC3B,OAAO,YAAY,YAAY;gCAC/B,QAAQ,GAAG,CAAC;oCACV,QAAQ,mBAAmB,CACzB,QAAQ,eAAe,CAAC,KAAK,CAAC,GAAG;gCAErC;4BACF;wBACF;oBACF;gBACF;gBACA,OAAO;YACT;QACF;QACA,MAAM,CAAC;YACL,QAAQ,QAAQ,CAAC;YACjB,IAAI,WAAW,MAAM;gBACnB,gDAAgD;gBAChD,QAAQ,cAAc;YACxB;YACA;YACA,OAAO;gBACL,QAAQ;oBACN,MAAM,cAAc,OAAO,QAAQ,CAAC,KAAK,KAAK;oBAC9C,IACE,YAAY,QAAQ,IAAI,QAAQ,YAAY,YAAY,IAAI,MAC5D;wBACA,IACE,2EAA2E;wBAC3E,+EAA+E;wBAC/E,sBAAsB;wBACtB,yBACA,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAClC,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,MACvC,MACN;4BACA,wBAAwB;4BACxB,IACE,YAAY,YAAY,KAAK,SAC7B,CAAC,YAAY,cAAc,EAC3B;gCACA,MAAM,mBAAmB,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK;gCAC3D;;iBAEC,GACD,MAAM,KAAK,oBAAoB,iBAAiB,WAAW;gCAC3D,IAAI,IAAI;oCACN,GAAG,aAAa;gCAClB;4BACF;4BACA,QAAQ,GAAG,CAAC;gCACV,kBAAkB,GAAG,AAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC;oCAC/C,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,YAAY,YAAY;oCACpD,QAAQ,mBAAmB,CAAC,KAAK,KAAK,CAAC,GAAG;gCAC5C,GAAG,sWAAA,CAAA,iBAAc;4BACnB;wBACF;oBACF;gBACF;gBACA,SAAS;oBACP,QAAQ,OAAO;gBACjB;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,MAAM,2BAA2B,CAAC,IAAI,QAAQ;IAC5C,IAAI,WAAW,QAAQ,OAAO,MAAM,KAAK,QAAQ,OAAO,IAAI,KAAK,MAAM;QACrE,IAAI,OAAO,IAAI,KAAK,OAAO;YACzB,GAAG,YAAY,CAAC,IAAI,yNAAA,CAAA,eAAY,CAAC,GAAG,GAAG;QACzC,OAAO,IAAI,OAAO,IAAI,KAAK,QAAQ;YACjC,MAAM,SAAS,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC9C,QAAQ,GAAG,EACX,QAAQ,IAAI,EACZ,OAAO,MAAM,EACb,QAAQ,OAAO;YAEjB,GAAG,YAAY,CAAC,yNAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE;QAC/C,OAAO;YACL,MAAM,SAAS,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC9C,QAAQ,GAAG,EACX,QAAQ,IAAI,EACZ,OAAO,MAAM,EACb,QAAQ,OAAO;YAEjB,MAAM,OAAO,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC5C,QAAQ,GAAG,EACX,QAAQ,IAAI,EACZ,OAAO,IAAI,EACX,QAAQ,OAAO;YAEjB,IAAI,WAAW,QAAQ,SAAS,MAAM;gBACpC,MAAM,MAAM,yNAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC;gBACzE,GAAG,YAAY,CAAC;YAClB;QACF;IACF;AACF;AAMO,MAAM,uBAAuB,CAAC,WAAW,QAAU,CAAC;QACzD,MAAM,gBAAgB,GAAG,AAAC,MAAM,SAAS,CAAE,MAAM;QACjD,QAAQ,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EACvC,MAAM,SAAS,CAAC,MAAM,EACtB,UAAU,IAAI,EACd,UAAU,OAAO;QAEnB,MAAM,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EACrC,MAAM,SAAS,CAAC,IAAI,EACpB,UAAU,IAAI,EACd,UAAU,OAAO;IAErB,CAAC;AAOM,MAAM;IACX;;;GAGC,GACD,YAAa,YAAY,EAAE,UAAU,IAAI,KAAK,CAAE;QAC9C,IAAI,CAAC,IAAI,GAAG;QACZ;;;KAGC,GACD,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,GAAG,GAAG,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;QACrB,IAAI,CAAC,OAAO,GAAG;QACf;;;;KAIC,GACD,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QACnD;;KAEC,GACD,aAAa;QACb,IAAI,CAAC,GAAG,GAAG,aAAa,GAAG;QAC3B;;KAEC,GACD,IAAI,CAAC,0BAA0B,GAAG;QAClC,IAAI,CAAC,qBAAqB,GAAG;YAC3B,IAAI,IAAI,CAAC,0BAA0B,KAAK,QAAQ,IAAI,CAAC,eAAe,IAAI,MAAM;gBAC5E,IAAI,CAAC,0BAA0B,GAAG,qBAChC,IAAI,EACJ,IAAI,CAAC,eAAe,CAAC,KAAK;YAE9B;QACF;QACA,IAAI,CAAC,oBAAoB,GAAG;YAC1B,IAAI,CAAC,0BAA0B,GAAG;QACpC;QACA,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEA;;;;GAIC,GACD,IAAI,MAAO;QACT,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,gBAAgB;IAC/D;IAEA,uBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI,OAAO;QAC7C,IAAI,yLAAA,CAAA,YAAqB,IAAI,IAAI,CAAC,mBAAmB,KAAK,MAAM;YAC9D,wFAAwF;YACxF,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE,GAAG;gBACnB,IAAI,CAAC,mBAAmB,GAAG;YAC7B;YACA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,qBAAqB;QACvD;QACA,OAAO,IAAI,CAAC,mBAAmB;IACjC;IAEA,wBAAyB;QACvB,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,YAAY;QAEzD,IAAI,aAAa,QAAQ,UAAU,UAAU,IAAI,MAAM,OAAO;QAE9D,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW;QACpD,MAAM,QAAQ,CAAC,UAAU,UAAU,EAAE,UAAU,YAAY;QAC3D,MAAM,MAAM,CAAC,UAAU,SAAS,EAAE,UAAU,WAAW;QAEvD,wEAAwE;QACxE,6EAA6E;QAC7E,2DAA2D;QAC3D,MAAM,QAAQ,MAAM,cAAc;QAClC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,uEAAuE;YACvE,IAAI,MAAM,cAAc,IAAI,MAAM,SAAS,EAAE;gBAC3C,MAAM,kBAAkB,CAAC,MAAM,cAAc;YAC/C;QACF;QAEA,MAAM,WAAW,MAAM,qBAAqB;QAC5C,MAAM,kBAAkB,iLAAA,CAAA,MAAO,CAAC,eAAe;QAE/C,OAAO,SAAS,MAAM,IAAI,KAAK,SAAS,KAAK,IAAI,KAC/C,SAAS,IAAI,IACX,CAAC,OAAO,UAAU,IAAI,gBAAgB,WAAW,IAAI,CAAC,KACxD,SAAS,GAAG,IAAI,CAAC,OAAO,WAAW,IAAI,gBAAgB,YAAY,IAAI,CAAC;IAC5E;IAEA;;;GAGC,GACD,eAAgB,QAAQ,EAAE,YAAY,EAAE;QACtC,IAAI,CAAC,cAAc;YACjB,eAAe,CAAA,GAAA,wLAAA,CAAA,iBAAgB,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,kBAAiB,AAAD,KAAK,IAAI;QAC3D;QACA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAC3B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,sWAAA,CAAA,iBAAc,EAAE;YAAE;YAAU;QAAa;IAE9D;IAEA,mBAAoB;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,GAAG,CAAC;YACP,MAAM,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,IAC/C,uBAC+B,GAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,EACjC,IAAI,GAEN,MAAM,CAAC,CAAC,IAAM,MAAM;YACtB,aAAa;YACb,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,CACzB,GACA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAC3C,IAAI,0NAAA,CAAA,QAAY,CAAC,0NAAA,CAAA,WAAe,CAAC,IAAI,CAAC,kBAAkB,GAAG;YAE7D,GAAG,OAAO,CAAC,sWAAA,CAAA,iBAAc,EAAE;gBAAE,UAAU;gBAAM,cAAc;YAAK;YAChE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,iBAAkB;QAChB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,GAAG,CAAC;YACP,0FAA0F;YAC1F,4FAA4F;YAC5F,yDAAyD;YACzD,MAAM,MAAM,IAAI,CAAC,0BAA0B,KAAK,OAAO,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS;YAClG,MAAM,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,IAC/C,uBAC+B,GAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,EACjC,IAAI,GAEN,MAAM,CAAC,CAAC,IAAM,MAAM;YACtB,aAAa;YACb,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,CACzB,GACA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAC3C,IAAI,0NAAA,CAAA,QAAY,CAAC,0NAAA,CAAA,WAAe,CAAC,IAAI,CAAC,kBAAkB,GAAG;YAE7D,IAAI,KAAK;gBACP;;;;SAIC,GACD,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,IAAI,MAAM,EAAE,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI;gBAC3E,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI;gBAEvE,GAAG,YAAY,CAAC,yNAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,eAAe;YAC9D;YACA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAC3B,GAAG,OAAO,CAAC,sWAAA,CAAA,iBAAc,EAAE;gBAAE,gBAAgB;gBAAM,SAAS,IAAI;YAAC;QAErE;IACF;IAEA;;;;GAIC,GACD,gBAAiB,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE;QACpD;;;KAGC,GACD,IAAI,aAAa,IAAI,CAAC,GAAG;QACzB,IAAI,cAAc,IAAI,CAAC,IAAI;QAC3B,IAAI,CAAC,UAAU;YACb,WAAW,CAAA,GAAA,wLAAA,CAAA,WAAU,AAAD,EAAE,IAAI,CAAC,GAAG;QAChC;QACA,IAAI,oBAAoB,cAAc,wBAAwB,YAAY;YACxE,IAAI,CAAC,CAAC,oBAAoB,UAAU,KAAK,CAAC,CAAC,wBAAwB,UAAU,GAAG;gBAC9E,2CAA2C;gBAC3C,CAAA,GAAA,mLAAA,CAAA,iBAAoB,AAAD;YACrB;YACA,aAAa,IAAI,wLAAA,CAAA,MAAK,CAAC;gBAAE,IAAI;YAAM;YACnC,CAAA,GAAA,wLAAA,CAAA,gBAAe,AAAD,EAAE,YAAY;YAC5B,eAAe,CAAA,GAAA,wLAAA,CAAA,WAAU,AAAD,EAAE;YAC1B,CAAA,GAAA,wLAAA,CAAA,gBAAe,AAAD,EAAE,YAAY;YAC5B,WAAW,CAAA,GAAA,wLAAA,CAAA,WAAU,AAAD,EAAE;YACtB,IAAI,YAAY,KAAK,KAAK,MAAM;gBAC9B;;;SAGC,GACD,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CACpD,CAAC,MAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI;gBAEhD,cAAc,WAAW,cAAc,CAAC;YAC1C,OAAO;gBACL;;SAEC,GACD,MAAM,iBACJ,WAAW,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,EAAE;gBACjE,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,cAAa,AAAD,EAC5B,gBACA,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK;gBAE5B,MAAM,OAA8B,cAAc,CAAC,UAAU;gBAC7D,MAAM,UAAwC,KAAK,OAAO;gBAC1D,cAA4C,QAAQ,IAAI;YAC1D;QACF;QACA,iDAAiD;QACjD,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,GAAG,CAAC;YACP,WAAW,QAAQ,CAAC,CAAC;gBACnB,uEAAuE;gBACvE,0CAA0C;gBAC1C;;SAEC,GACD,MAAM,MAAM,YAAY,iBAAiB;gBACzC,IAAI,KAAK;oBACP,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACf,CAAA,GAAA,wLAAA,CAAA,wBAAuB,AAAD,EAAE,aAAa,IAAI,CAAC,SAAW;oBACvD;gBACF;gBACA;;;SAGC,GACD,MAAM,iBAAiB,CAAC,MAAM;oBAC5B,MAAM,OAAO,SAAS,UAClB,IAAI,iBAAiB,CAAC,GAAG,MAAM,IAC/B,IAAI,kBAAkB,CAAC;oBAC3B,OAAO;wBACL;wBACA;wBACA,OAAO,aACL,YAAY,YAAY,EACxB,YAAY,MAAM,EAClB;oBAEJ;gBACF;gBACA,sCAAsC;gBACtC,MAAM,kBAAkB,CAAA,GAAA,wLAAA,CAAA,0BAAyB,AAAD,EAC9C,aACA,IAAI,wLAAA,CAAA,WAAU,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,GAC3C,GAAG,CAAC,CAAC;oBACL,IACE,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU,EAAE,KAAK,EAAE,aACvC,UAAU,EAAE,KAAK,EAAE,eACnB;wBACA,OAAO,uBACL,GACA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,EACjC;4BAAE,SAAS,IAAI;4BAAO,SAAS,IAAI;wBAAM,GACzC,UACA,cACA;oBAEJ,OAAO;wBACL,sEAAsE;wBACtE,2GAA2G;wBAC3G,OAAO;oBACT;gBACF,GAAG,MAAM,CAAC,CAAC,IAAM,MAAM;gBACvB,aAAa;gBACb,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,CACzB,GACA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAC3C,IAAI,0NAAA,CAAA,QAAY,CAAC,0NAAA,CAAA,WAAe,CAAC,IAAI,CAAC,kBAAkB,GAAG;gBAE7D,IAAI,CAAC,eAAe,CAAC,QAAQ,CAC3B,GAAG,OAAO,CAAC,sWAAA,CAAA,iBAAc,EAAE;oBAAE,gBAAgB;gBAAK;YAEtD,GAAG,sWAAA,CAAA,iBAAc;QACnB;IACF;IAEA;;;GAGC,GACD,aAAc,MAAM,EAAE,WAAW,EAAE;QACjC,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM;QAClC,MAAM,YAAY,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK;QACpE,IACE,OAAO,MAAM,KAAK,KAAK,UAAU,QAAQ,IAAI,QAC7C,UAAU,YAAY,IAAI,MAC1B;YACA,iCAAiC;YACjC,IAAI,CAAC,cAAc,CAAC,UAAU,QAAQ,EAAE,UAAU,YAAY;YAC9D;QACF;QACA,IAAI,CAAC,GAAG,CAAC;YACP;;;OAGC,GACD,MAAM,UAAU,CAAC,GAAG,OAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,CAAA,GAAA,wLAAA,CAAA,wBAAuB,AAAD,EACpB,aACA,YAAY,SAAS,EACrB,CAAC;gBACC,IAAI,OAAO,WAAW,KAAK,wLAAA,CAAA,OAAM,EAAE;oBACjC,MAAM,OAAO,0BAA0B,GAAG,AAAC,mBAAmB,GAAG,AAAC,OAAQ,OAAO,CAAE,IAAI;oBACvF,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC9B;YACF;YAEF,YAAY,OAAO,CAAC,OAAO,CAAC;YAC5B,YAAY,kBAAkB,CAAC,OAAO,CAAC;YACvC,MAAM,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,IAC/C,sBAC2C,GACzC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,EACjC,IAAI,GAEN,MAAM,CAAC,CAAC,IAAM,MAAM;YACtB,aAAa;YACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,CACvB,GACA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAC3C,IAAI,0NAAA,CAAA,QAAY,CAAC,0NAAA,CAAA,WAAe,CAAC,IAAI,CAAC,kBAAkB,GAAG;YAE7D,yBAAyB,IAAI,IAAI,CAAC,0BAA0B,EAAE,IAAI;YAClE,KAAK,GAAG,OAAO,CAAC,sWAAA,CAAA,iBAAc,EAAE;gBAAE,gBAAgB;gBAAM,qBAAqB,YAAY,MAAM,YAAY,wLAAA,CAAA,cAAa;YAAC;YACzH,IACE,IAAI,CAAC,0BAA0B,KAAK,QAAQ,IAAI,CAAC,oBAAoB,IACrE;gBACA,GAAG,cAAc;YACnB;YACA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA;;GAEC,GACD,oBAAqB,GAAG,EAAE;QACxB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;YAChB,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI;YAC9C,IAAI,CAAC,0BAA0B,GAAG,qBAChC,IAAI,EACJ,IAAI,CAAC,eAAe,CAAC,KAAK;QAE9B,GAAG,sWAAA,CAAA,iBAAc;IACnB;IAEA;;;GAGC,GACD,SAAU,eAAe,EAAE;QACzB,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,IAAI,CAAC,OAAO;QAC9C,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,yBAAyB,IAAI,CAAC,qBAAqB;QAC/D,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,wBAAwB,IAAI,CAAC,oBAAoB;QAC7D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB;IAC7C;IAEA,UAAW;QACT,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM;QAClC,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB;QAC7C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,qBAAqB;QAChE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,oBAAoB;IAChE;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,wBAAwB,CAC5B,IACA,QACA,MACA,UACA,cACA;IAEA,MAAM,OAAmC,KAAK,OAAO,CAAC,GAAG,CAAC;IAC1D,IAAI,SAAS,WAAW;QACtB,IAAI,cAAc,wLAAA,CAAA,aAAY,EAAE;YAC9B,OAAO,uBACL,IACA,QACA,MACA,UACA,cACA;QAEJ,OAAO;YACL,MAAM,CAAA,GAAA,mLAAA,CAAA,sBAAyB,AAAD,IAAI,sCAAsC;;QAC1E;IACF;IACA,OAAO;AACT;AAYO,MAAM,yBAAyB,CACpC,IACA,QACA,MACA,UACA,cACA;IAEA,MAAM,WAAW,EAAE;IACnB;;GAEC,GACD,MAAM,iBAAiB,CAAC;QACtB,IAAI,gBAAgB,wLAAA,CAAA,aAAY,EAAE;YAChC,MAAM,IAAI,sBACR,MACA,QACA,MACA,UACA,cACA;YAEF,IAAI,MAAM,MAAM;gBACd,SAAS,IAAI,CAAC;YAChB;QACF,OAAO;YACL,yFAAyF;YACzF,yFAAyF;YACzF,SAAS;YACT,MAAM,YAAY,0BAA0B,GAAI,KAAK,KAAK,CAAC,KAAK,EAAE,SAAU;YAC5E,IAAI,qBAAqB,wLAAA,CAAA,OAAM,IAAI,CAAC,UAAU,KAAK,CAAC,OAAO,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,GAAG,CAAC,QAAQ,EAAE;gBACnH,KAAK,UAAU,CAAC;oBACd;wBAAE,QAAQ,KAAK,MAAM;oBAAC;uBACnB,UAAU,OAAO;iBACrB;gBACD,UAAU,GAAG,CAAC,QAAQ,CAAC,CAAA;oBACrB,UAAU,KAAK,CAAC,MAAM,CAAC;gBACzB;YACF;YACA,wCAAwC;YACxC,MAAM,KAAK,yBACT,MACA,QACA,MACA,UACA,cACA;YAEF,IAAI,OAAO,MAAM;gBACf,GAAG,OAAO,CAAC,CAAC;oBACV,IAAI,cAAc,MAAM;wBACtB,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF;IACF;IACA,IAAI,aAAa,aAAa,iBAAiB,WAAW;QACxD,GAAG,OAAO,GAAG,OAAO,CAAC;IACvB,OAAO;QACL,CAAA,GAAA,wLAAA,CAAA,0BAAyB,AAAD,EAAE,IAAI,IAAI,wLAAA,CAAA,WAAU,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,GACtE,OAAO,CAAC;IACb;IACA,IAAI;QACF,MAAM,QAAQ,GAAG,aAAa,CAAC;QAC/B,IAAI,aAAa,WAAW;YAC1B,IAAI,CAAC,UAAiC,GAAG,KAAK,EAAG,WAAW;gBAC1D,MAAM,OAAO,GAAG,iBACZ,eAAe,WAAW,mBAAmB,GAAG,AAAC,GAAG,KAAK,CAAE,EAAE,IAC7D;oBAAE,MAAM;gBAAU;YACxB,OAAO,IAAI,CAAC,UAAiC,GAAG,KAAK,EAAG,eAAe;gBACrE,MAAM,OAAO,GAAG,iBACZ,eAAe,SAAS,mBAAmB,GAAG,AAAC,GAAG,KAAK,CAAE,EAAE,IAC3D;oBAAE,MAAM;gBAAQ;YACtB;QACF;QACA,MAAM,OAAO,OAAO,IAAI,CAAC,GAAG,QAAQ,EAAE,OAAO;QAC7C,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI;QACrB,OAAO;IACT,EAAE,OAAO,GAAG;QACV,8FAA8F;QAC9F,kBAAkB,GAAG,AAAC,GAAG,GAAG,CAAE,QAAQ,CAAC,CAAC;YACtC,mBAAmB,GAAG,AAAC,GAAG,KAAK,CAAE,MAAM,CAAC;QAC1C,GAAG,sWAAA,CAAA,iBAAc;QACjB,KAAK,OAAO,CAAC,MAAM,CAAC;QACpB,OAAO;IACT;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,2BAA2B,CAC/B,MACA,QACA,OACA,UACA,cACA;IAEA,MAAM,QAAQ,EAAE;IAChB,MAAM,SAAS,KAAK,OAAO,CAAC,UAAU,cAAc;IACpD,IAAI;QACF,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;YACvB,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE,kBAAkB,MAAM,UAAU,EAAE;QAC3E;IACF,EAAE,OAAO,GAAG;QACV,8FAA8F;QAC9F,kBAAkB,GAAG,AAAC,KAAK,GAAG,CAAE,QAAQ,CAAC,CAAC;YACxC,mBAAmB,GAAG,AAAC,KAAK,KAAK,CAAE,MAAM,CAAC;QAC5C,GAAG,sWAAA,CAAA,iBAAc;QACjB,OAAO;IACT;IACA,aAAa;IACb,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,0BAA0B,CAAC,OAAO;IACtC,MAAM,OAAO,IAAI,wLAAA,CAAA,UAAS;IAC1B,MAAM,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;YACjC,aAAa;YACb,QAAQ,KAAK,IAAI;YACjB,YAAY,kBAAkB,KAAK,KAAK,EAAE;QAC5C,CAAC;IACD,KAAK,UAAU,CAAC;IAChB,KAAK,OAAO,CAAC,GAAG,CAAC,MAAM;IACvB,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,4BAA4B,CAAC,MAAM;IACvC,MAAM,OAAO,IAAI,wLAAA,CAAA,aAAY,CAAC,KAAK,IAAI,CAAC,IAAI;IAC5C,IAAK,MAAM,OAAO,KAAK,KAAK,CAAE;QAC5B,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;QAC3B,IAAI,QAAQ,QAAQ,QAAQ,WAAW;YACrC,KAAK,YAAY,CAAC,KAAK;QACzB;IACF;IACA,KAAK,MAAM,CACT,GACA,sBAAsB,MAAM,GAAG,CAAC,CAAC,IAC/B,gCAAgC,GAAG;IAGvC,KAAK,OAAO,CAAC,GAAG,CAAC,MAAM;IACvB,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,kCAAkC,CAAC,MAAM,OAC7C,gBAAgB,QACZ,wBAAwB,MAAM,QAC9B,0BAA0B,MAAM;AAEtC;;CAEC,GACD,MAAM,WAAW,CAAC,MAAQ,OAAO,QAAQ,YAAY,QAAQ;AAE7D;;;CAGC,GACD,MAAM,aAAa,CAAC,QAAQ;IAC1B,MAAM,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAQ,MAAM,CAAC,IAAI,KAAK;IACjE,IAAI,KACF,KAAK,MAAM,KACT,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM;IAC1F,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI,IAAK;QAC1C,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,IAAI;QACrB,MAAM,IAAI,MAAM,CAAC,IAAI;QACrB,KAAK,QAAQ,aAAa,MAAM,KAC7B,SAAS,MAAM,SAAS,MAAM,WAAW,GAAG;IACjD;IACA,OAAO;AACT;AAEA;;CAEC,GAED;;;CAGC,GACD,MAAM,wBAAwB,CAAC;IAC7B,MAAM,IAAI,MAAM,OAAO,CAAC,OAAO;IAC/B,MAAM,MAAM,EAAE;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,MAAM,IAAI,CAAC,CAAC,EAAE;QACd,IAAI,EAAE,MAAM,EAAE;YACZ,MAAM,YAAY,EAAE;YACpB,IAAK,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,IAAI,MAAM,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAE;gBACnE,UAAU,IAAI,CAAC;YACjB;YACA;YACA,IAAI,IAAI,CAAC;QACX,OAAO;YACL,IAAI,IAAI,CAAC;QACX;IACF;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,MAAM,kBAAkB,CAAC,OAAO;IAC9B,MAAM,QAAQ,MAAM,OAAO;IAC3B,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,IACnC,MAAM,KAAK,CAAC,wCAAwC,GAAG,CAAC,GAAG,IACzD,EAAE,MAAM,KAAK,gBAAgB,GAAG,AAAC,MAAM,CAAC,EAAE,CAAE,IAAI,IAChD,CAAA,GAAA,oLAAA,CAAA,OAAW,AAAD,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IACjE,CAAA,GAAA,oLAAA,CAAA,QAAY,AAAD,EAAE,EAAE,UAAU,EAAE,CAAC,MAAM;YAChC,MAAM,WAAW,eAAe;YAChC,MAAM,SAAS,MAAM,CAAC,EAAE,CAAC,KAAK;YAC9B,OAAO,WAAW,MAAM,OAAO,IAAI,CAAC,sBAAsB,GAAG,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;QACrG;AAEN;AAEA;;;CAGC,GACD,MAAM,kBAAkB,CAAC,OAAO;IAC9B,IACE,iBAAiB,wLAAA,CAAA,aAAY,IAAI,CAAC,CAAC,iBAAiB,KAAK,KACzD,cAAc,OAAO,QACrB;QACA,MAAM,oBAAoB,sBAAsB;QAChD,OAAO,MAAM,OAAO,KAAK,kBAAkB,MAAM,IAC/C,WAAW,MAAM,aAAa,IAAI,MAAM,KAAK,KAC7C,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,QAAQ,IAC7B,gBAAgB,QAAQ,iBAAiB,CAAC,EAAE;IAElD;IACA,OAAO,iBAAiB,wLAAA,CAAA,UAAS,IAAI,iBAAiB,SACpD,gBAAgB,OAAO;AAC3B;AAEA;;;CAGC,GACD,MAAM,iBAAiB,CAAC,QAAQ,WAC9B,WAAW,YACV,kBAAkB,SAAS,oBAAoB,SAC9C,OAAO,MAAM,KAAK,SAAS,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,GAAG,IACtD,QAAQ,CAAC,EAAE,KAAK;AAGpB;;;;;CAKC,GACD,MAAM,6BAA6B,CAAC,OAAO,OAAO;IAChD,MAAM,YAAY,MAAM,OAAO;IAC/B,MAAM,YAAY,sBAAsB;IACxC,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,WAAW;IACnC,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,IAAI,mBAAmB;IACvB,MAAO,OAAO,QAAQ,OAAQ;QAC5B,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,IAAI,eAAe,KAAK,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ;YAClD,mBAAmB,KAAK,yBAAyB;;QACnD,OAAO,IAAI,CAAC,gBAAgB,OAAO,QAAQ;YACzC;QACF;IACF;IACA,MAAO,OAAO,QAAQ,QAAQ,QAAS;QACrC,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;QAC/C,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;QAC/C,IAAI,eAAe,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,SAAS;YACpD,mBAAmB;QACrB,OAAO,IAAI,CAAC,gBAAgB,QAAQ,SAAS;YAC3C;QACF;IACF;IACA,OAAO;QACL,gBAAgB,OAAO;QACvB;IACF;AACF;AAEA;;CAEC,GACD,MAAM,aAAa,CAAC;IAClB,IAAI,MAAM;IACV;;GAEC,GACD,IAAI,IAAI,MAAM,MAAM;IACpB,MAAM,SAAS,CAAC;IAChB,MAAO,MAAM,KAAM;QACjB,IAAI,CAAC,EAAE,OAAO,EAAE;YACd,IAAI,EAAE,SAAS,IAAI,EAAE,OAAO,YAAY,wLAAA,CAAA,gBAAe,EAAE;gBACvD,OAAO,EAAE,OAAO,CAAC,GAAG;YACtB,OAAO,IAAI,EAAE,OAAO,YAAY,wLAAA,CAAA,gBAAe,EAAE;gBAC/C,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG;YAC1B;QACF;QACA,IAAI,EAAE,KAAK;IACb;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA;;;;;;CAMC,GACD,MAAM,cAAc,CAAC,OAAO,QAAQ;IAClC,KAAK,OAAO,CAAC,GAAG,CAAC,OAAO;IACxB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,WAAW;IACnC,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC;YACjC,QAAQ,gBAAgB,GAAG,AAAC,EAAG,IAAI;YACnC,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ,kBAAkB,EAAE,KAAK,EAAE;QACnE,CAAC;IACD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EACzC,KACA,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IAEpC,MAAM,MAAM,CAAC,OAAO;IACpB,MAAM,MAAM,CAAC,OAAO;IACpB,MAAM,UAAU,CACd,QAAQ,GAAG,CAAC,CAAC,IAAM,CAAC;YAAE,QAAQ,EAAE,MAAM,CAAC,MAAM;YAAE,YAAY,EAAE,UAAU;QAAC,CAAC;AAE7E;AAEA,MAAM,sBAAsB;AAIrB,MAAM,iBAAiB,CAAA,WAAY,oBAAoB,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI;AAQ9E,MAAM,oBAAoB,CAAC,OAAO;IACvC;;GAEC,GACD,MAAM,QAAQ,EAAE;IAChB,IAAK,MAAM,YAAY,MAAO;QAC5B,6BAA6B;QAC7B,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,eAAe,WAAW,KAAK,CAAC,SAAS;IAClE;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,MAAM,oBAAoB,CAAC,OAAO;IAChC,MAAM,SAAS,CAAC;IAChB,MAAM,OAAO,CAAC,CAAC;QACb,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;YAChC,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,iBAAkB,AAAD,EAAE,KAAK,OAAO,EAAE,KAAK,IAAI,EAAE,IAAM,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;YACrG,MAAM,CAAC,gBAAgB,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAA,GAAA,4VAAA,CAAA,aAAgB,AAAD,EAAE,KAAK,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK;QAC/G;IACF;IACA,OAAO;AACT;AAeO,MAAM,kBAAkB,CAAC,GAAG,cAAc,OAAO;IACtD,IACE,wBAAwB,wLAAA,CAAA,aAAY,IACpC,aAAa,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,EACzC;QACA,MAAM,IAAI,MAAM;IAClB;IACA,KAAK,OAAO,CAAC,GAAG,CAAC,cAAc;IAC/B,oBAAoB;IACpB,IAAI,wBAAwB,wLAAA,CAAA,aAAY,EAAE;QACxC,MAAM,YAAY,aAAa,aAAa;QAC5C,MAAM,SAAS,MAAM,KAAK;QAC1B,IAAK,MAAM,OAAO,OAAQ;YACxB,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM;gBACxB,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,QAAQ,WAAW;oBACvD,aAAa,YAAY,CAAC,KAAK,MAAM,CAAC,IAAI;gBAC5C;YACF,OAAO;gBACL,aAAa,eAAe,CAAC;YAC/B;QACF;QACA,+CAA+C;QAC/C,IAAK,MAAM,OAAO,UAAW;YAC3B,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;gBAC7B,aAAa,eAAe,CAAC;YAC/B;QACF;IACF;IACA,kBAAkB;IAClB,MAAM,YAAY,sBAAsB;IACxC,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,YAAY,aAAa,OAAO;IACtC,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,WAAW;IACnC,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,6CAA6C;IAC7C,MAAO,OAAO,QAAQ,OAAQ;QAC5B,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ;YACnD,IAAI,gBAAgB,OAAO,QAAQ;gBACjC,iBAAiB;gBACjB,KAAK,OAAO,CAAC,GAAG,CAAC,OAAO;YAC1B,OAAO;gBACL;YACF;QACF;IACF;IACA,8CAA8C;IAC9C,MAAO,QAAQ,OAAO,QAAQ,QAAS;QACrC,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;QAC/C,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;QAC/C,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,SAAS;YACrD,IAAI,gBAAgB,QAAQ,SAAS;gBACnC,iBAAiB;gBACjB,KAAK,OAAO,CAAC,GAAG,CAAC,QAAQ;YAC3B,OAAO;gBACL;YACF;QACF;IACF;IACA,EAAE,QAAQ,CAAC;QACT,4BAA4B;QAC5B,MAAO,YAAY,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,EAAG;YACnE,MAAM,QAAQ,SAAS,CAAC,KAAK;YAC7B,MAAM,QAAQ,SAAS,CAAC,KAAK;YAC7B,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;YAC/C,MAAM,SAAS,SAAS,CAAC,YAAY,QAAQ,EAAE;YAC/C,IAAI,iBAAiB,wLAAA,CAAA,UAAS,IAAI,iBAAiB,OAAO;gBACxD,IAAI,CAAC,gBAAgB,OAAO,QAAQ;oBAClC,YAAY,OAAO,OAAO;gBAC5B;gBACA,QAAQ;YACV,OAAO;gBACL,IAAI,aAAa,iBAAiB,wLAAA,CAAA,aAAY,IAC5C,cAAc,OAAO;gBACvB,IAAI,cAAc,kBAAkB,wLAAA,CAAA,aAAY,IAC9C,cAAc,QAAQ;gBACxB,IAAI,cAAc,aAAa;oBAC7B,uCAAuC;oBACvC,MAAM,eAAe,2BACU,OACD,OAC5B;oBAEF,MAAM,gBAAgB,2BACS,QACD,QAC5B;oBAEF,IACE,aAAa,gBAAgB,IAAI,CAAC,cAAc,gBAAgB,EAChE;wBACA,cAAc;oBAChB,OAAO,IACL,CAAC,aAAa,gBAAgB,IAAI,cAAc,gBAAgB,EAChE;wBACA,aAAa;oBACf,OAAO,IACL,aAAa,cAAc,GAAG,cAAc,cAAc,EAC1D;wBACA,aAAa;oBACf,OAAO;wBACL,cAAc;oBAChB;gBACF;gBACA,IAAI,YAAY;oBACd,gBACE,GAC8B,OACF,OAC5B;oBAEF,QAAQ;gBACV,OAAO,IAAI,aAAa;oBACtB,gBACE,GAC8B,QACF,QAC5B;oBAEF,SAAS;gBACX,OAAO;oBACL,KAAK,OAAO,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC;oBACrC,aAAa,MAAM,CAAC,MAAM;oBAC1B,aAAa,MAAM,CAAC,MAAM;wBACxB,gCAAgC,OAAO;qBACxC;oBACD,QAAQ;gBACV;YACF;QACF;QACA,MAAM,UAAU,YAAY,OAAO;QACnC,IACE,cAAc,KAAK,cAAc,KAAK,SAAS,CAAC,EAAE,YAAY,wLAAA,CAAA,UAAS,EACvE;YACA,KAAK,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAChC,qEAAqE;YACrE,2FAA2F;YAC3F,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM;QAC5C,OAAO,IAAI,UAAU,GAAG;YACtB,aAAa,KAAK,CAAC,MAAM,OAAO,SAAS,OAAO,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,MAAM,CAAC;YAC7E,aAAa,MAAM,CAAC,MAAM;QAC5B;QACA,IAAI,OAAO,QAAQ,WAAW;YAC5B,MAAM,MAAM,EAAE;YACd,IAAK,IAAI,IAAI,MAAM,IAAI,YAAY,OAAO,IAAK;gBAC7C,IAAI,IAAI,CAAC,gCAAgC,SAAS,CAAC,EAAE,EAAE;YACzD;YACA,aAAa,MAAM,CAAC,MAAM;QAC5B;IACF,GAAG,sWAAA,CAAA,iBAAc;AACnB;AAEA;;;;CAIC,GACD,MAAM,gBAAgB,CAAC,UAAU,QAC/B,CAAC,CAAC,iBAAiB,KAAK,KAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/lib.js"], "sourcesContent": ["import { updateYFragment, createNodeFromYElement, yattr2markname, createEmptyMeta } from './plugins/sync-plugin.js' // eslint-disable-line\nimport { ySyncPluginKey } from './plugins/keys.js'\nimport * as Y from 'yjs'\nimport { EditorView } from 'prosemirror-view' // eslint-disable-line\nimport { Node, Schema, Fragment } from 'prosemirror-model' // eslint-disable-line\nimport * as error from 'lib0/error'\nimport * as map from 'lib0/map'\nimport * as eventloop from 'lib0/eventloop'\n\n/**\n * Either a node if type is YXmlElement or an Array of text nodes if YXmlText\n * @typedef {Map<Y.AbstractType, Node | Array<Node>>} ProsemirrorMapping\n */\n\n/**\n * Is null if no timeout is in progress.\n * Is defined if a timeout is in progress.\n * Maps from view\n * @type {Map<EditorView, Map<any, any>>|null}\n */\nlet viewsToUpdate = null\n\nconst updateMetas = () => {\n  const ups = /** @type {Map<EditorView, Map<any, any>>} */ (viewsToUpdate)\n  viewsToUpdate = null\n  ups.forEach((metas, view) => {\n    const tr = view.state.tr\n    const syncState = ySyncPluginKey.getState(view.state)\n    if (syncState && syncState.binding && !syncState.binding.isDestroyed) {\n      metas.forEach((val, key) => {\n        tr.setMeta(key, val)\n      })\n      view.dispatch(tr)\n    }\n  })\n}\n\nexport const setMeta = (view, key, value) => {\n  if (!viewsToUpdate) {\n    viewsToUpdate = new Map()\n    eventloop.timeout(0, updateMetas)\n  }\n  map.setIfUndefined(viewsToUpdate, view, map.create).set(key, value)\n}\n\n/**\n * Transforms a Prosemirror based absolute position to a Yjs Cursor (relative position in the Yjs model).\n *\n * @param {number} pos\n * @param {Y.XmlFragment} type\n * @param {ProsemirrorMapping} mapping\n * @return {any} relative position\n */\nexport const absolutePositionToRelativePosition = (pos, type, mapping) => {\n  if (pos === 0) {\n    // if the type is later populated, we want to retain the 0 position (hence assoc=-1)\n    return Y.createRelativePositionFromTypeIndex(type, 0, type.length === 0 ? -1 : 0)\n  }\n  /**\n   * @type {any}\n   */\n  let n = type._first === null ? null : /** @type {Y.ContentType} */ (type._first.content).type\n  while (n !== null && type !== n) {\n    if (n instanceof Y.XmlText) {\n      if (n._length >= pos) {\n        return Y.createRelativePositionFromTypeIndex(n, pos, type.length === 0 ? -1 : 0)\n      } else {\n        pos -= n._length\n      }\n      if (n._item !== null && n._item.next !== null) {\n        n = /** @type {Y.ContentType} */ (n._item.next.content).type\n      } else {\n        do {\n          n = n._item === null ? null : n._item.parent\n          pos--\n        } while (n !== type && n !== null && n._item !== null && n._item.next === null)\n        if (n !== null && n !== type) {\n          // @ts-gnore we know that n.next !== null because of above loop conditition\n          n = n._item === null ? null : /** @type {Y.ContentType} */ (/** @type Y.Item */ (n._item.next).content).type\n        }\n      }\n    } else {\n      const pNodeSize = /** @type {any} */ (mapping.get(n) || { nodeSize: 0 }).nodeSize\n      if (n._first !== null && pos < pNodeSize) {\n        n = /** @type {Y.ContentType} */ (n._first.content).type\n        pos--\n      } else {\n        if (pos === 1 && n._length === 0 && pNodeSize > 1) {\n          // edge case, should end in this paragraph\n          return new Y.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? Y.findRootTypeKey(n) : null, null)\n        }\n        pos -= pNodeSize\n        if (n._item !== null && n._item.next !== null) {\n          n = /** @type {Y.ContentType} */ (n._item.next.content).type\n        } else {\n          if (pos === 0) {\n            // set to end of n.parent\n            n = n._item === null ? n : n._item.parent\n            return new Y.RelativePosition(n._item === null ? null : n._item.id, n._item === null ? Y.findRootTypeKey(n) : null, null)\n          }\n          do {\n            n = /** @type {Y.Item} */ (n._item).parent\n            pos--\n          } while (n !== type && /** @type {Y.Item} */ (n._item).next === null)\n          // if n is null at this point, we have an unexpected case\n          if (n !== type) {\n            // We know that n._item.next is defined because of above loop condition\n            n = /** @type {Y.ContentType} */ (/** @type {Y.Item} */ (/** @type {Y.Item} */ (n._item).next).content).type\n          }\n        }\n      }\n    }\n    if (n === null) {\n      throw error.unexpectedCase()\n    }\n    if (pos === 0 && n.constructor !== Y.XmlText && n !== type) { // TODO: set to <= 0\n      return createRelativePosition(n._item.parent, n._item)\n    }\n  }\n  return Y.createRelativePositionFromTypeIndex(type, type._length, type.length === 0 ? -1 : 0)\n}\n\nconst createRelativePosition = (type, item) => {\n  let typeid = null\n  let tname = null\n  if (type._item === null) {\n    tname = Y.findRootTypeKey(type)\n  } else {\n    typeid = Y.createID(type._item.id.client, type._item.id.clock)\n  }\n  return new Y.RelativePosition(typeid, tname, item.id)\n}\n\n/**\n * @param {Y.Doc} y\n * @param {Y.XmlFragment} documentType Top level type that is bound to pView\n * @param {any} relPos Encoded Yjs based relative position\n * @param {ProsemirrorMapping} mapping\n * @return {null|number}\n */\nexport const relativePositionToAbsolutePosition = (y, documentType, relPos, mapping) => {\n  const decodedPos = Y.createAbsolutePositionFromRelativePosition(relPos, y)\n  if (decodedPos === null || (decodedPos.type !== documentType && !Y.isParentOf(documentType, decodedPos.type._item))) {\n    return null\n  }\n  let type = decodedPos.type\n  let pos = 0\n  if (type.constructor === Y.XmlText) {\n    pos = decodedPos.index\n  } else if (type._item === null || !type._item.deleted) {\n    let n = type._first\n    let i = 0\n    while (i < type._length && i < decodedPos.index && n !== null) {\n      if (!n.deleted) {\n        const t = /** @type {Y.ContentType} */ (n.content).type\n        i++\n        if (t instanceof Y.XmlText) {\n          pos += t._length\n        } else {\n          pos += /** @type {any} */ (mapping.get(t)).nodeSize\n        }\n      }\n      n = /** @type {Y.Item} */ (n.right)\n    }\n    pos += 1 // increase because we go out of n\n  }\n  while (type !== documentType && type._item !== null) {\n    // @ts-ignore\n    const parent = type._item.parent\n    // @ts-ignore\n    if (parent._item === null || !parent._item.deleted) {\n      pos += 1 // the start tag\n      let n = /** @type {Y.AbstractType} */ (parent)._first\n      // now iterate until we found type\n      while (n !== null) {\n        const contentType = /** @type {Y.ContentType} */ (n.content).type\n        if (contentType === type) {\n          break\n        }\n        if (!n.deleted) {\n          if (contentType instanceof Y.XmlText) {\n            pos += contentType._length\n          } else {\n            pos += /** @type {any} */ (mapping.get(contentType)).nodeSize\n          }\n        }\n        n = n.right\n      }\n    }\n    type = /** @type {Y.AbstractType} */ (parent)\n  }\n  return pos - 1 // we don't count the most outer tag, because it is a fragment\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror fragment.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nexport const yXmlFragmentToProseMirrorFragment = (yXmlFragment, schema) => {\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    createNodeFromYElement(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      createEmptyMeta()\n    )\n  ).filter((n) => n !== null)\n  return Fragment.fromArray(fragmentContent)\n}\n\n/**\n * Utility function for converting an Y.Fragment to a ProseMirror node.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n */\nexport const yXmlFragmentToProseMirrorRootNode = (yXmlFragment, schema) =>\n  schema.topNodeType.create(null, yXmlFragmentToProseMirrorFragment(yXmlFragment, schema))\n\n/**\n * The initial ProseMirror content should be supplied by Yjs. This function transforms a Y.Fragment\n * to a ProseMirror Doc node and creates a mapping that is used by the sync plugin.\n *\n * @param {Y.XmlFragment} yXmlFragment\n * @param {Schema} schema\n *\n * @todo deprecate mapping property\n */\nexport const initProseMirrorDoc = (yXmlFragment, schema) => {\n  const meta = createEmptyMeta()\n  const fragmentContent = yXmlFragment.toArray().map((t) =>\n    createNodeFromYElement(\n      /** @type {Y.XmlElement} */ (t),\n      schema,\n      meta\n    )\n  ).filter((n) => n !== null)\n  const doc = schema.topNodeType.create(null, Fragment.fromArray(fragmentContent))\n  return { doc, meta, mapping: meta.mapping }\n}\n\n/**\n * Utility method to convert a Prosemirror Doc Node into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Node} doc\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nexport function prosemirrorToYDoc (doc, xmlFragment = 'prosemirror') {\n  const ydoc = new Y.Doc()\n  const type = /** @type {Y.XmlFragment} */ (ydoc.get(xmlFragment, Y.XmlFragment))\n  if (!type.doc) {\n    return ydoc\n  }\n\n  prosemirrorToYXmlFragment(doc, type)\n  return type.doc\n}\n\n/**\n * Utility method to update an empty Y.XmlFragment with content from a Prosemirror Doc Node.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * Note: The Y.XmlFragment does not need to be part of a Y.Doc document at the time that this\n * method is called, but it must be added before any other operations are performed on it.\n *\n * @param {Node} doc prosemirror document.\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nexport function prosemirrorToYXmlFragment (doc, xmlFragment) {\n  const type = xmlFragment || new Y.XmlFragment()\n  const ydoc = type.doc ? type.doc : { transact: (transaction) => transaction(undefined) }\n  updateYFragment(ydoc, type, doc, { mapping: new Map(), isOMark: new Map() })\n  return type\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON into a Y.Doc.\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {string} xmlFragment\n * @return {Y.Doc}\n */\nexport function prosemirrorJSONToYDoc (schema, state, xmlFragment = 'prosemirror') {\n  const doc = Node.fromJSON(schema, state)\n  return prosemirrorToYDoc(doc, xmlFragment)\n}\n\n/**\n * Utility method to convert Prosemirror compatible JSON to a Y.XmlFragment\n *\n * This can be used when importing existing content to Y.Doc for the first time,\n * note that this should not be used to rehydrate a Y.Doc from a database once\n * collaboration has begun as all history will be lost\n *\n * @param {Schema} schema\n * @param {any} state\n * @param {Y.XmlFragment} [xmlFragment] If supplied, an xml fragment to be\n *   populated from the prosemirror state; otherwise a new XmlFragment will be created.\n * @return {Y.XmlFragment}\n */\nexport function prosemirrorJSONToYXmlFragment (schema, state, xmlFragment) {\n  const doc = Node.fromJSON(schema, state)\n  return prosemirrorToYXmlFragment(doc, xmlFragment)\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.Doc} ydoc\n * @return {Node}\n */\nexport function yDocToProsemirror (schema, ydoc) {\n  const state = yDocToProsemirrorJSON(ydoc)\n  return Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.XmlFragment to a Prosemirror Doc node.\n *\n * @param {Schema} schema\n * @param {Y.XmlFragment} xmlFragment\n * @return {Node}\n */\nexport function yXmlFragmentToProsemirror (schema, xmlFragment) {\n  const state = yXmlFragmentToProsemirrorJSON(xmlFragment)\n  return Node.fromJSON(schema, state)\n}\n\n/**\n *\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.Doc} ydoc\n * @param {string} xmlFragment\n * @return {Record<string, any>}\n */\nexport function yDocToProsemirrorJSON (\n  ydoc,\n  xmlFragment = 'prosemirror'\n) {\n  return yXmlFragmentToProsemirrorJSON(ydoc.getXmlFragment(xmlFragment))\n}\n\n/**\n * @deprecated Use `yXmlFragmentToProseMirrorRootNode` instead\n *\n * Utility method to convert a Y.Doc to Prosemirror compatible JSON.\n *\n * @param {Y.XmlFragment} xmlFragment The fragment, which must be part of a Y.Doc.\n * @return {Record<string, any>}\n */\nexport function yXmlFragmentToProsemirrorJSON (xmlFragment) {\n  const items = xmlFragment.toArray()\n\n  /**\n   * @param {Y.AbstractType} item\n   */\n  const serialize = item => {\n    /**\n     * @type {Object} NodeObject\n     * @property {string} NodeObject.type\n     * @property {Record<string, string>=} NodeObject.attrs\n     * @property {Array<NodeObject>=} NodeObject.content\n     */\n    let response\n\n    // TODO: Must be a better way to detect text nodes than this\n    if (item instanceof Y.XmlText) {\n      const delta = item.toDelta()\n      response = delta.map(/** @param {any} d */ (d) => {\n        const text = {\n          type: 'text',\n          text: d.insert\n        }\n        if (d.attributes) {\n          text.marks = Object.keys(d.attributes).map((type_) => {\n            const attrs = d.attributes[type_]\n            const type = yattr2markname(type_)\n            const mark = {\n              type\n            }\n            if (Object.keys(attrs)) {\n              mark.attrs = attrs\n            }\n            return mark\n          })\n        }\n        return text\n      })\n    } else if (item instanceof Y.XmlElement) {\n      response = {\n        type: item.nodeName\n      }\n\n      const attrs = item.getAttributes()\n      if (Object.keys(attrs).length) {\n        response.attrs = attrs\n      }\n\n      const children = item.toArray()\n      if (children.length) {\n        response.content = children.map(serialize).flat()\n      }\n    } else {\n      // expected either Y.XmlElement or Y.XmlText\n      error.unexpectedCase()\n    }\n\n    return response\n  }\n\n  return {\n    type: 'doc',\n    content: items.map(serialize)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,ynBAAoH,sBAAsB;AAC1I;AACA;AAEA,0XAA2D,sBAAsB;AACjF;AACA;AACA;;;;;;;;;AAEA;;;CAGC,GAED;;;;;CAKC,GACD,IAAI,gBAAgB;AAEpB,MAAM,cAAc;IAClB,MAAM,MAAqD;IAC3D,gBAAgB;IAChB,IAAI,OAAO,CAAC,CAAC,OAAO;QAClB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE;QACxB,MAAM,YAAY,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK;QACpD,IAAI,aAAa,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,WAAW,EAAE;YACpE,MAAM,OAAO,CAAC,CAAC,KAAK;gBAClB,GAAG,OAAO,CAAC,KAAK;YAClB;YACA,KAAK,QAAQ,CAAC;QAChB;IACF;AACF;AAEO,MAAM,UAAU,CAAC,MAAM,KAAK;IACjC,IAAI,CAAC,eAAe;QAClB,gBAAgB,IAAI;QACpB,CAAA,GAAA,uLAAA,CAAA,UAAiB,AAAD,EAAE,GAAG;IACvB;IACA,CAAA,GAAA,iLAAA,CAAA,iBAAkB,AAAD,EAAE,eAAe,MAAM,iLAAA,CAAA,SAAU,EAAE,GAAG,CAAC,KAAK;AAC/D;AAUO,MAAM,qCAAqC,CAAC,KAAK,MAAM;IAC5D,IAAI,QAAQ,GAAG;QACb,oFAAoF;QACpF,OAAO,CAAA,GAAA,wLAAA,CAAA,sCAAqC,AAAD,EAAE,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;IACjF;IACA;;GAEC,GACD,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,OAAO,0BAA0B,GAAG,AAAC,KAAK,MAAM,CAAC,OAAO,CAAE,IAAI;IAC7F,MAAO,MAAM,QAAQ,SAAS,EAAG;QAC/B,IAAI,aAAa,wLAAA,CAAA,UAAS,EAAE;YAC1B,IAAI,EAAE,OAAO,IAAI,KAAK;gBACpB,OAAO,CAAA,GAAA,wLAAA,CAAA,sCAAqC,AAAD,EAAE,GAAG,KAAK,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;YAChF,OAAO;gBACL,OAAO,EAAE,OAAO;YAClB;YACA,IAAI,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,CAAC,IAAI,KAAK,MAAM;gBAC7C,IAAI,0BAA0B,GAAG,AAAC,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI;YAC9D,OAAO;gBACL,GAAG;oBACD,IAAI,EAAE,KAAK,KAAK,OAAO,OAAO,EAAE,KAAK,CAAC,MAAM;oBAC5C;gBACF,QAAS,MAAM,QAAQ,MAAM,QAAQ,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,CAAC,IAAI,KAAK,KAAK;gBAC/E,IAAI,MAAM,QAAQ,MAAM,MAAM;oBAC5B,2EAA2E;oBAC3E,IAAI,EAAE,KAAK,KAAK,OAAO,OAAO,0BAA0B,GAAG,AAAC,iBAAiB,GAAG,AAAC,EAAE,KAAK,CAAC,IAAI,CAAE,OAAO,CAAE,IAAI;gBAC9G;YACF;QACF,OAAO;YACL,MAAM,YAAY,gBAAgB,GAAG,CAAC,QAAQ,GAAG,CAAC,MAAM;gBAAE,UAAU;YAAE,CAAC,EAAE,QAAQ;YACjF,IAAI,EAAE,MAAM,KAAK,QAAQ,MAAM,WAAW;gBACxC,IAAI,0BAA0B,GAAG,AAAC,EAAE,MAAM,CAAC,OAAO,CAAE,IAAI;gBACxD;YACF,OAAO;gBACL,IAAI,QAAQ,KAAK,EAAE,OAAO,KAAK,KAAK,YAAY,GAAG;oBACjD,0CAA0C;oBAC1C,OAAO,IAAI,wLAAA,CAAA,mBAAkB,CAAC,EAAE,KAAK,KAAK,OAAO,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,KAAK,OAAO,CAAA,GAAA,wLAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,MAAM;gBACtH;gBACA,OAAO;gBACP,IAAI,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,CAAC,IAAI,KAAK,MAAM;oBAC7C,IAAI,0BAA0B,GAAG,AAAC,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI;gBAC9D,OAAO;oBACL,IAAI,QAAQ,GAAG;wBACb,yBAAyB;wBACzB,IAAI,EAAE,KAAK,KAAK,OAAO,IAAI,EAAE,KAAK,CAAC,MAAM;wBACzC,OAAO,IAAI,wLAAA,CAAA,mBAAkB,CAAC,EAAE,KAAK,KAAK,OAAO,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,KAAK,OAAO,CAAA,GAAA,wLAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,MAAM;oBACtH;oBACA,GAAG;wBACD,IAAI,mBAAmB,GAAG,AAAC,EAAE,KAAK,CAAE,MAAM;wBAC1C;oBACF,QAAS,MAAM,QAAQ,mBAAmB,GAAG,AAAC,EAAE,KAAK,CAAE,IAAI,KAAK,KAAK;oBACrE,yDAAyD;oBACzD,IAAI,MAAM,MAAM;wBACd,uEAAuE;wBACvE,IAAI,0BAA0B,GAAG,AAAC,mBAAmB,GAAG,AAAC,mBAAmB,GAAG,AAAC,EAAE,KAAK,CAAE,IAAI,CAAE,OAAO,CAAE,IAAI;oBAC9G;gBACF;YACF;QACF;QACA,IAAI,MAAM,MAAM;YACd,MAAM,CAAA,GAAA,mLAAA,CAAA,iBAAoB,AAAD;QAC3B;QACA,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,wLAAA,CAAA,UAAS,IAAI,MAAM,MAAM;YAC1D,OAAO,uBAAuB,EAAE,KAAK,CAAC,MAAM,EAAE,EAAE,KAAK;QACvD;IACF;IACA,OAAO,CAAA,GAAA,wLAAA,CAAA,sCAAqC,AAAD,EAAE,MAAM,KAAK,OAAO,EAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;AAC5F;AAEA,MAAM,yBAAyB,CAAC,MAAM;IACpC,IAAI,SAAS;IACb,IAAI,QAAQ;IACZ,IAAI,KAAK,KAAK,KAAK,MAAM;QACvB,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAiB,AAAD,EAAE;IAC5B,OAAO;QACL,SAAS,CAAA,GAAA,wLAAA,CAAA,WAAU,AAAD,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK;IAC/D;IACA,OAAO,IAAI,wLAAA,CAAA,mBAAkB,CAAC,QAAQ,OAAO,KAAK,EAAE;AACtD;AASO,MAAM,qCAAqC,CAAC,GAAG,cAAc,QAAQ;IAC1E,MAAM,aAAa,CAAA,GAAA,wLAAA,CAAA,6CAA4C,AAAD,EAAE,QAAQ;IACxE,IAAI,eAAe,QAAS,WAAW,IAAI,KAAK,gBAAgB,CAAC,CAAA,GAAA,wLAAA,CAAA,aAAY,AAAD,EAAE,cAAc,WAAW,IAAI,CAAC,KAAK,GAAI;QACnH,OAAO;IACT;IACA,IAAI,OAAO,WAAW,IAAI;IAC1B,IAAI,MAAM;IACV,IAAI,KAAK,WAAW,KAAK,wLAAA,CAAA,UAAS,EAAE;QAClC,MAAM,WAAW,KAAK;IACxB,OAAO,IAAI,KAAK,KAAK,KAAK,QAAQ,CAAC,KAAK,KAAK,CAAC,OAAO,EAAE;QACrD,IAAI,IAAI,KAAK,MAAM;QACnB,IAAI,IAAI;QACR,MAAO,IAAI,KAAK,OAAO,IAAI,IAAI,WAAW,KAAK,IAAI,MAAM,KAAM;YAC7D,IAAI,CAAC,EAAE,OAAO,EAAE;gBACd,MAAM,IAAI,0BAA0B,GAAG,AAAC,EAAE,OAAO,CAAE,IAAI;gBACvD;gBACA,IAAI,aAAa,wLAAA,CAAA,UAAS,EAAE;oBAC1B,OAAO,EAAE,OAAO;gBAClB,OAAO;oBACL,OAAO,gBAAgB,GAAG,AAAC,QAAQ,GAAG,CAAC,GAAI,QAAQ;gBACrD;YACF;YACA,IAA2B,EAAE,KAAK;QACpC;QACA,OAAO,EAAE,kCAAkC;;IAC7C;IACA,MAAO,SAAS,gBAAgB,KAAK,KAAK,KAAK,KAAM;QACnD,aAAa;QACb,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM;QAChC,aAAa;QACb,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE;YAClD,OAAO,EAAE,gBAAgB;;YACzB,IAAI,IAAI,2BAA2B,GAAG,AAAC,OAAQ,MAAM;YACrD,kCAAkC;YAClC,MAAO,MAAM,KAAM;gBACjB,MAAM,cAAc,0BAA0B,GAAG,AAAC,EAAE,OAAO,CAAE,IAAI;gBACjE,IAAI,gBAAgB,MAAM;oBACxB;gBACF;gBACA,IAAI,CAAC,EAAE,OAAO,EAAE;oBACd,IAAI,uBAAuB,wLAAA,CAAA,UAAS,EAAE;wBACpC,OAAO,YAAY,OAAO;oBAC5B,OAAO;wBACL,OAAO,gBAAgB,GAAG,AAAC,QAAQ,GAAG,CAAC,aAAc,QAAQ;oBAC/D;gBACF;gBACA,IAAI,EAAE,KAAK;YACb;QACF;QACA,OAAsC;IACxC;IACA,OAAO,MAAM,EAAE,8DAA8D;;AAC/E;AAQO,MAAM,oCAAoC,CAAC,cAAc;IAC9D,MAAM,kBAAkB,aAAa,OAAO,GAAG,GAAG,CAAC,CAAC,IAClD,CAAA,GAAA,gXAAA,CAAA,yBAAsB,AAAD,EACU,GAC7B,QACA,CAAA,GAAA,gXAAA,CAAA,kBAAe,AAAD,MAEhB,MAAM,CAAC,CAAC,IAAM,MAAM;IACtB,OAAO,0NAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;AAC5B;AAQO,MAAM,oCAAoC,CAAC,cAAc,SAC9D,OAAO,WAAW,CAAC,MAAM,CAAC,MAAM,kCAAkC,cAAc;AAW3E,MAAM,qBAAqB,CAAC,cAAc;IAC/C,MAAM,OAAO,CAAA,GAAA,gXAAA,CAAA,kBAAe,AAAD;IAC3B,MAAM,kBAAkB,aAAa,OAAO,GAAG,GAAG,CAAC,CAAC,IAClD,CAAA,GAAA,gXAAA,CAAA,yBAAsB,AAAD,EACU,GAC7B,QACA,OAEF,MAAM,CAAC,CAAC,IAAM,MAAM;IACtB,MAAM,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC,MAAM,0NAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAC/D,OAAO;QAAE;QAAK;QAAM,SAAS,KAAK,OAAO;IAAC;AAC5C;AAaO,SAAS,kBAAmB,GAAG,EAAE,cAAc,aAAa;IACjE,MAAM,OAAO,IAAI,wLAAA,CAAA,MAAK;IACtB,MAAM,OAAqC,KAAK,GAAG,CAAC,aAAa,wLAAA,CAAA,cAAa;IAC9E,IAAI,CAAC,KAAK,GAAG,EAAE;QACb,OAAO;IACT;IAEA,0BAA0B,KAAK;IAC/B,OAAO,KAAK,GAAG;AACjB;AAiBO,SAAS,0BAA2B,GAAG,EAAE,WAAW;IACzD,MAAM,OAAO,eAAe,IAAI,wLAAA,CAAA,cAAa;IAC7C,MAAM,OAAO,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG;QAAE,UAAU,CAAC,cAAgB,YAAY;IAAW;IACvF,CAAA,GAAA,gXAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,KAAK;QAAE,SAAS,IAAI;QAAO,SAAS,IAAI;IAAM;IAC1E,OAAO;AACT;AAcO,SAAS,sBAAuB,MAAM,EAAE,KAAK,EAAE,cAAc,aAAa;IAC/E,MAAM,MAAM,0NAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,QAAQ;IAClC,OAAO,kBAAkB,KAAK;AAChC;AAeO,SAAS,8BAA+B,MAAM,EAAE,KAAK,EAAE,WAAW;IACvE,MAAM,MAAM,0NAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,QAAQ;IAClC,OAAO,0BAA0B,KAAK;AACxC;AAWO,SAAS,kBAAmB,MAAM,EAAE,IAAI;IAC7C,MAAM,QAAQ,sBAAsB;IACpC,OAAO,0NAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,QAAQ;AAC/B;AAYO,SAAS,0BAA2B,MAAM,EAAE,WAAW;IAC5D,MAAM,QAAQ,8BAA8B;IAC5C,OAAO,0NAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,QAAQ;AAC/B;AAYO,SAAS,sBACd,IAAI,EACJ,cAAc,aAAa;IAE3B,OAAO,8BAA8B,KAAK,cAAc,CAAC;AAC3D;AAUO,SAAS,8BAA+B,WAAW;IACxD,MAAM,QAAQ,YAAY,OAAO;IAEjC;;GAEC,GACD,MAAM,YAAY,CAAA;QAChB;;;;;KAKC,GACD,IAAI;QAEJ,4DAA4D;QAC5D,IAAI,gBAAgB,wLAAA,CAAA,UAAS,EAAE;YAC7B,MAAM,QAAQ,KAAK,OAAO;YAC1B,WAAW,MAAM,GAAG,CAAC,mBAAmB,GAAG,CAAC;gBAC1C,MAAM,OAAO;oBACX,MAAM;oBACN,MAAM,EAAE,MAAM;gBAChB;gBACA,IAAI,EAAE,UAAU,EAAE;oBAChB,KAAK,KAAK,GAAG,OAAO,IAAI,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;wBAC1C,MAAM,QAAQ,EAAE,UAAU,CAAC,MAAM;wBACjC,MAAM,OAAO,CAAA,GAAA,gXAAA,CAAA,iBAAc,AAAD,EAAE;wBAC5B,MAAM,OAAO;4BACX;wBACF;wBACA,IAAI,OAAO,IAAI,CAAC,QAAQ;4BACtB,KAAK,KAAK,GAAG;wBACf;wBACA,OAAO;oBACT;gBACF;gBACA,OAAO;YACT;QACF,OAAO,IAAI,gBAAgB,wLAAA,CAAA,aAAY,EAAE;YACvC,WAAW;gBACT,MAAM,KAAK,QAAQ;YACrB;YAEA,MAAM,QAAQ,KAAK,aAAa;YAChC,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE;gBAC7B,SAAS,KAAK,GAAG;YACnB;YAEA,MAAM,WAAW,KAAK,OAAO;YAC7B,IAAI,SAAS,MAAM,EAAE;gBACnB,SAAS,OAAO,GAAG,SAAS,GAAG,CAAC,WAAW,IAAI;YACjD;QACF,OAAO;YACL,4CAA4C;YAC5C,CAAA,GAAA,mLAAA,CAAA,iBAAoB,AAAD;QACrB;QAEA,OAAO;IACT;IAEA,OAAO;QACL,MAAM;QACN,SAAS,MAAM,GAAG,CAAC;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/plugins/cursor-plugin.js"], "sourcesContent": ["import * as Y from 'yjs'\nimport { Decoration, DecorationSet } from \"prosemirror-view\"; // eslint-disable-line\nimport { Plugin } from \"prosemirror-state\"; // eslint-disable-line\nimport { Awareness } from \"y-protocols/awareness\"; // eslint-disable-line\nimport {\n  absolutePositionToRelativePosition,\n  relativePositionToAbsolutePosition,\n  setMeta\n} from '../lib.js'\nimport { yCursorPluginKey, ySyncPluginKey } from './keys.js'\n\nimport * as math from 'lib0/math'\n\n/**\n * Default awareness state filter\n *\n * @param {number} currentClientId current client id\n * @param {number} userClientId user client id\n * @param {any} _user user data\n * @return {boolean}\n */\nexport const defaultAwarenessStateFilter = (currentClientId, userClientId, _user) => currentClientId !== userClientId\n\n/**\n * Default generator for a cursor element\n *\n * @param {any} user user data\n * @return {HTMLElement}\n */\nexport const defaultCursorBuilder = (user) => {\n  const cursor = document.createElement('span')\n  cursor.classList.add('ProseMirror-yjs-cursor')\n  cursor.setAttribute('style', `border-color: ${user.color}`)\n  const userDiv = document.createElement('div')\n  userDiv.setAttribute('style', `background-color: ${user.color}`)\n  userDiv.insertBefore(document.createTextNode(user.name), null)\n  const nonbreakingSpace1 = document.createTextNode('\\u2060')\n  const nonbreakingSpace2 = document.createTextNode('\\u2060')\n  cursor.insertBefore(nonbreakingSpace1, null)\n  cursor.insertBefore(userDiv, null)\n  cursor.insertBefore(nonbreakingSpace2, null)\n  return cursor\n}\n\n/**\n * Default generator for the selection attributes\n *\n * @param {any} user user data\n * @return {import('prosemirror-view').DecorationAttrs}\n */\nexport const defaultSelectionBuilder = (user) => {\n  return {\n    style: `background-color: ${user.color}70`,\n    class: 'ProseMirror-yjs-selection'\n  }\n}\n\nconst rxValidColor = /^#[0-9a-fA-F]{6}$/\n\n/**\n * @param {any} state\n * @param {Awareness} awareness\n * @param {function(number, number, any):boolean} awarenessFilter\n * @param {(user: { name: string, color: string }, clientId: number) => Element} createCursor\n * @param {(user: { name: string, color: string }, clientId: number) => import('prosemirror-view').DecorationAttrs} createSelection\n * @return {any} DecorationSet\n */\nexport const createDecorations = (\n  state,\n  awareness,\n  awarenessFilter,\n  createCursor,\n  createSelection\n) => {\n  const ystate = ySyncPluginKey.getState(state)\n  const y = ystate.doc\n  const decorations = []\n  if (\n    ystate.snapshot != null || ystate.prevSnapshot != null ||\n    ystate.binding.mapping.size === 0\n  ) {\n    // do not render cursors while snapshot is active\n    return DecorationSet.create(state.doc, [])\n  }\n  awareness.getStates().forEach((aw, clientId) => {\n    if (!awarenessFilter(y.clientID, clientId, aw)) {\n      return\n    }\n\n    if (aw.cursor != null) {\n      const user = aw.user || {}\n      if (user.color == null) {\n        user.color = '#ffa500'\n      } else if (!rxValidColor.test(user.color)) {\n        // We only support 6-digit RGB colors in y-prosemirror\n        console.warn('A user uses an unsupported color format', user)\n      }\n      if (user.name == null) {\n        user.name = `User: ${clientId}`\n      }\n      let anchor = relativePositionToAbsolutePosition(\n        y,\n        ystate.type,\n        Y.createRelativePositionFromJSON(aw.cursor.anchor),\n        ystate.binding.mapping\n      )\n      let head = relativePositionToAbsolutePosition(\n        y,\n        ystate.type,\n        Y.createRelativePositionFromJSON(aw.cursor.head),\n        ystate.binding.mapping\n      )\n      if (anchor !== null && head !== null) {\n        const maxsize = math.max(state.doc.content.size - 1, 0)\n        anchor = math.min(anchor, maxsize)\n        head = math.min(head, maxsize)\n        decorations.push(\n          Decoration.widget(head, () => createCursor(user, clientId), {\n            key: clientId + '',\n            side: 10\n          })\n        )\n        const from = math.min(anchor, head)\n        const to = math.max(anchor, head)\n        decorations.push(\n          Decoration.inline(from, to, createSelection(user, clientId), {\n            inclusiveEnd: true,\n            inclusiveStart: false\n          })\n        )\n      }\n    }\n  })\n  return DecorationSet.create(state.doc, decorations)\n}\n\n/**\n * A prosemirror plugin that listens to awareness information on Yjs.\n * This requires that a `prosemirrorPlugin` is also bound to the prosemirror.\n *\n * @public\n * @param {Awareness} awareness\n * @param {object} opts\n * @param {function(any, any, any):boolean} [opts.awarenessStateFilter]\n * @param {(user: any, clientId: number) => HTMLElement} [opts.cursorBuilder]\n * @param {(user: any, clientId: number) => import('prosemirror-view').DecorationAttrs} [opts.selectionBuilder]\n * @param {function(any):any} [opts.getSelection]\n * @param {string} [cursorStateField] By default all editor bindings use the awareness 'cursor' field to propagate cursor information.\n * @return {any}\n */\nexport const yCursorPlugin = (\n  awareness,\n  {\n    awarenessStateFilter = defaultAwarenessStateFilter,\n    cursorBuilder = defaultCursorBuilder,\n    selectionBuilder = defaultSelectionBuilder,\n    getSelection = (state) => state.selection\n  } = {},\n  cursorStateField = 'cursor'\n) =>\n  new Plugin({\n    key: yCursorPluginKey,\n    state: {\n      init (_, state) {\n        return createDecorations(\n          state,\n          awareness,\n          awarenessStateFilter,\n          cursorBuilder,\n          selectionBuilder\n        )\n      },\n      apply (tr, prevState, _oldState, newState) {\n        const ystate = ySyncPluginKey.getState(newState)\n        const yCursorState = tr.getMeta(yCursorPluginKey)\n        if (\n          (ystate && ystate.isChangeOrigin) ||\n          (yCursorState && yCursorState.awarenessUpdated)\n        ) {\n          return createDecorations(\n            newState,\n            awareness,\n            awarenessStateFilter,\n            cursorBuilder,\n            selectionBuilder\n          )\n        }\n        return prevState.map(tr.mapping, tr.doc)\n      }\n    },\n    props: {\n      decorations: (state) => {\n        return yCursorPluginKey.getState(state)\n      }\n    },\n    view: (view) => {\n      const awarenessListener = () => {\n        // @ts-ignore\n        if (view.docView) {\n          setMeta(view, yCursorPluginKey, { awarenessUpdated: true })\n        }\n      }\n      const updateCursorInfo = () => {\n        const ystate = ySyncPluginKey.getState(view.state)\n        // @note We make implicit checks when checking for the cursor property\n        const current = awareness.getLocalState() || {}\n        if (view.hasFocus()) {\n          const selection = getSelection(view.state)\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const anchor = absolutePositionToRelativePosition(\n            selection.anchor,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          /**\n           * @type {Y.RelativePosition}\n           */\n          const head = absolutePositionToRelativePosition(\n            selection.head,\n            ystate.type,\n            ystate.binding.mapping\n          )\n          if (\n            current.cursor == null ||\n            !Y.compareRelativePositions(\n              Y.createRelativePositionFromJSON(current.cursor.anchor),\n              anchor\n            ) ||\n            !Y.compareRelativePositions(\n              Y.createRelativePositionFromJSON(current.cursor.head),\n              head\n            )\n          ) {\n            awareness.setLocalStateField(cursorStateField, {\n              anchor,\n              head\n            })\n          }\n        } else if (\n          current.cursor != null &&\n          relativePositionToAbsolutePosition(\n            ystate.doc,\n            ystate.type,\n            Y.createRelativePositionFromJSON(current.cursor.anchor),\n            ystate.binding.mapping\n          ) !== null\n        ) {\n          // delete cursor information if current cursor information is owned by this editor binding\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n      awareness.on('change', awarenessListener)\n      view.dom.addEventListener('focusin', updateCursorInfo)\n      view.dom.addEventListener('focusout', updateCursorInfo)\n      return {\n        update: updateCursorInfo,\n        destroy: () => {\n          view.dom.removeEventListener('focusin', updateCursorInfo)\n          view.dom.removeEventListener('focusout', updateCursorInfo)\n          awareness.off('change', awarenessListener)\n          awareness.setLocalStateField(cursorStateField, null)\n        }\n      }\n    }\n  })\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA,sXAA8D,sBAAsB;AACpF,wXAA4C,sBAAsB;AAClE,4XAAmD,sBAAsB;AACzE;AAKA;AAEA;;;;;;;;AAUO,MAAM,8BAA8B,CAAC,iBAAiB,cAAc,QAAU,oBAAoB;AAQlG,MAAM,uBAAuB,CAAC;IACnC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,SAAS,CAAC,GAAG,CAAC;IACrB,OAAO,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,KAAK,KAAK,EAAE;IAC1D,MAAM,UAAU,SAAS,aAAa,CAAC;IACvC,QAAQ,YAAY,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;IAC/D,QAAQ,YAAY,CAAC,SAAS,cAAc,CAAC,KAAK,IAAI,GAAG;IACzD,MAAM,oBAAoB,SAAS,cAAc,CAAC;IAClD,MAAM,oBAAoB,SAAS,cAAc,CAAC;IAClD,OAAO,YAAY,CAAC,mBAAmB;IACvC,OAAO,YAAY,CAAC,SAAS;IAC7B,OAAO,YAAY,CAAC,mBAAmB;IACvC,OAAO;AACT;AAQO,MAAM,0BAA0B,CAAC;IACtC,OAAO;QACL,OAAO,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;QAC1C,OAAO;IACT;AACF;AAEA,MAAM,eAAe;AAUd,MAAM,oBAAoB,CAC/B,OACA,WACA,iBACA,cACA;IAEA,MAAM,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC;IACvC,MAAM,IAAI,OAAO,GAAG;IACpB,MAAM,cAAc,EAAE;IACtB,IACE,OAAO,QAAQ,IAAI,QAAQ,OAAO,YAAY,IAAI,QAClD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,GAChC;QACA,iDAAiD;QACjD,OAAO,wNAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE;IAC3C;IACA,UAAU,SAAS,GAAG,OAAO,CAAC,CAAC,IAAI;QACjC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,UAAU,KAAK;YAC9C;QACF;QAEA,IAAI,GAAG,MAAM,IAAI,MAAM;YACrB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC;YACzB,IAAI,KAAK,KAAK,IAAI,MAAM;gBACtB,KAAK,KAAK,GAAG;YACf,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,KAAK,GAAG;gBACzC,sDAAsD;gBACtD,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;YACA,IAAI,KAAK,IAAI,IAAI,MAAM;gBACrB,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU;YACjC;YACA,IAAI,SAAS,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC5C,GACA,OAAO,IAAI,EACX,CAAA,GAAA,wLAAA,CAAA,iCAAgC,AAAD,EAAE,GAAG,MAAM,CAAC,MAAM,GACjD,OAAO,OAAO,CAAC,OAAO;YAExB,IAAI,OAAO,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC1C,GACA,OAAO,IAAI,EACX,CAAA,GAAA,wLAAA,CAAA,iCAAgC,AAAD,EAAE,GAAG,MAAM,CAAC,IAAI,GAC/C,OAAO,OAAO,CAAC,OAAO;YAExB,IAAI,WAAW,QAAQ,SAAS,MAAM;gBACpC,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG;gBACrD,SAAS,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,QAAQ;gBAC1B,OAAO,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,MAAM;gBACtB,YAAY,IAAI,CACd,wNAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,IAAM,aAAa,MAAM,WAAW;oBAC1D,KAAK,WAAW;oBAChB,MAAM;gBACR;gBAEF,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,QAAQ;gBAC9B,MAAM,KAAK,CAAA,GAAA,kLAAA,CAAA,MAAQ,AAAD,EAAE,QAAQ;gBAC5B,YAAY,IAAI,CACd,wNAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,IAAI,gBAAgB,MAAM,WAAW;oBAC3D,cAAc;oBACd,gBAAgB;gBAClB;YAEJ;QACF;IACF;IACA,OAAO,wNAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;AACzC;AAgBO,MAAM,gBAAgB,CAC3B,WACA,EACE,uBAAuB,2BAA2B,EAClD,gBAAgB,oBAAoB,EACpC,mBAAmB,uBAAuB,EAC1C,eAAe,CAAC,QAAU,MAAM,SAAS,EAC1C,GAAG,CAAC,CAAC,EACN,mBAAmB,QAAQ,GAE3B,IAAI,yNAAA,CAAA,SAAM,CAAC;QACT,KAAK,sWAAA,CAAA,mBAAgB;QACrB,OAAO;YACL,MAAM,CAAC,EAAE,KAAK;gBACZ,OAAO,kBACL,OACA,WACA,sBACA,eACA;YAEJ;YACA,OAAO,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;gBACvC,MAAM,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC;gBACvC,MAAM,eAAe,GAAG,OAAO,CAAC,sWAAA,CAAA,mBAAgB;gBAChD,IACE,AAAC,UAAU,OAAO,cAAc,IAC/B,gBAAgB,aAAa,gBAAgB,EAC9C;oBACA,OAAO,kBACL,UACA,WACA,sBACA,eACA;gBAEJ;gBACA,OAAO,UAAU,GAAG,CAAC,GAAG,OAAO,EAAE,GAAG,GAAG;YACzC;QACF;QACA,OAAO;YACL,aAAa,CAAC;gBACZ,OAAO,sWAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC;YACnC;QACF;QACA,MAAM,CAAC;YACL,MAAM,oBAAoB;gBACxB,aAAa;gBACb,IAAI,KAAK,OAAO,EAAE;oBAChB,CAAA,GAAA,0VAAA,CAAA,UAAO,AAAD,EAAE,MAAM,sWAAA,CAAA,mBAAgB,EAAE;wBAAE,kBAAkB;oBAAK;gBAC3D;YACF;YACA,MAAM,mBAAmB;gBACvB,MAAM,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK;gBACjD,sEAAsE;gBACtE,MAAM,UAAU,UAAU,aAAa,MAAM,CAAC;gBAC9C,IAAI,KAAK,QAAQ,IAAI;oBACnB,MAAM,YAAY,aAAa,KAAK,KAAK;oBACzC;;WAEC,GACD,MAAM,SAAS,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC9C,UAAU,MAAM,EAChB,OAAO,IAAI,EACX,OAAO,OAAO,CAAC,OAAO;oBAExB;;WAEC,GACD,MAAM,OAAO,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC5C,UAAU,IAAI,EACd,OAAO,IAAI,EACX,OAAO,OAAO,CAAC,OAAO;oBAExB,IACE,QAAQ,MAAM,IAAI,QAClB,CAAC,CAAA,GAAA,wLAAA,CAAA,2BAA0B,AAAD,EACxB,CAAA,GAAA,wLAAA,CAAA,iCAAgC,AAAD,EAAE,QAAQ,MAAM,CAAC,MAAM,GACtD,WAEF,CAAC,CAAA,GAAA,wLAAA,CAAA,2BAA0B,AAAD,EACxB,CAAA,GAAA,wLAAA,CAAA,iCAAgC,AAAD,EAAE,QAAQ,MAAM,CAAC,IAAI,GACpD,OAEF;wBACA,UAAU,kBAAkB,CAAC,kBAAkB;4BAC7C;4BACA;wBACF;oBACF;gBACF,OAAO,IACL,QAAQ,MAAM,IAAI,QAClB,CAAA,GAAA,0VAAA,CAAA,qCAAkC,AAAD,EAC/B,OAAO,GAAG,EACV,OAAO,IAAI,EACX,CAAA,GAAA,wLAAA,CAAA,iCAAgC,AAAD,EAAE,QAAQ,MAAM,CAAC,MAAM,GACtD,OAAO,OAAO,CAAC,OAAO,MAClB,MACN;oBACA,0FAA0F;oBAC1F,UAAU,kBAAkB,CAAC,kBAAkB;gBACjD;YACF;YACA,UAAU,EAAE,CAAC,UAAU;YACvB,KAAK,GAAG,CAAC,gBAAgB,CAAC,WAAW;YACrC,KAAK,GAAG,CAAC,gBAAgB,CAAC,YAAY;YACtC,OAAO;gBACL,QAAQ;gBACR,SAAS;oBACP,KAAK,GAAG,CAAC,mBAAmB,CAAC,WAAW;oBACxC,KAAK,GAAG,CAAC,mBAAmB,CAAC,YAAY;oBACzC,UAAU,GAAG,CAAC,UAAU;oBACxB,UAAU,kBAAkB,CAAC,kBAAkB;gBACjD;YACF;QACF;IACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/y-prosemirror%401.3.7_prosemirror-model%401.25.2_prosemirror-state%401.4.3_prosemirror-view%401.40.0__ypeixlljqfn6qomhmepeqhka34/node_modules/y-prosemirror/src/plugins/undo-plugin.js"], "sourcesContent": ["import { Plugin } from 'prosemirror-state'\n\nimport { getRelativeSelection } from './sync-plugin.js'\nimport { UndoManager, Item, ContentType, XmlElement, Text } from 'yjs'\nimport { yUndoPluginKey, ySyncPluginKey } from './keys.js'\n\n/**\n * @typedef {Object} UndoPluginState\n * @property {import('yjs').UndoManager} undoManager\n * @property {ReturnType<typeof getRelativeSelection> | null} prevSel\n * @property {boolean} hasUndoOps\n * @property {boolean} hasRedoOps\n */\n\n/**\n * Undo the last user action\n *\n * @param {import('prosemirror-state').EditorState} state\n * @return {boolean} whether a change was undone\n */\nexport const undo = state => yUndoPluginKey.getState(state)?.undoManager?.undo() != null\n\n/**\n * Redo the last user action\n *\n * @param {import('prosemirror-state').EditorState} state\n * @return {boolean} whether a change was undone\n */\nexport const redo = state => yUndoPluginKey.getState(state)?.undoManager?.redo() != null\n\n/**\n * Undo the last user action if there are undo operations available\n * @type {import('prosemirror-state').Command}\n */\nexport const undoCommand = (state, dispatch) => dispatch == null ? yUndoPluginKey.getState(state)?.undoManager?.canUndo() : undo(state)\n\n/**\n * Redo the last user action if there are redo operations available\n * @type {import('prosemirror-state').Command}\n */\nexport const redoCommand = (state, dispatch) => dispatch == null ? yUndoPluginKey.getState(state)?.undoManager?.canRedo() : redo(state)\n\nexport const defaultProtectedNodes = new Set(['paragraph'])\n\n/**\n * @param {import('yjs').Item} item\n * @param {Set<string>} protectedNodes\n * @returns {boolean}\n */\nexport const defaultDeleteFilter = (item, protectedNodes) => !(item instanceof Item) ||\n  !(item.content instanceof ContentType) ||\n  !(item.content.type instanceof Text ||\n  (item.content.type instanceof XmlElement && protectedNodes.has(item.content.type.nodeName))) ||\n  item.content.type._length === 0\n\n/**\n * @param {object} [options]\n * @param {Set<string>} [options.protectedNodes]\n * @param {any[]} [options.trackedOrigins]\n * @param {import('yjs').UndoManager | null} [options.undoManager]\n */\nexport const yUndoPlugin = ({ protectedNodes = defaultProtectedNodes, trackedOrigins = [], undoManager = null } = {}) => new Plugin({\n  key: yUndoPluginKey,\n  state: {\n    init: (initargs, state) => {\n      // TODO: check if plugin order matches and fix\n      const ystate = ySyncPluginKey.getState(state)\n      const _undoManager = undoManager || new UndoManager(ystate.type, {\n        trackedOrigins: new Set([ySyncPluginKey].concat(trackedOrigins)),\n        deleteFilter: (item) => defaultDeleteFilter(item, protectedNodes),\n        captureTransaction: tr => tr.meta.get('addToHistory') !== false\n      })\n      return {\n        undoManager: _undoManager,\n        prevSel: null,\n        hasUndoOps: _undoManager.undoStack.length > 0,\n        hasRedoOps: _undoManager.redoStack.length > 0\n      }\n    },\n    apply: (tr, val, oldState, state) => {\n      const binding = ySyncPluginKey.getState(state).binding\n      const undoManager = val.undoManager\n      const hasUndoOps = undoManager.undoStack.length > 0\n      const hasRedoOps = undoManager.redoStack.length > 0\n      if (binding) {\n        return {\n          undoManager,\n          prevSel: getRelativeSelection(binding, oldState),\n          hasUndoOps,\n          hasRedoOps\n        }\n      } else {\n        if (hasUndoOps !== val.hasUndoOps || hasRedoOps !== val.hasRedoOps) {\n          return Object.assign({}, val, {\n            hasUndoOps: undoManager.undoStack.length > 0,\n            hasRedoOps: undoManager.redoStack.length > 0\n          })\n        } else { // nothing changed\n          return val\n        }\n      }\n    }\n  },\n  view: view => {\n    const ystate = ySyncPluginKey.getState(view.state)\n    const undoManager = yUndoPluginKey.getState(view.state).undoManager\n    undoManager.on('stack-item-added', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        stackItem.meta.set(binding, yUndoPluginKey.getState(view.state).prevSel)\n      }\n    })\n    undoManager.on('stack-item-popped', ({ stackItem }) => {\n      const binding = ystate.binding\n      if (binding) {\n        binding.beforeTransactionSelection = stackItem.meta.get(binding) || binding.beforeTransactionSelection\n      }\n    })\n    return {\n      destroy: () => {\n        undoManager.destroy()\n      }\n    }\n  }\n})\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;AACA;;;;;AAgBO,MAAM,OAAO,CAAA,QAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,QAAQ,aAAa,UAAU;AAQ7E,MAAM,OAAO,CAAA,QAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,QAAQ,aAAa,UAAU;AAM7E,MAAM,cAAc,CAAC,OAAO,WAAa,YAAY,OAAO,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,QAAQ,aAAa,YAAY,KAAK;AAM1H,MAAM,cAAc,CAAC,OAAO,WAAa,YAAY,OAAO,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,QAAQ,aAAa,YAAY,KAAK;AAE1H,MAAM,wBAAwB,IAAI,IAAI;IAAC;CAAY;AAOnD,MAAM,sBAAsB,CAAC,MAAM,iBAAmB,CAAC,CAAC,gBAAgB,wLAAA,CAAA,OAAI,KACjF,CAAC,CAAC,KAAK,OAAO,YAAY,wLAAA,CAAA,cAAW,KACrC,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,YAAY,wLAAA,CAAA,OAAI,IAClC,KAAK,OAAO,CAAC,IAAI,YAAY,wLAAA,CAAA,aAAU,IAAI,eAAe,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAE,KAC3F,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK;AAQzB,MAAM,cAAc,CAAC,EAAE,iBAAiB,qBAAqB,EAAE,iBAAiB,EAAE,EAAE,cAAc,IAAI,EAAE,GAAG,CAAC,CAAC,GAAK,IAAI,yNAAA,CAAA,SAAM,CAAC;QAClI,KAAK,sWAAA,CAAA,iBAAc;QACnB,OAAO;YACL,MAAM,CAAC,UAAU;gBACf,8CAA8C;gBAC9C,MAAM,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC;gBACvC,MAAM,eAAe,eAAe,IAAI,wLAAA,CAAA,cAAW,CAAC,OAAO,IAAI,EAAE;oBAC/D,gBAAgB,IAAI,IAAI;wBAAC,sWAAA,CAAA,iBAAc;qBAAC,CAAC,MAAM,CAAC;oBAChD,cAAc,CAAC,OAAS,oBAAoB,MAAM;oBAClD,oBAAoB,CAAA,KAAM,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB;gBAC5D;gBACA,OAAO;oBACL,aAAa;oBACb,SAAS;oBACT,YAAY,aAAa,SAAS,CAAC,MAAM,GAAG;oBAC5C,YAAY,aAAa,SAAS,CAAC,MAAM,GAAG;gBAC9C;YACF;YACA,OAAO,CAAC,IAAI,KAAK,UAAU;gBACzB,MAAM,UAAU,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,OAAO,OAAO;gBACtD,MAAM,cAAc,IAAI,WAAW;gBACnC,MAAM,aAAa,YAAY,SAAS,CAAC,MAAM,GAAG;gBAClD,MAAM,aAAa,YAAY,SAAS,CAAC,MAAM,GAAG;gBAClD,IAAI,SAAS;oBACX,OAAO;wBACL;wBACA,SAAS,CAAA,GAAA,gXAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS;wBACvC;wBACA;oBACF;gBACF,OAAO;oBACL,IAAI,eAAe,IAAI,UAAU,IAAI,eAAe,IAAI,UAAU,EAAE;wBAClE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;4BAC5B,YAAY,YAAY,SAAS,CAAC,MAAM,GAAG;4BAC3C,YAAY,YAAY,SAAS,CAAC,MAAM,GAAG;wBAC7C;oBACF,OAAO;wBACL,OAAO;oBACT;gBACF;YACF;QACF;QACA,MAAM,CAAA;YACJ,MAAM,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK;YACjD,MAAM,cAAc,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,WAAW;YACnE,YAAY,EAAE,CAAC,oBAAoB,CAAC,EAAE,SAAS,EAAE;gBAC/C,MAAM,UAAU,OAAO,OAAO;gBAC9B,IAAI,SAAS;oBACX,UAAU,IAAI,CAAC,GAAG,CAAC,SAAS,sWAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;gBACzE;YACF;YACA,YAAY,EAAE,CAAC,qBAAqB,CAAC,EAAE,SAAS,EAAE;gBAChD,MAAM,UAAU,OAAO,OAAO;gBAC9B,IAAI,SAAS;oBACX,QAAQ,0BAA0B,GAAG,UAAU,IAAI,CAAC,GAAG,CAAC,YAAY,QAAQ,0BAA0B;gBACxG;YACF;YACA,OAAO;gBACL,SAAS;oBACP,YAAY,OAAO;gBACrB;YACF;QACF;IACF", "ignoreList": [0], "debugId": null}}]}