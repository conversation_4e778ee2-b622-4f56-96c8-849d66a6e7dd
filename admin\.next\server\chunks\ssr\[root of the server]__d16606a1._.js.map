{"version": 3, "sources": [], "sections": [{"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/unified%4011.0.5/node_modules/unified/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {unified} from './lib/index.js'\n"], "names": [], "mappings": "AAAA,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/bail%402.0.2/node_modules/bail/index.js"], "sourcesContent": ["/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACM,SAAS,KAAK,KAAK;IACxB,IAAI,OAAO;QACT,MAAM;IACR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/extend%403.0.2/node_modules/extend/index.js"], "sourcesContent": ["'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,OAAO,SAAS,CAAC,cAAc;AAC5C,IAAI,QAAQ,OAAO,SAAS,CAAC,QAAQ;AACrC,IAAI,iBAAiB,OAAO,cAAc;AAC1C,IAAI,OAAO,OAAO,wBAAwB;AAE1C,IAAI,UAAU,SAAS,QAAQ,GAAG;IACjC,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY;QACxC,OAAO,MAAM,OAAO,CAAC;IACtB;IAEA,OAAO,MAAM,IAAI,CAAC,SAAS;AAC5B;AAEA,IAAI,gBAAgB,SAAS,cAAc,GAAG;IAC7C,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,SAAS,mBAAmB;QAClD,OAAO;IACR;IAEA,IAAI,oBAAoB,OAAO,IAAI,CAAC,KAAK;IACzC,IAAI,mBAAmB,IAAI,WAAW,IAAI,IAAI,WAAW,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,IAAI,WAAW,CAAC,SAAS,EAAE;IAC9G,8CAA8C;IAC9C,IAAI,IAAI,WAAW,IAAI,CAAC,qBAAqB,CAAC,kBAAkB;QAC/D,OAAO;IACR;IAEA,yDAAyD;IACzD,mDAAmD;IACnD,IAAI;IACJ,IAAK,OAAO,IAAK,CAAO;IAExB,OAAO,OAAO,QAAQ,eAAe,OAAO,IAAI,CAAC,KAAK;AACvD;AAEA,gHAAgH;AAChH,IAAI,cAAc,SAAS,YAAY,MAAM,EAAE,OAAO;IACrD,IAAI,kBAAkB,QAAQ,IAAI,KAAK,aAAa;QACnD,eAAe,QAAQ,QAAQ,IAAI,EAAE;YACpC,YAAY;YACZ,cAAc;YACd,OAAO,QAAQ,QAAQ;YACvB,UAAU;QACX;IACD,OAAO;QACN,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG,QAAQ,QAAQ;IACxC;AACD;AAEA,8EAA8E;AAC9E,IAAI,cAAc,SAAS,YAAY,GAAG,EAAE,IAAI;IAC/C,IAAI,SAAS,aAAa;QACzB,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,OAAO;YAC5B,OAAO,KAAK;QACb,OAAO,IAAI,MAAM;YAChB,oEAAoE;YACpE,yEAAyE;YACzE,OAAO,KAAK,KAAK,MAAM,KAAK;QAC7B;IACD;IAEA,OAAO,GAAG,CAAC,KAAK;AACjB;AAEA,OAAO,OAAO,GAAG,SAAS;IACzB,IAAI,SAAS,MAAM,KAAK,MAAM,aAAa;IAC3C,IAAI,SAAS,SAAS,CAAC,EAAE;IACzB,IAAI,IAAI;IACR,IAAI,SAAS,UAAU,MAAM;IAC7B,IAAI,OAAO;IAEX,+BAA+B;IAC/B,IAAI,OAAO,WAAW,WAAW;QAChC,OAAO;QACP,SAAS,SAAS,CAAC,EAAE,IAAI,CAAC;QAC1B,kCAAkC;QAClC,IAAI;IACL;IACA,IAAI,UAAU,QAAS,OAAO,WAAW,YAAY,OAAO,WAAW,YAAa;QACnF,SAAS,CAAC;IACX;IAEA,MAAO,IAAI,QAAQ,EAAE,EAAG;QACvB,UAAU,SAAS,CAAC,EAAE;QACtB,2CAA2C;QAC3C,IAAI,WAAW,MAAM;YACpB,yBAAyB;YACzB,IAAK,QAAQ,QAAS;gBACrB,MAAM,YAAY,QAAQ;gBAC1B,OAAO,YAAY,SAAS;gBAE5B,4BAA4B;gBAC5B,IAAI,WAAW,MAAM;oBACpB,mDAAmD;oBACnD,IAAI,QAAQ,QAAQ,CAAC,cAAc,SAAS,CAAC,cAAc,QAAQ,KAAK,CAAC,GAAG;wBAC3E,IAAI,aAAa;4BAChB,cAAc;4BACd,QAAQ,OAAO,QAAQ,OAAO,MAAM,EAAE;wBACvC,OAAO;4BACN,QAAQ,OAAO,cAAc,OAAO,MAAM,CAAC;wBAC5C;wBAEA,0CAA0C;wBAC1C,YAAY,QAAQ;4BAAE,MAAM;4BAAM,UAAU,OAAO,MAAM,OAAO;wBAAM;oBAEvE,kCAAkC;oBAClC,OAAO,IAAI,OAAO,SAAS,aAAa;wBACvC,YAAY,QAAQ;4BAAE,MAAM;4BAAM,UAAU;wBAAK;oBAClD;gBACD;YACD;QACD;IACD;IAEA,6BAA6B;IAC7B,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/dequal%402.0.3/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/devlop%401.1.0/node_modules/devlop/lib/development.js"], "sourcesContent": ["import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,cAAc,IAAI;AAExB,MAAM,uBAAuB;IAC3B,OAA6B,YAAY;IACzC,OAA6B,gBAAgB;IAE7C;;;;;;;;;;;;;;;GAeC,GACD,sCAAsC;IACtC,YAAY,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAE;QAC1D,KAAK,CAAC;QAEN,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD;QAEA;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;KAEC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAEA,MAAM,yBAAyB;IAC7B,OAA6B,qBAAqB;IAElD;;;;;;;;;GASC,GACD,YAAY,OAAO,EAAE,IAAI,CAAE;QACzB,KAAK,CAAC;QAEN;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAwBO,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI;IACzC,IAAI,SAAS;IAEb,6EAA6E;IAC7E,OAAO,cAAc,CAAC,YAAY;IAElC,8CAA8C;IAC9C,OAAO;;IAEP;;;;GAIC,GACD,SAAS,WAAW,GAAG,IAAI;QACzB,IAAI,CAAC,QAAQ;YACX,SAAS;YAET,IAAI,OAAO,SAAS,YAAY,YAAY,GAAG,CAAC,OAAO;YACrD,SAAS;YACX,OAAO;gBACL,QAAQ,KAAK,CAAC,IAAI,iBAAiB,SAAS,QAAQ;gBAEpD,IAAI,OAAO,SAAS,UAAU,YAAY,GAAG,CAAC;YAChD;QACF;QAEA,OAAO,aACH,QAAQ,SAAS,CAAC,IAAI,MAAM,cAC5B,QAAQ,KAAK,CAAC,IAAI,IAAI,EAAE;IAC9B;AACF;AAqBO,SAAS,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC7C,OACE,CAAA,GAAA,2LAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WACf,QACA,UACA,SACA,sCACA;AAEJ;AAiBO,SAAS,GAAG,KAAK,EAAE,OAAO;IAC/B,OACE,QAAQ,QACR,OACA,MACA,MACA,+BACA;AAEJ;AAeO,SAAS,YAAY,OAAO;IACjC,OAAO,OAAO,OAAO,MAAM,MAAM,eAAe;AAClD;AAEA;;;;;;;;;;;;;;;CAeC,GACD,sCAAsC;AACtC,SAAS,OAAO,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW;IAC3E,IAAI,CAAC,MAAM;QACT,MAAM,uBAAuB,QACzB,cACA,IAAI,eACF,eAAe,gBACf,QACA,UACA,UACA,CAAC;IAET;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/is-plain-obj%404.1.0/node_modules/is-plain-obj/index.js"], "sourcesContent": ["export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,cAAc,KAAK;IAC1C,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;QAChD,OAAO;IACR;IAEA,MAAM,YAAY,OAAO,cAAc,CAAC;IACxC,OAAO,CAAC,cAAc,QAAQ,cAAc,OAAO,SAAS,IAAI,OAAO,cAAc,CAAC,eAAe,IAAI,KAAK,CAAC,CAAC,OAAO,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,QAAQ,IAAI,KAAK;AAC3K", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/trough%402.2.0/node_modules/trough/lib/index.js"], "sourcesContent": ["// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,uEAAuE;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GAED;;;;;CAKC;;;;AACM,SAAS;IACd,8BAA8B,GAC9B,MAAM,MAAM,EAAE;IACd,qBAAqB,GACrB,MAAM,WAAW;QAAC;QAAK;IAAG;IAE1B,OAAO;;IAEP,gBAAgB,GAChB,SAAS,IAAI,GAAG,MAAM;QACpB,IAAI,kBAAkB,CAAC;QACvB,qBAAqB,GACrB,MAAM,WAAW,OAAO,GAAG;QAE3B,IAAI,OAAO,aAAa,YAAY;YAClC,MAAM,IAAI,UAAU,6CAA6C;QACnE;QAEA,KAAK,SAAS;QAEd;;;;;KAKC,GACD,SAAS,KAAK,KAAK,EAAE,GAAG,MAAM;YAC5B,MAAM,KAAK,GAAG,CAAC,EAAE,gBAAgB;YACjC,IAAI,QAAQ,CAAC;YAEb,IAAI,OAAO;gBACT,SAAS;gBACT;YACF;YAEA,sCAAsC;YACtC,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;gBAC9B,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,KAAK,WAAW;oBACzD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;gBAC/B;YACF;YAEA,qDAAqD;YACrD,SAAS;YAET,gBAAgB;YAChB,IAAI,IAAI;gBACN,KAAK,IAAI,SAAS;YACpB,OAAO;gBACL,SAAS,SAAS;YACpB;QACF;IACF;IAEA,gBAAgB,GAChB,SAAS,IAAI,UAAU;QACrB,IAAI,OAAO,eAAe,YAAY;YACpC,MAAM,IAAI,UACR,iDAAiD;QAErD;QAEA,IAAI,IAAI,CAAC;QACT,OAAO;IACT;AACF;AAkCO,SAAS,KAAK,UAAU,EAAE,QAAQ;IACvC,oBAAoB,GACpB,IAAI;IAEJ,OAAO;;IAEP;;;;;GAKC,GACD,SAAS,QAAQ,GAAG,UAAU;QAC5B,MAAM,oBAAoB,WAAW,MAAM,GAAG,WAAW,MAAM;QAC/D,gBAAgB,GAChB,IAAI;QAEJ,IAAI,mBAAmB;YACrB,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI;YACF,SAAS,WAAW,KAAK,CAAC,IAAI,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,MAAM,YAAkC;YAExC,kCAAkC;YAClC,yEAAyE;YACzE,kBAAkB;YAClB,2DAA2D;YAC3D,IAAI,qBAAqB,QAAQ;gBAC/B,MAAM;YACR;YAEA,OAAO,KAAK;QACd;QAEA,IAAI,CAAC,mBAAmB;YACtB,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;gBAC9D,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO,IAAI,kBAAkB,OAAO;gBAClC,KAAK;YACP,OAAO;gBACL,KAAK;YACP;QACF;IACF;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK,EAAE,GAAG,MAAM;QAC5B,IAAI,CAAC,QAAQ;YACX,SAAS;YACT,SAAS,UAAU;QACrB;IACF;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK;QACjB,KAAK,MAAM;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/unist-util-stringify-position%404.0.0/node_modules/unist-util-stringify-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;;CAcC;;;AACM,SAAS,kBAAkB,KAAK;IACrC,WAAW;IACX,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO;IACT;IAEA,QAAQ;IACR,IAAI,cAAc,SAAS,UAAU,OAAO;QAC1C,OAAO,SAAS,MAAM,QAAQ;IAChC;IAEA,YAAY;IACZ,IAAI,WAAW,SAAS,SAAS,OAAO;QACtC,OAAO,SAAS;IAClB;IAEA,SAAS;IACT,IAAI,UAAU,SAAS,YAAY,OAAO;QACxC,OAAO,MAAM;IACf;IAEA,IAAI;IACJ,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,SAAS,MAAM,IAAI,IAAI,MAAM,MAAM,SAAS,MAAM,MAAM;AACvE;AAEA;;;CAGC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/vfile-message%404.0.2/node_modules/vfile-message/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super()\n\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace\n      optionsOrParentOrPlace = undefined\n    }\n\n    /** @type {string} */\n    let reason = ''\n    /** @type {Options} */\n    let options = {}\n    let legacyCause = false\n\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if (\n        'line' in optionsOrParentOrPlace &&\n        'column' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Position.\n      else if (\n        'start' in optionsOrParentOrPlace &&\n        'end' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        }\n      }\n      // Options.\n      else {\n        options = {...optionsOrParentOrPlace}\n      }\n    }\n\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true\n      reason = causeOrReason.message\n      options.cause = causeOrReason\n    }\n\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        options.ruleId = origin\n      } else {\n        options.source = origin.slice(0, index)\n        options.ruleId = origin.slice(index + 1)\n      }\n    }\n\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1]\n\n      if (parent) {\n        options.place = parent.position\n      }\n    }\n\n    const start =\n      options.place && 'start' in options.place\n        ? options.place.start\n        : options.place\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1'\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack =\n      legacyCause && options.cause && typeof options.cause.stack === 'string'\n        ? options.cause.stack\n        : ''\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.column = undefined\nVFileMessage.prototype.line = undefined\nVFileMessage.prototype.ancestors = undefined\nVFileMessage.prototype.cause = undefined\nVFileMessage.prototype.fatal = undefined\nVFileMessage.prototype.place = undefined\nVFileMessage.prototype.ruleId = undefined\nVFileMessage.prototype.source = undefined\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;;;CAeC;;;AAED;;AAKO,MAAM,qBAAqB;IAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqDC,GACD,sCAAsC;IACtC,YAAY,aAAa,EAAE,sBAAsB,EAAE,MAAM,CAAE;QACzD,KAAK;QAEL,IAAI,OAAO,2BAA2B,UAAU;YAC9C,SAAS;YACT,yBAAyB;QAC3B;QAEA,mBAAmB,GACnB,IAAI,SAAS;QACb,oBAAoB,GACpB,IAAI,UAAU,CAAC;QACf,IAAI,cAAc;QAElB,IAAI,wBAAwB;YAC1B,SAAS;YACT,IACE,UAAU,0BACV,YAAY,wBACZ;gBACA,UAAU;oBAAC,OAAO;gBAAsB;YAC1C,OAEK,IACH,WAAW,0BACX,SAAS,wBACT;gBACA,UAAU;oBAAC,OAAO;gBAAsB;YAC1C,OAEK,IAAI,UAAU,wBAAwB;gBACzC,UAAU;oBACR,WAAW;wBAAC;qBAAuB;oBACnC,OAAO,uBAAuB,QAAQ;gBACxC;YACF,OAEK;gBACH,UAAU;oBAAC,GAAG,sBAAsB;gBAAA;YACtC;QACF;QAEA,IAAI,OAAO,kBAAkB,UAAU;YACrC,SAAS;QACX,OAEK,IAAI,CAAC,QAAQ,KAAK,IAAI,eAAe;YACxC,cAAc;YACd,SAAS,cAAc,OAAO;YAC9B,QAAQ,KAAK,GAAG;QAClB;QAEA,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,IAAI,OAAO,WAAW,UAAU;YACpE,MAAM,QAAQ,OAAO,OAAO,CAAC;YAE7B,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ,MAAM,GAAG;YACnB,OAAO;gBACL,QAAQ,MAAM,GAAG,OAAO,KAAK,CAAC,GAAG;gBACjC,QAAQ,MAAM,GAAG,OAAO,KAAK,CAAC,QAAQ;YACxC;QACF;QAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,EAAE;YAC5D,MAAM,SAAS,QAAQ,SAAS,CAAC,QAAQ,SAAS,CAAC,MAAM,GAAG,EAAE;YAE9D,IAAI,QAAQ;gBACV,QAAQ,KAAK,GAAG,OAAO,QAAQ;YACjC;QACF;QAEA,MAAM,QACJ,QAAQ,KAAK,IAAI,WAAW,QAAQ,KAAK,GACrC,QAAQ,KAAK,CAAC,KAAK,GACnB,QAAQ,KAAK;QAEnB,wCAAwC,GACxC;;;;KAIC,GACD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,IAAI;QAEtC;;;;KAIC,GACD,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,IAAI;QAE9B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,MAAM,GAAG;QAErC;;;;;;;;KAQC,GACD,IAAI,CAAC,KAAK,GAAG;QAEb;;;;KAIC,GACD,IAAI,CAAC,IAAI;QAET,sBAAsB;QACtB;;;;KAIC,GACD,IAAI,CAAC,OAAO,GAAG;QAEf;;;;KAIC,GACD,IAAI,CAAC,IAAI,GAAG,QAAQ,MAAM,IAAI,GAAG;QAEjC,sBAAsB;QACtB;;;;;KAKC,GACD,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,yPAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,KAAK,KAAK;QAEhD;;;;KAIC,GACD,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,IAAI;QAE9B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;QAE1B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,IAAI;QAEhC;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,IAAI;QAEhC,sBAAsB;QACtB;;;;;;;KAOC,GACD,IAAI,CAAC,KAAK,GACR,eAAe,QAAQ,KAAK,IAAI,OAAO,QAAQ,KAAK,CAAC,KAAK,KAAK,WAC3D,QAAQ,KAAK,CAAC,KAAK,GACnB;QAEN,yCAAyC;QACzC,gBAAgB;QAChB,+DAA+D;QAE/D;;;;;KAKC,GACD,IAAI,CAAC,MAAM;QAEX;;;;KAIC,GACD,IAAI,CAAC,QAAQ;QAEb;;;;KAIC,GACD,IAAI,CAAC,IAAI;QAET;;;;;;;KAOC,GACD,IAAI,CAAC,GAAG;IACR,uCAAuC,GACzC;AACF;AAEA,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,OAAO,GAAG;AACjC,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,SAAS,GAAG;AACnC,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/vfile%406.0.3/node_modules/vfile/lib/minurl.shared.js"], "sourcesContent": ["/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nexport function isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC,GACD,+EAA+E;;;;AACxE,SAAS,MAAM,aAAa;IACjC,OAAO,QACL,kBAAkB,QAChB,OAAO,kBAAkB,YACzB,UAAU,iBACV,cAAc,IAAI,IAClB,cAAc,iBACd,cAAc,QAAQ,IACtB,sCAAsC;IACtC,cAAc,IAAI,KAAK;AAE7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/vfile%406.0.3/node_modules/vfile/lib/index.js"], "sourcesContent": ["/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {VFileMessage} from 'vfile-message'\nimport {minpath} from '#minpath'\nimport {minproc} from '#minproc'\nimport {urlToPath, isUrl} from '#minurl'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = minpath.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? minpath.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = minpath.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? minpath.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;CAEC;;;AAED;AACA;AACA;AACA;AAAA;;;;;AAEA;;;;CAIC,GACD,MAAM,QAA8B;IAClC;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM;IACX;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,YAAY,KAAK,CAAE;QACjB,4BAA4B,GAC5B,IAAI;QAEJ,IAAI,CAAC,OAAO;YACV,UAAU,CAAC;QACb,OAAO,IAAI,CAAA,GAAA,kMAAA,CAAA,QAAK,AAAD,EAAE,QAAQ;YACvB,UAAU;gBAAC,MAAM;YAAK;QACxB,OAAO,IAAI,OAAO,UAAU,YAAY,aAAa,QAAQ;YAC3D,UAAU;gBAAC;YAAK;QAClB,OAAO;YACL,UAAU;QACZ;QAEA,wCAAwC,GAExC;;;;KAIC,GACD,uEAAuE;QACvE,yDAAyD;QACzD,IAAI,CAAC,GAAG,GAAG,SAAS,UAAU,KAAK,6JAAA,CAAA,UAAO,CAAC,GAAG;QAE9C;;;;;;;KAOC,GACD,IAAI,CAAC,IAAI,GAAG,CAAC;QAEb;;;;;;KAMC,GACD,IAAI,CAAC,OAAO,GAAG,EAAE;QAEjB;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG,EAAE;QAElB;;;;KAIC,GACD,IAAI,CAAC,KAAK;QAEV,qDAAqD;QACrD,gCAAgC;QAChC;;;;;;;KAOC,GACD,IAAI,CAAC,GAAG;QAER;;;;;;;KAOC,GACD,IAAI,CAAC,MAAM;QAEX;;;;;;KAMC,GACD,IAAI,CAAC,MAAM;QACX,uCAAuC,GAEvC,oDAAoD;QACpD,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;YAC7B,MAAM,QAAQ,KAAK,CAAC,MAAM;YAE1B,uEAAuE;YACvE,gBAAgB;YAChB,IACE,SAAS,WACT,OAAO,CAAC,MAAM,KAAK,aACnB,OAAO,CAAC,MAAM,KAAK,MACnB;gBACA,yDAAyD;gBACzD,IAAI,CAAC,MAAM,GAAG,UAAU,YAAY;uBAAI,OAAO,CAAC,MAAM;iBAAC,GAAG,OAAO,CAAC,MAAM;YAC1E;QACF;QAEA,mBAAmB,GACnB,IAAI;QAEJ,mCAAmC;QACnC,IAAK,SAAS,QAAS;YACrB,8CAA8C;YAC9C,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ;gBAC1B,8CAA8C;gBAC9C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,IAAI,WAAW;QACb,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAC1B;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,SAAS,QAAQ,EAAE;QACrB,eAAe,UAAU;QACzB,WAAW,UAAU;QACrB,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI;IAC/C;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IACzB;IACN;IAEA;;;;;;;;;GASC,GACD,IAAI,QAAQ,OAAO,EAAE;QACnB,WAAW,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ;IACvD;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IACzB;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,QAAQ,OAAO,EAAE;QACnB,WAAW,SAAS;QACpB,WAAW,IAAI,CAAC,OAAO,EAAE;QAEzB,IAAI,SAAS;YACX,IAAI,QAAQ,WAAW,CAAC,OAAO,GAAG,OAAO,KAAI;gBAC3C,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI;gBAC5B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE;IACnE;IAEA;;;;;GAKC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;IAC9C;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,KAAK,IAAI,EAAE;QACb,IAAI,CAAA,GAAA,kMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;YACf,OAAO,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;QACnB;QAEA,eAAe,MAAM;QAErB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACpB;IACF;IAEA;;;;;GAKC,GACD,IAAI,OAAO;QACT,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,IACxC;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,KAAK,IAAI,EAAE;QACb,eAAe,MAAM;QACrB,WAAW,MAAM;QACjB,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;IACzE;IAEA,6BAA6B;IAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2DC,GACD,KAAK,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QAClD,4CAA4C;QAC5C,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,eAAe,wBAAwB;QAEpE,QAAQ,KAAK,GAAG;QAEhB,MAAM;IACR;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDC,GACD,KAAK,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QAClD,4CAA4C;QAC5C,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,eAAe,wBAAwB;QAEpE,QAAQ,KAAK,GAAG;QAEhB,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDC,GACD,QAAQ,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QACrD,MAAM,UAAU,IAAI,6MAAA,CAAA,eAAY,CAC9B,4CAA4C;QAC5C,eACA,wBACA;QAGF,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,QAAQ,IAAI;YAC7C,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI;QAC1B;QAEA,QAAQ,KAAK,GAAG;QAEhB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,OAAO;IACT;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,QAAQ,EAAE;QACjB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,OAAO;QACT;QAEA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,UAAU;YAClC,OAAO,IAAI,CAAC,KAAK;QACnB;QAEA,MAAM,UAAU,IAAI,YAAY,YAAY;QAC5C,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;IAClC;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,IAAI,EAAE,IAAI;IAC5B,IAAI,QAAQ,KAAK,QAAQ,CAAC,uJAAA,CAAA,UAAO,CAAC,GAAG,GAAG;QACtC,MAAM,IAAI,MACR,MAAM,OAAO,yCAAyC,uJAAA,CAAA,UAAO,CAAC,GAAG,GAAG;IAExE;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,eAAe,IAAI,EAAE,IAAI;IAChC,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,IAAI,EAAE,IAAI;IAC5B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,cAAc,OAAO;IACvC;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,QACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAEtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/unified%4011.0.5/node_modules/unified/lib/callable-instance.js"], "sourcesContent": ["export const CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n"], "names": [], "mappings": ";;;AAAO,MAAM,mBAOP;;;;OAIC,GACD,SAAU,QAAQ;IAChB,MAAM,OAAO,IAAI;IACjB,MAAM,SAAS,KAAK,WAAW;IAC/B,MAAM,QACJ,uBAAuB;IACvB,iCAAiC;IACjC,OAAO,SAAS;IAElB,MAAM,QAAQ,KAAK,CAAC,SAAS;IAC7B,uDAAuD,GACvD,MAAM,QAAQ;QACZ,OAAO,MAAM,KAAK,CAAC,OAAO;IAC5B;IAEA,OAAO,cAAc,CAAC,OAAO;IAE7B,kEAAkE;IAClE,YAAY;IACZ,yDAAyD;IACzD,QAAQ;IACR,oBAAoB;IACpB,kDAAkD;IAClD,EAAE;IACF,2BAA2B;IAC3B,iEAAiE;IACjE,gEAAgE;IAChE,IAAI;IAEJ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/node_modules/.pnpm/unified%4011.0.5/node_modules/unified/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport {bail} from 'bail'\nimport extend from 'extend'\nimport {ok as assert} from 'devlop'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\nimport {CallableInstance} from './callable-instance.js'\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      bail(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC,GAED;;;;;;CAMC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;CAOC,GAED;;;CAGC,GAED,4DAA4D;AAC5D,2DAA2D;AAC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8CC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;;;CAeC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0CC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwDC,GAED;;;;;;;;;;;;CAYC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,kEAAkE;AAElE,4EAA4E;AAC5E,qBAAqB;AACrB,6EAA6E;AAE7E,MAAM,MAAM,CAAC,EAAE,cAAc;AAetB,MAAM,kBAAkB,2MAAA,CAAA,mBAAgB;IAC7C;;GAEC,GACD,aAAc;QACZ,kEAAkE;QAClE,KAAK,CAAC;QAEN;;;;;;;;;;;;KAYC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;;;;KASC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd,qDAAqD;QACrD,mEAAmE;QACnE,mEAAmE;QACnE,0EAA0E;QAC1E;;;;;;KAMC,GACD,IAAI,CAAC,SAAS,GAAG,EAAE;QAEnB;;;;;;;;;;KAUC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;KAMC,GACD,IAAI,CAAC,WAAW,GAAG,CAAC;QAEpB;;;;;;KAMC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;;;;;KAMC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;QAElB;;;;;;;KAOC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;;;;;KAMC,GACD,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,yLAAA,CAAA,SAAM,AAAD;IAC3B;IAEA;;;;;;;;;;GAUC,GACD,OAAO;QACL,gEAAgE;QAChE,MAAM,cAEF,IAAI;QAER,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE;YACtC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM;YACtC,YAAY,GAAG,IAAI;QACrB;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,kLAAA,CAAA,UAAM,AAAD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;QAEhD,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0DC,GACD,KAAK,GAAG,EAAE,KAAK,EAAE;QACf,IAAI,OAAO,QAAQ,UAAU;YAC3B,aAAa;YACb,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,eAAe,QAAQ,IAAI,CAAC,MAAM;gBAClC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;gBACtB,OAAO,IAAI;YACb;YAEA,aAAa;YACb,OAAO,AAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,IAAK;QACnE;QAEA,aAAa;QACb,IAAI,KAAK;YACP,eAAe,QAAQ,IAAI,CAAC,MAAM;YAClC,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,IAAI;QACb;QAEA,aAAa;QACb,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,SAAS;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI;QACb;QAEA,2CAA2C;QAC3C,yEAAyE;QACzE,wBAAwB;QACxB,MAAM,OAAyD,IAAI;QAEnE,MAAO,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE;YACjD,MAAM,CAAC,UAAU,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;YAE/D,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO;gBACxB;YACF;YAEA,IAAI,OAAO,CAAC,EAAE,KAAK,MAAM;gBACvB,OAAO,CAAC,EAAE,GAAG;YACf;YAEA,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS;YAE3C,IAAI,OAAO,gBAAgB,YAAY;gBACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG,OAAO,iBAAiB;QAE3C,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,IAAI,EAAE;QACV,IAAI,CAAC,MAAM;QACX,MAAM,WAAW,MAAM;QACvB,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QACzC,aAAa,SAAS;QACtB,OAAO,OAAO,OAAO,WAAW;IAClC;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCC,GACD,QAAQ,IAAI,EAAE,IAAI,EAAE;QAClB,MAAM,OAAO,IAAI;QAEjB,IAAI,CAAC,MAAM;QACX,aAAa,WAAW,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QAClD,eAAe,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAExD,OAAO,OAAO,SAAS,WAAW,QAAQ,IAAI,QAAQ;;QAEtD,+BAA+B;QAC/B;;;;KAIC,GACD,SAAS,SAAS,OAAO,EAAE,MAAM;YAC/B,MAAM,WAAW,MAAM;YACvB,wEAAwE;YACxE,iCAAiC;YACjC,MAAM,YAEsB,KAAK,KAAK,CAAC;YAGvC,KAAK,GAAG,CAAC,WAAW,UAAU,SAAU,KAAK,EAAE,IAAI,EAAE,IAAI;gBACvD,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM;oBAC3B,OAAO,SAAS;gBAClB;gBAEA,+DAA+D;gBAC/D,6CAA6C;gBAC7C,MAAM,cAEsB;gBAG5B,MAAM,gBAAgB,KAAK,SAAS,CAAC,aAAa;gBAElD,IAAI,gBAAgB,gBAAgB;oBAClC,KAAK,KAAK,GAAG;gBACf,OAAO;oBACL,KAAK,MAAM,GAAG;gBAChB;gBAEA,SAAS,OAAsD;YACjE;YAEA;;;;OAIC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;gBAC3B,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO;gBACT,OAAO,IAAI,SAAS;oBAClB,QAAQ;gBACV,OAAO;oBACL,CAAA,GAAA,+LAAA,CAAA,KAAM,AAAD,EAAE,MAAM;oBACb,KAAK,WAAW;gBAClB;YACF;QACF;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BC,GACD,YAAY,IAAI,EAAE;QAChB,oBAAoB,GACpB,IAAI,WAAW;QACf,uDAAuD,GACvD,IAAI;QAEJ,IAAI,CAAC,MAAM;QACX,aAAa,eAAe,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QACtD,eAAe,eAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAE5D,IAAI,CAAC,OAAO,CAAC,MAAM;QACnB,WAAW,eAAe,WAAW;QACrC,CAAA,GAAA,+LAAA,CAAA,KAAM,AAAD,EAAE,QAAQ;QAEf,OAAO;;QAEP;;KAEC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;YAC3B,WAAW;YACX,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE;YACL,SAAS;QACX;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCC,GACD,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;QACpB,WAAW;QACX,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe,IAAI,CAAC,YAAY;QAEtC,IAAI,CAAC,QAAQ,OAAO,SAAS,YAAY;YACvC,OAAO;YACP,OAAO;QACT;QAEA,OAAO,OAAO,SAAS,WAAW,QAAQ,IAAI,QAAQ;;QAEtD,+BAA+B;QAC/B;;;;;;;KAOC,GACD,SAAS,SAAS,OAAO,EAAE,MAAM;YAC/B,CAAA,GAAA,+LAAA,CAAA,KAAM,AAAD,EACH,OAAO,SAAS,YAChB;YAEF,MAAM,WAAW,MAAM;YACvB,aAAa,GAAG,CAAC,MAAM,UAAU;YAEjC;;;;;OAKC,GACD,SAAS,SAAS,KAAK,EAAE,UAAU,EAAE,IAAI;gBACvC,MAAM,gBAEF,cAAc;gBAGlB,IAAI,OAAO;oBACT,OAAO;gBACT,OAAO,IAAI,SAAS;oBAClB,QAAQ;gBACV,OAAO;oBACL,CAAA,GAAA,+LAAA,CAAA,KAAM,AAAD,EAAE,MAAM;oBACb,KAAK,WAAW,eAAe;gBACjC;YACF;QACF;IACF;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,QAAQ,IAAI,EAAE,IAAI,EAAE;QAClB,oBAAoB,GACpB,IAAI,WAAW;QACf,uEAAuE,GACvE,IAAI;QAEJ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAErB,WAAW,WAAW,OAAO;QAC7B,CAAA,GAAA,+LAAA,CAAA,KAAM,AAAD,EAAE,QAAQ;QACf,OAAO;;QAEP;;KAEC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;YAC3B,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE;YACL,SAAS;YACT,WAAW;QACb;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BC,GACD,UAAU,IAAI,EAAE,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM;QACX,MAAM,WAAW,MAAM;QACvB,MAAM,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC/C,eAAe,aAAa;QAC5B,WAAW;QAEX,OAAO,SAAS,MAAM;IACxB;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDC,GACD,IAAI,KAAK,EAAE,GAAG,UAAU,EAAE;QACxB,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,YAAY,IAAI,CAAC,SAAS;QAEhC,eAAe,OAAO,IAAI,CAAC,MAAM;QAEjC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,SAAS;QACX,OAAO,IAAI,OAAO,UAAU,YAAY;YACtC,UAAU,OAAO;QACnB,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,QAAQ;YACV,OAAO;gBACL,UAAU;YACZ;QACF,OAAO;YACL,MAAM,IAAI,UAAU,iCAAiC,QAAQ;QAC/D;QAEA,OAAO,IAAI;;QAEX;;;KAGC,GACD,SAAS,IAAI,KAAK;YAChB,IAAI,OAAO,UAAU,YAAY;gBAC/B,UAAU,OAAO,EAAE;YACrB,OAAO,IAAI,OAAO,UAAU,UAAU;gBACpC,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,CAAC,QAAQ,GAAG,WAAW,GACiB;oBAC9C,UAAU,QAAQ;gBACpB,OAAO;oBACL,UAAU;gBACZ;YACF,OAAO;gBACL,MAAM,IAAI,UAAU,iCAAiC,QAAQ;YAC/D;QACF;QAEA;;;KAGC,GACD,SAAS,UAAU,MAAM;YACvB,IAAI,CAAC,CAAC,aAAa,MAAM,KAAK,CAAC,CAAC,cAAc,MAAM,GAAG;gBACrD,MAAM,IAAI,MACR;YAEJ;YAEA,QAAQ,OAAO,OAAO;YAEtB,IAAI,OAAO,QAAQ,EAAE;gBACnB,UAAU,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,UAAM,AAAD,EAAE,MAAM,UAAU,QAAQ,EAAE,OAAO,QAAQ;YACvE;QACF;QAEA;;;KAGC,GACD,SAAS,QAAQ,OAAO;YACtB,IAAI,QAAQ,CAAC;YAEb,IAAI,YAAY,QAAQ,YAAY,WAAW;YAC7C,SAAS;YACX,OAAO,IAAI,MAAM,OAAO,CAAC,UAAU;gBACjC,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;oBAC/B,MAAM,QAAQ,OAAO,CAAC,MAAM;oBAC5B,IAAI;gBACN;YACF,OAAO;gBACL,MAAM,IAAI,UAAU,sCAAsC,UAAU;YACtE;QACF;QAEA;;;;KAIC,GACD,SAAS,UAAU,MAAM,EAAE,UAAU;YACnC,IAAI,QAAQ,CAAC;YACb,IAAI,aAAa,CAAC;YAElB,MAAO,EAAE,QAAQ,UAAU,MAAM,CAAE;gBACjC,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,QAAQ;oBAClC,aAAa;oBACb;gBACF;YACF;YAEA,IAAI,eAAe,CAAC,GAAG;gBACrB,UAAU,IAAI,CAAC;oBAAC;uBAAW;iBAAW;YACxC,OAGK,IAAI,WAAW,MAAM,GAAG,GAAG;gBAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;gBACzB,MAAM,iBAAiB,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC/C,IAAI,CAAA,GAAA,0MAAA,CAAA,UAAU,AAAD,EAAE,mBAAmB,CAAA,GAAA,0MAAA,CAAA,UAAU,AAAD,EAAE,UAAU;oBACrD,UAAU,CAAA,GAAA,kLAAA,CAAA,UAAM,AAAD,EAAE,MAAM,gBAAgB;gBACzC;gBAEA,SAAS,CAAC,WAAW,GAAG;oBAAC;oBAAQ;uBAAY;iBAAK;YACpD;QACF;IACF;AACF;AA8BO,MAAM,UAAU,IAAI,YAAY,MAAM;AAE7C;;;;;;CAMC,GACD,SAAS,aAAa,IAAI,EAAE,KAAK;IAC/B,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,IAAI,UAAU,aAAa,OAAO;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,IAAI,EAAE,KAAK;IACjC,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,IAAI,UAAU,aAAa,OAAO;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,IAAI,EAAE,MAAM;IAClC,IAAI,QAAQ;QACV,MAAM,IAAI,MACR,kBACE,OACA;IAEN;AACF;AAEA;;;;;CAKC,GACD,SAAS,WAAW,IAAI;IACtB,8DAA8D;IAC9D,iCAAiC;IACjC,IAAI,CAAC,CAAA,GAAA,0MAAA,CAAA,UAAU,AAAD,EAAE,SAAS,OAAO,KAAK,IAAI,KAAK,UAAU;QACtD,MAAM,IAAI,UAAU,yBAAyB,OAAO;IACpD,QAAQ;IACV;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,IAAI,EAAE,SAAS,EAAE,QAAQ;IAC3C,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MACR,MAAM,OAAO,4BAA4B,YAAY;IAEzD;AACF;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,gBAAgB,SAAS,QAAQ,IAAI,uLAAA,CAAA,QAAK,CAAC;AACpD;AAEA;;;CAGC,GACD,SAAS,gBAAgB,KAAK;IAC5B,OAAO,QACL,SACE,OAAO,UAAU,YACjB,aAAa,SACb,cAAc;AAEpB;AAEA;;;CAGC,GACD,SAAS,gBAAgB,KAAK;IAC5B,OAAO,OAAO,UAAU,YAAY,aAAa;AACnD;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,QACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAEtB", "ignoreList": [0], "debugId": null}}]}