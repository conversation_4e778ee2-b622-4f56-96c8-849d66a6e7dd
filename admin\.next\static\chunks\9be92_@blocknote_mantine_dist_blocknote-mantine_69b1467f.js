(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/9be92_@blocknote_mantine_dist_blocknote-mantine_69b1467f.js", {

"[project]/node_modules/.pnpm/@blocknote+mantine@0.33.0_@types+hast@3.0.4_@types+react@19.1.2_highlight.js@11.11.1_lowlight_cypfhgfko7e6ia7ytp2wxiqnhm/node_modules/@blocknote/mantine/dist/blocknote-mantine.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BlockNoteView": (()=>Ot),
    "applyBlockNoteCSSVariablesFromTheme": (()=>G),
    "components": (()=>Mt),
    "darkDefaultTheme": (()=>zt),
    "defaultColorScheme": (()=>f),
    "lightDefaultTheme": (()=>U),
    "removeBlockNoteCSSVariables": (()=>Ie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@blocknote+core@0.33.0_@types+hast@3.0.4_highlight.js@11.11.1_lowlight@3.3.0/node_modules/@blocknote/core/dist/blocknote.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@blocknote+react@0.33.0_@types+hast@3.0.4_highlight.js@11.11.1_lowlight@3.3.0_react-dom@19.1._schy3hvqyhquxxajeo6gf73mce/node_modules/@blocknote/react/dist/blocknote-react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Button/Button.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$ActionIcon$2f$ActionIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tooltip$2f$Tooltip$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Stack$2f$Stack$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Stack/Stack.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Text/Text.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Group/Group.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Chip$2f$Chip$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Chip/Chip.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Divider$2f$Divider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Divider/Divider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Card$2f$Card$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Card/Card.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Skeleton$2f$Skeleton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Skeleton/Skeleton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Avatar$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$TextInput$2f$TextInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Menu/Menu.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Checkbox$2f$CheckIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Checkbox/CheckIcon.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tabs$2f$Tabs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$LoadingOverlay$2f$LoadingOverlay$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/LoadingOverlay/LoadingOverlay.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$FileInput$2f$FileInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/FileInput/FileInput.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$PopoverDropdown$2f$PopoverDropdown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Popover/PopoverDropdown/PopoverDropdown.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$PopoverTarget$2f$PopoverTarget$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Popover/PopoverTarget/PopoverTarget.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$Popover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Popover/Popover.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Badge$2f$Badge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Badge/Badge.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Loader$2f$Loader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Loader/Loader.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Flex$2f$Flex$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/components/Flex/Flex.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+core@7.17.8_@mantine+hooks@7.17.8_react@19.1.0__@types+react@19.1.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.4_@babel+core@7.26.10_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$hover$2f$use$2d$hover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-hover/use-hover.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$focus$2d$within$2f$use$2d$focus$2d$within$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-focus-within/use-focus-within.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$merged$2d$ref$2f$use$2d$merged$2d$ref$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-merged-ref/use-merged-ref.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$focus$2d$trap$2f$use$2d$focus$2d$trap$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mantine+hooks@7.17.8_react@19.1.0/node_modules/@mantine/hooks/esm/use-focus-trap/use-focus-trap.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
const te = (e, n, t = !1)=>{
    const o = [];
    function r(i, l = "--bn") {
        for(const a in i){
            const u = a.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase(), c = `${l}-${u}`;
            typeof i[a] != "object" ? (typeof i[a] == "number" && (i[a] = `${i[a]}px`), t ? n.style.removeProperty(c) : n.style.setProperty(c, i[a].toString())) : r(i[a], c);
        }
    }
    return r(e), o;
}, G = (e, n)=>te(e, n), De = {
    colors: {
        editor: {
            text: void 0,
            background: void 0
        },
        menu: {
            text: void 0,
            background: void 0
        },
        tooltip: {
            text: void 0,
            background: void 0
        },
        hovered: {
            text: void 0,
            background: void 0
        },
        selected: {
            text: void 0,
            background: void 0
        },
        disabled: {
            text: void 0,
            background: void 0
        },
        shadow: void 0,
        border: void 0,
        sideMenu: void 0,
        highlights: {
            gray: {
                text: void 0,
                background: void 0
            },
            brown: {
                text: void 0,
                background: void 0
            },
            red: {
                text: void 0,
                background: void 0
            },
            orange: {
                text: void 0,
                background: void 0
            },
            yellow: {
                text: void 0,
                background: void 0
            },
            green: {
                text: void 0,
                background: void 0
            },
            blue: {
                text: void 0,
                background: void 0
            },
            purple: {
                text: void 0,
                background: void 0
            },
            pink: {
                text: void 0,
                background: void 0
            }
        }
    },
    borderRadius: void 0,
    fontFamily: void 0
}, Ie = (e)=>te(De, e, !0), ne = (e)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Stack$2f$Stack$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
        gap: 0,
        className: "bn-tooltip",
        children: [
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                size: "sm",
                lineClamp: 5,
                children: e.mainTooltip
            }),
            e.secondaryTooltip && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                size: "xs",
                lineClamp: 5,
                children: e.secondaryTooltip
            })
        ]
    }), D = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, mainTooltip: r, secondaryTooltip: i, icon: l, isSelected: a, isDisabled: u, onClick: c, label: g, variant: h, ...p } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(p, !1);
    const [C, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), k = o ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        "aria-label": g,
        className: t,
        onMouseDown: (x)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSafari"])() && x.currentTarget.focus();
        },
        onClick: (x)=>{
            y(!0), c == null || c(x);
        },
        onPointerLeave: ()=>y(!1),
        "aria-pressed": a,
        "data-selected": a || void 0,
        "data-test": r ? r.slice(0, 1).toLowerCase() + r.replace(/\s+/g, "").slice(1) : void 0,
        size: h === "compact" ? "compact-xs" : "xs",
        disabled: u || !1,
        ref: n,
        ...p,
        children: o
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$ActionIcon$2f$ActionIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionIcon"], {
        className: t,
        "aria-label": g,
        onMouseDown: (x)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSafari"])() && x.currentTarget.focus();
        },
        onClick: (x)=>{
            y(!0), c == null || c(x);
        },
        onPointerLeave: ()=>y(!1),
        "aria-pressed": a,
        "data-selected": a || void 0,
        "data-test": r ? r.slice(0, 1).toLowerCase() + r.replace(/\s+/g, "").slice(1) : void 0,
        size: h === "compact" ? 20 : 30,
        disabled: u || !1,
        ref: n,
        ...p,
        children: l
    });
    return r ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tooltip$2f$Tooltip$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
        disabled: C,
        withinPortal: !1,
        label: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ne, {
            mainTooltip: r,
            secondaryTooltip: i
        }),
        children: k
    }) : k;
}), Le = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, text: o, icon: r, isSelected: i, mainTooltip: l, secondaryTooltip: a, onClick: u, onMouseEnter: c, ...g } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(g, !1);
    const [h, p] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), C = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Chip$2f$Chip$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Chip"], {
        className: t,
        checked: i === !0,
        wrapperProps: {
            onMouseEnter: c,
            onMouseLeave: ()=>p(!1),
            onClick: (y)=>{
                p(!0), u == null || u(y);
            }
        },
        variant: "light",
        icon: null,
        ref: n,
        children: [
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                children: r
            }),
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                children: o
            })
        ]
    });
    return !l || h ? C : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tooltip$2f$Tooltip$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
        refProp: "rootRef",
        withinPortal: !1,
        label: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ne, {
            mainTooltip: l,
            secondaryTooltip: a
        }),
        children: C
    });
}), Be = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        className: t,
        ref: n,
        children: o
    });
}), Ee = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, headerText: r, selected: i, onFocus: l, onBlur: a, tabIndex: u, ...c } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(c, !1);
    const g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        var h;
        i && ((h = g.current) == null || h.scrollIntoView({
            behavior: "smooth",
            block: "center"
        }));
    }, [
        i
    ]), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Card$2f$Card$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeCSSClasses"])(t, i ? "selected" : ""),
        onFocus: l,
        onBlur: a,
        tabIndex: u,
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])([
            n,
            g
        ]),
        children: [
            r && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                className: "bn-header-text",
                children: r
            }),
            o
        ]
    });
}), ze = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Card$2f$Card$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"].Section, {
        className: t,
        ref: n,
        children: o
    });
}), je = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Divider$2f$Divider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Divider"], {
        className: t,
        label: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
            children: o
        }),
        ref: n
    });
}), $e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { authorInfo: t, timeString: o, edited: r, ...i } = e, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDictionary"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i, !1), t === "loading" ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        children: [
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Skeleton$2f$Skeleton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                height: 24,
                width: 24
            }),
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Skeleton$2f$Skeleton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                    height: 12,
                    width: 100
                })
            })
        ]
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        children: [
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Avatar$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                src: t.avatarUrl,
                alt: t.username,
                radius: "xl",
                size: "sm",
                color: "initials"
            }),
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                fz: "sm",
                fw: "bold",
                children: [
                    t.username,
                    /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                        fz: "xs",
                        c: "dimmed",
                        span: !0,
                        ml: "xs",
                        children: [
                            o,
                            " ",
                            r && `(${l.comments.edited})`
                        ]
                    })
                ]
            })
        ]
    });
}), Ae = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, showActions: o, authorInfo: r, timeString: i, edited: l, actions: a, children: u, ...c } = e, { hovered: g, ref: h } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$hover$2f$use$2d$hover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useHover"])(), { focused: p, ref: C } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$focus$2d$within$2f$use$2d$focus$2d$within$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFocusWithin"])(), y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])([
        n,
        h
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(c, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        pos: "relative",
        ref: y,
        className: t,
        children: [
            a && (o === !0 || o === void 0 || o === "hover" && g || p) ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
                ref: C,
                style: {
                    position: "absolute",
                    right: 0,
                    top: 0,
                    zIndex: 10
                },
                children: a
            }) : null,
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])($e, {
                ...e
            }),
            u
        ]
    });
}), _e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, autoFocus: o, onFocus: r, onBlur: i, editor: l, editable: a, ...u } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(u, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Ot, {
        autoFocus: o,
        className: t,
        editor: e.editor,
        sideMenu: !1,
        slashMenu: !1,
        tableHandles: !1,
        filePanel: !1,
        formattingToolbar: !1,
        editable: a,
        ref: n,
        onFocus: r,
        onBlur: i,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormattingToolbarController"], {
            formattingToolbar: Ve
        })
    });
}), Ve = ()=>{
    const e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFormattingToolbarItems"])([]).filter((n)=>n.key !== "nestBlockButton" && n.key !== "unnestBlockButton");
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormattingToolbar"], {
        blockTypeSelectItems: [],
        children: e
    });
}, Ge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, name: o, label: r, variant: i, icon: l, value: a, autoFocus: u, placeholder: c, disabled: g, onKeyDown: h, onChange: p, onSubmit: C, autoComplete: y, rightSection: k, ...x } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(x), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$TextInput$2f$TextInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextInput"], {
        size: "xs",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeCSSClasses"])(t || "", i === "large" ? "bn-mt-input-large" : ""),
        ref: n,
        name: o,
        label: r,
        leftSection: l,
        value: a,
        autoFocus: u,
        "data-autofocus": u ? "true" : void 0,
        rightSection: k,
        placeholder: c,
        disabled: g,
        onKeyDown: h,
        onChange: p,
        onSubmit: C,
        autoComplete: y
    });
});
var oe = {
    color: void 0,
    size: void 0,
    className: void 0,
    style: void 0,
    attr: void 0
}, H = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext(oe), He = [
    "attr",
    "size",
    "title"
];
function Ke(e, n) {
    if (e == null) return {};
    var t = Ue(e, n), o, r;
    if (Object.getOwnPropertySymbols) {
        var i = Object.getOwnPropertySymbols(e);
        for(r = 0; r < i.length; r++)o = i[r], !(n.indexOf(o) >= 0) && Object.prototype.propertyIsEnumerable.call(e, o) && (t[o] = e[o]);
    }
    return t;
}
function Ue(e, n) {
    if (e == null) return {};
    var t = {};
    for(var o in e)if (Object.prototype.hasOwnProperty.call(e, o)) {
        if (n.indexOf(o) >= 0) continue;
        t[o] = e[o];
    }
    return t;
}
function F() {
    return F = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined), F.apply(this, arguments);
}
function K(e, n) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        n && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function O(e) {
    for(var n = 1; n < arguments.length; n++){
        var t = arguments[n] != null ? arguments[n] : {};
        n % 2 ? K(Object(t), !0).forEach(function(o) {
            We(e, o, t[o]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : K(Object(t)).forEach(function(o) {
            Object.defineProperty(e, o, Object.getOwnPropertyDescriptor(t, o));
        });
    }
    return e;
}
function We(e, n, t) {
    return n = qe(n), n in e ? Object.defineProperty(e, n, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[n] = t, e;
}
function qe(e) {
    var n = Ze(e, "string");
    return typeof n == "symbol" ? n : n + "";
}
function Ze(e, n) {
    if (typeof e != "object" || !e) return e;
    var t = e[Symbol.toPrimitive];
    if (t !== void 0) {
        var o = t.call(e, n);
        if (typeof o != "object") return o;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (n === "string" ? String : Number)(e);
}
function re(e) {
    return e && e.map((n, t)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(n.tag, O({
            key: t
        }, n.attr), re(n.child)));
}
function se(e) {
    return (n)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(Je, F({
            attr: O({}, e.attr)
        }, n), re(e.child));
}
function Je(e) {
    var n = (t)=>{
        var { attr: o, size: r, title: i } = e, l = Ke(e, He), a = r || t.size || "1em", u;
        return t.className && (u = t.className), e.className && (u = (u ? u + " " : "") + e.className), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", F({
            stroke: "currentColor",
            fill: "currentColor",
            strokeWidth: "0"
        }, t.attr, o, l, {
            className: u,
            style: O(O({
                color: e.color || t.color
            }, t.style), e.style),
            height: a,
            width: a,
            xmlns: "http://www.w3.org/2000/svg"
        }), i && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("title", null, i), e.children);
    };
    return H !== void 0 ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(H.Consumer, null, (t)=>n(t)) : n(oe);
}
function Qe(e) {
    return se({
        attr: {
            viewBox: "0 0 20 20",
            fill: "currentColor",
            "aria-hidden": "true"
        },
        child: [
            {
                tag: "path",
                attr: {
                    fillRule: "evenodd",
                    d: "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",
                    clipRule: "evenodd"
                },
                child: []
            }
        ]
    })(e);
}
function Xe(e) {
    return se({
        attr: {
            viewBox: "0 0 20 20",
            fill: "currentColor",
            "aria-hidden": "true"
        },
        child: [
            {
                tag: "path",
                attr: {
                    fillRule: "evenodd",
                    d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",
                    clipRule: "evenodd"
                },
                child: []
            }
        ]
    })(e);
}
const ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(void 0), Ye = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { children: t, onOpenChange: o, position: r, sub: i, // not used
    ...l } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(l);
    const [a, u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        g.current && clearTimeout(g.current), g.current = setTimeout(()=>{
            u(!1);
        }, 250);
    }, []), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        g.current && clearTimeout(g.current), u(!0);
    }, []);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ie.Provider, {
        value: {
            onMenuMouseOver: p,
            onMenuMouseLeave: h
        },
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Item, {
            className: "bn-menu-item bn-mt-sub-menu-item",
            ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$merged$2d$ref$2f$use$2d$merged$2d$ref$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])(n, c),
            onMouseOver: p,
            onMouseLeave: h,
            children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
                portalProps: {
                    target: c.current ? c.current.parentElement : void 0
                },
                middlewares: {
                    flip: !0,
                    shift: !0,
                    inline: !1,
                    size: !0
                },
                trigger: "hover",
                opened: a,
                onChange: o,
                position: r,
                children: t
            })
        })
    });
}), et = (e)=>{
    const { children: n, onOpenChange: t, position: o, sub: r, ...i } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i), r ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Ye, {
        ...e
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
        withinPortal: !1,
        middlewares: {
            flip: !0,
            shift: !0,
            inline: !1,
            size: !0
        },
        onChange: t,
        position: o,
        children: n
    });
}, tt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, icon: r, checked: i, subTrigger: l, onClick: a, ...u } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(u, !1), l ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
        onClick: (c)=>{
            c.preventDefault(), c.stopPropagation();
        },
        ref: n,
        ...u,
        children: [
            o,
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Xe, {
                size: 15
            })
        ]
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Item, {
        className: t,
        ref: n,
        leftSection: r,
        rightSection: i ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Checkbox$2f$CheckIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CheckIcon"], {
            size: 10
        }) : i === !1 ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            className: "bn-tick-space"
        }) : null,
        onClick: a,
        ...u,
        children: o
    });
}), nt = (e)=>{
    const { children: n, sub: t, // unused
    ...o } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(o), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Target, {
        children: n
    });
}, ot = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, sub: r, //unused
    ...i } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i);
    const l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ie);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Dropdown, {
        className: t,
        ref: n,
        onMouseOver: l == null ? void 0 : l.onMenuMouseOver,
        onMouseLeave: l == null ? void 0 : l.onMenuMouseLeave,
        children: o
    });
}), rt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, ...o } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(o), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Divider, {
        className: t,
        ref: n
    });
}), st = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Label, {
        className: t,
        ref: n,
        children: o
    });
}), it = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, icon: r, onClick: i, onDragEnd: l, onDragStart: a, draggable: u, label: c, ...g } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(g, !1), r ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$ActionIcon$2f$ActionIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionIcon"], {
        size: 24,
        className: t,
        ref: n,
        onClick: i,
        onDragEnd: l,
        onDragStart: a,
        draggable: u,
        "aria-label": c,
        ...g,
        children: r
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        className: t,
        ref: n,
        onClick: i,
        onDragEnd: l,
        onDragStart: a,
        draggable: u,
        "aria-label": c,
        ...g,
        children: o
    });
}), at = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, tabs: o, defaultOpenTab: r, openTab: i, setOpenTab: l, loading: a, ...u } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(u), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        className: t,
        ref: n,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tabs$2f$Tabs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
            value: i,
            defaultValue: r,
            onChange: l,
            children: [
                a && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$LoadingOverlay$2f$LoadingOverlay$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingOverlay"], {
                    visible: a
                }),
                /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tabs$2f$Tabs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"].List, {
                    children: o.map((c)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tabs$2f$Tabs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"].Tab, {
                            "data-test": `${c.name.toLowerCase()}-tab`,
                            value: c.name,
                            children: c.name
                        }, c.name))
                }),
                o.map((c)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Tabs$2f$Tabs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"].Panel, {
                        value: c.name,
                        children: c.tabPanel
                    }, c.name))
            ]
        })
    });
}), lt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, onClick: r, label: i, ...l } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(l), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        size: "xs",
        "aria-label": i,
        className: t,
        ref: n,
        onClick: r,
        ...l,
        children: o
    });
}), ct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, accept: o, value: r, placeholder: i, onChange: l, ...a } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(a), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$FileInput$2f$FileInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileInput"], {
        size: "xs",
        className: t,
        ref: n,
        accept: o,
        value: r,
        placeholder: i,
        onChange: l,
        ...a
    });
}), ut = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: t,
        ref: n,
        children: o
    });
}), dt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, value: o, placeholder: r, onKeyDown: i, onChange: l, ...a } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(a), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$TextInput$2f$TextInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextInput"], {
        size: "xs",
        "data-test": "embed-input",
        className: t,
        ref: n,
        value: o,
        placeholder: r,
        onKeyDown: i,
        onChange: l
    });
}), mt = (e)=>{
    const { opened: n, position: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$Popover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
        withinPortal: !1,
        opened: n,
        position: t,
        children: o
    });
}, ft = (e)=>{
    const { children: n, ...t } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(t), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$PopoverTarget$2f$PopoverTarget$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTarget"], {
        children: n
    });
}, gt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, variant: r, // unused
    ...i } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Popover$2f$PopoverDropdown$2f$PopoverDropdown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverDropdown"], {
        className: t,
        ref: n,
        children: o
    });
}), ht = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        align: "center",
        gap: 0,
        className: t,
        ref: n,
        ...r,
        children: o
    });
}), bt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, icon: r, onClick: i, onDragEnd: l, onDragStart: a, draggable: u, label: c, ...g } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(g, !1), r ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$ActionIcon$2f$ActionIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionIcon"], {
        size: 24,
        className: t,
        ref: n,
        onClick: i,
        onDragEnd: l,
        onDragStart: a,
        draggable: u,
        "aria-label": c,
        ...g,
        children: r
    }) : /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        className: t,
        ref: n,
        onClick: i,
        onDragEnd: l,
        onDragStart: a,
        draggable: u,
        "aria-label": c,
        ...g,
        children: o
    });
}), pt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, id: r, ...i } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Stack$2f$Stack$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
        gap: 0,
        className: t,
        ref: n,
        id: r,
        role: "listbox",
        children: o
    });
}), vt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        className: t,
        ref: n,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
            className: "bn-mt-suggestion-menu-item-title",
            children: o
        })
    });
}), St = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, isSelected: o, onClick: r, item: i, id: l, ...a } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(a);
    const u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!u.current || !o) return;
        const c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["elementOverflow"])(u.current, document.querySelector(".bn-suggestion-menu, #ai-suggestion-menu"));
        c === "top" ? u.current.scrollIntoView(!0) : c === "bottom" && u.current.scrollIntoView(!1);
    }, [
        o
    ]), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        gap: 0,
        className: t,
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$merged$2d$ref$2f$use$2d$merged$2d$ref$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])(n, u),
        id: l,
        role: "option",
        onMouseDown: (c)=>c.preventDefault(),
        onClick: r,
        "aria-selected": o || void 0,
        children: [
            i.icon && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
                className: "bn-mt-suggestion-menu-item-section",
                "data-position": "left",
                children: i.icon
            }),
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Stack$2f$Stack$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
                gap: 0,
                className: "bn-mt-suggestion-menu-item-body",
                children: [
                    /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                        className: "bn-mt-suggestion-menu-item-title",
                        children: i.title
                    }),
                    /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Text$2f$Text$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
                        className: "bn-mt-suggestion-menu-item-subtitle",
                        children: i.subtext
                    })
                ]
            }),
            i.badge && /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
                "data-position": "right",
                className: "bn-mt-suggestion-menu-item-section",
                children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Badge$2f$Badge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                    size: "xs",
                    children: i.badge
                })
            })
        ]
    });
}), yt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, ...r } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(r), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        className: t,
        ref: n,
        children: o
    });
}), xt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, ...o } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(o), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Loader$2f$Loader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loader"], {
        className: t,
        type: "dots",
        size: 16,
        ref: n
    });
}), Ct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, id: r, columns: i, ...l } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(l), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: t,
        style: {
            gridTemplateColumns: `repeat(${i}, 1fr)`
        },
        ref: n,
        id: r,
        role: "grid",
        children: o
    });
}), wt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, columns: r, ...i } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
        className: t,
        style: {
            gridColumn: `1 / ${r + 1}`
        },
        ref: n,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Group$2f$Group$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Group"], {
            className: "bn-mt-suggestion-menu-item-title",
            children: o
        })
    });
}), Tt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, isSelected: o, onClick: r, item: i, id: l, ...a } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(a);
    const u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!u.current || !o) return;
        const c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["elementOverflow"])(u.current, document.querySelector(".bn-grid-suggestion-menu"));
        c === "top" ? u.current.scrollIntoView(!0) : c === "bottom" && u.current.scrollIntoView(!1);
    }, [
        o
    ]), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: t,
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$merged$2d$ref$2f$use$2d$merged$2d$ref$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])(n, u),
        id: l,
        role: "option",
        onClick: r,
        "aria-selected": o || void 0,
        children: i.icon
    });
}), Nt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, // unused, using "dots" instead
    columns: r, ...i } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Loader$2f$Loader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loader"], {
        className: t,
        style: {
            gridColumn: `1 / ${r + 1}`
        },
        type: "dots",
        ref: n
    });
}), kt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { children: t, className: o, onMouseDown: r, onClick: i, ...l } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(l, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        className: o,
        ref: n,
        onMouseDown: r,
        onClick: i,
        ...l,
        children: t
    });
}), Pt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, draggable: r, onDragStart: i, onDragEnd: l, style: a, label: u, ...c } = e;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(c, !1), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
        className: t,
        ref: n,
        "aria-label": u,
        draggable: r,
        onDragStart: i,
        onDragEnd: l,
        style: a,
        ...c,
        children: o
    });
}), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, children: o, onMouseEnter: r, onMouseLeave: i, variant: l, ...a } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(a);
    const { ref: u, focused: c } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$focus$2d$within$2f$use$2d$focus$2d$within$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFocusWithin"])(), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$focus$2d$trap$2f$use$2d$focus$2d$trap$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFocusTrap"])(c), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$hooks$2f$esm$2f$use$2d$merged$2d$ref$2f$use$2d$merged$2d$ref$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRefs"])(n, u, g);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Flex$2f$Flex$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Flex"], {
        className: t,
        ref: h,
        role: "toolbar",
        onMouseEnter: r,
        onMouseLeave: i,
        gap: l === "action-toolbar" ? 2 : void 0,
        children: o
    });
}), L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((e, n)=>{
    const { className: t, items: o, isDisabled: r, ...i } = e;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["assertEmpty"])(i);
    const l = o.filter((a)=>a.isSelected)[0];
    return l ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
        withinPortal: !1,
        transitionProps: {
            exitDuration: 0
        },
        disabled: r,
        middlewares: {
            flip: !0,
            shift: !0,
            inline: !1,
            size: !0
        },
        children: [
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Target, {
                children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    onMouseDown: (a)=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSafari"])() && a.currentTarget.focus();
                    },
                    leftSection: l.icon,
                    rightSection: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Qe, {}),
                    size: "xs",
                    variant: "subtle",
                    disabled: r,
                    children: l.text
                })
            }),
            /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Dropdown, {
                className: t,
                ref: n,
                children: o.map((a)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Menu$2f$Menu$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"].Item, {
                        onClick: a.onClick,
                        leftSection: a.icon,
                        rightSection: a.isSelected ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$components$2f$Checkbox$2f$CheckIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CheckIcon"], {
                            size: 10,
                            className: "bn-tick-icon"
                        }) : // Ensures space for tick even if item isn't currently selected.
                        /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                            className: "bn-tick-space"
                        }),
                        disabled: a.isDisabled,
                        children: a.text
                    }, a.text))
            })
        ]
    }) : null;
}), Mt = {
    FormattingToolbar: {
        Root: I,
        Button: D,
        Select: L
    },
    FilePanel: {
        Root: at,
        Button: lt,
        FileInput: ct,
        TabPanel: ut,
        TextInput: dt
    },
    GridSuggestionMenu: {
        Root: Ct,
        Item: Tt,
        EmptyItem: wt,
        Loader: Nt
    },
    LinkToolbar: {
        Root: I,
        Button: D,
        Select: L
    },
    SideMenu: {
        Root: ht,
        Button: bt
    },
    SuggestionMenu: {
        Root: pt,
        Item: St,
        EmptyItem: vt,
        Label: yt,
        Loader: xt
    },
    TableHandle: {
        Root: Pt,
        ExtendButton: kt
    },
    Generic: {
        Badge: {
            Root: Le,
            Group: Be
        },
        Form: {
            Root: (e)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                    children: e.children
                }),
            TextInput: Ge
        },
        Menu: {
            Root: et,
            Trigger: nt,
            Dropdown: ot,
            Divider: rt,
            Label: st,
            Item: tt,
            Button: it
        },
        Popover: {
            Root: mt,
            Trigger: ft,
            Content: gt
        },
        Toolbar: {
            Root: I,
            Button: D,
            Select: L
        }
    },
    Comments: {
        Comment: Ae,
        Editor: _e,
        Card: Ee,
        CardSection: ze,
        ExpandSectionsPrompt: je
    }
}, Ft = {
    // Removes button press effect
    activeClassName: ""
}, Ot = (e)=>{
    const { className: n, theme: t, ...o } = e, r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBlockNoteContext"])(), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePrefersColorScheme"])(), l = (r == null ? void 0 : r.colorSchemePreference) || i, a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((c)=>{
        if (c && (Ie(c), typeof t == "object")) {
            if ("light" in t && "dark" in t) {
                G(t[l === "dark" ? "dark" : "light"], c);
                return;
            }
            G(t, c);
            return;
        }
    }, [
        l,
        t
    ]), u = typeof t == "string" ? t : l !== "no-preference" ? l : "light";
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ComponentsContext"].Provider, {
        value: Mt,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mantine$2b$core$40$7$2e$17$2e$8_$40$mantine$2b$hooks$40$7$2e$17$2e$8_react$40$19$2e$1$2e$0_$5f40$types$2b$react$40$19$2e$1$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f40$mantine$2f$core$2f$esm$2f$core$2f$MantineProvider$2f$MantineProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MantineProvider"], {
            theme: Ft,
            cssVariablesSelector: ".bn-mantine",
            getRootElement: ()=>{},
            children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$4_$40$babel$2b$core$40$7$2e$26$2e$10_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$react$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0_react$2d$dom$40$19$2e$1$2e$_schy3hvqyhquxxajeo6gf73mce$2f$node_modules$2f40$blocknote$2f$react$2f$dist$2f$blocknote$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BlockNoteViewRaw"], {
                "data-mantine-color-scheme": u,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeCSSClasses"])("bn-mantine", n || ""),
                theme: typeof t == "object" ? void 0 : t,
                ...o,
                ref: a
            })
        })
    });
}, f = [
    "#FFFFFF",
    "#EFEFEF",
    "#CFCFCF",
    "#AFAFAF",
    "#7F7F7F",
    "#3F3F3F",
    "#1F1F1F",
    "#161616",
    "#0F0F0F",
    "#000000"
], U = {
    colors: {
        editor: {
            text: f[5],
            background: f[0]
        },
        menu: {
            text: f[5],
            background: f[0]
        },
        tooltip: {
            text: f[5],
            background: f[1]
        },
        hovered: {
            text: f[5],
            background: f[1]
        },
        selected: {
            text: f[0],
            background: f[5]
        },
        disabled: {
            text: f[3],
            background: f[1]
        },
        shadow: f[2],
        border: f[1],
        sideMenu: f[2],
        highlights: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLORS_DEFAULT"]
    },
    borderRadius: 6,
    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Open Sans", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif'
}, zt = {
    colors: {
        editor: {
            text: f[2],
            background: f[6]
        },
        menu: {
            text: f[2],
            background: f[6]
        },
        tooltip: {
            text: f[2],
            background: f[7]
        },
        hovered: {
            text: f[2],
            background: f[7]
        },
        selected: {
            text: f[2],
            background: f[8]
        },
        disabled: {
            text: f[5],
            background: f[7]
        },
        shadow: f[8],
        border: f[7],
        sideMenu: f[4],
        highlights: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$blocknote$2b$core$40$0$2e$33$2e$0_$40$types$2b$hast$40$3$2e$0$2e$4_highlight$2e$js$40$11$2e$11$2e$1_lowlight$40$3$2e$3$2e$0$2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$blocknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLORS_DARK_MODE_DEFAULT"]
    },
    borderRadius: U.borderRadius,
    fontFamily: U.fontFamily
};
;
 //# sourceMappingURL=blocknote-mantine.js.map
}}),
}]);

//# sourceMappingURL=9be92_%40blocknote_mantine_dist_blocknote-mantine_69b1467f.js.map