module.exports = {

"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast-util-to-mdast').Options} Options
 */ __turbopack_context__.s({});
;
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/types.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ARRAY": (()=>ARRAY),
    "BIGINT": (()=>BIGINT),
    "DATE": (()=>DATE),
    "ERROR": (()=>ERROR),
    "MAP": (()=>MAP),
    "OBJECT": (()=>OBJECT),
    "PRIMITIVE": (()=>PRIMITIVE),
    "REGEXP": (()=>REGEXP),
    "SET": (()=>SET),
    "VOID": (()=>VOID)
});
const VOID = -1;
const PRIMITIVE = 0;
const ARRAY = 1;
const OBJECT = 2;
const DATE = 3;
const REGEXP = 4;
const MAP = 5;
const SET = 6;
const ERROR = 7;
const BIGINT = 8; // export const SYMBOL = 9;
}}),
"[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/deserialize.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deserialize": (()=>deserialize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/types.js [app-ssr] (ecmascript)");
;
const env = typeof self === 'object' ? self : globalThis;
const deserializer = ($, _)=>{
    const as = (out, index)=>{
        $.set(index, out);
        return out;
    };
    const unpair = (index)=>{
        if ($.has(index)) return $.get(index);
        const [type, value] = _[index];
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIMITIVE"]:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VOID"]:
                return as(value, index);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ARRAY"]:
                {
                    const arr = as([], index);
                    for (const index of value)arr.push(unpair(index));
                    return arr;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OBJECT"]:
                {
                    const object = as({}, index);
                    for (const [key, index] of value)object[unpair(key)] = unpair(index);
                    return object;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DATE"]:
                return as(new Date(value), index);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEXP"]:
                {
                    const { source, flags } = value;
                    return as(new RegExp(source, flags), index);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MAP"]:
                {
                    const map = as(new Map, index);
                    for (const [key, index] of value)map.set(unpair(key), unpair(index));
                    return map;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SET"]:
                {
                    const set = as(new Set, index);
                    for (const index of value)set.add(unpair(index));
                    return set;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR"]:
                {
                    const { name, message } = value;
                    return as(new env[name](message), index);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BIGINT"]:
                return as(BigInt(value), index);
            case 'BigInt':
                return as(Object(BigInt(value)), index);
            case 'ArrayBuffer':
                return as(new Uint8Array(value).buffer, value);
            case 'DataView':
                {
                    const { buffer } = new Uint8Array(value);
                    return as(new DataView(buffer), value);
                }
        }
        return as(new env[type](value), index);
    };
    return unpair;
};
const deserialize = (serialized)=>deserializer(new Map, serialized)(0);
}}),
"[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/serialize.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "serialize": (()=>serialize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/types.js [app-ssr] (ecmascript)");
;
const EMPTY = '';
const { toString } = {};
const { keys } = Object;
const typeOf = (value)=>{
    const type = typeof value;
    if (type !== 'object' || !value) return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIMITIVE"],
        type
    ];
    const asString = toString.call(value).slice(8, -1);
    switch(asString){
        case 'Array':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ARRAY"],
                EMPTY
            ];
        case 'Object':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OBJECT"],
                EMPTY
            ];
        case 'Date':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DATE"],
                EMPTY
            ];
        case 'RegExp':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEXP"],
                EMPTY
            ];
        case 'Map':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MAP"],
                EMPTY
            ];
        case 'Set':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SET"],
                EMPTY
            ];
        case 'DataView':
            return [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ARRAY"],
                asString
            ];
    }
    if (asString.includes('Array')) return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ARRAY"],
        asString
    ];
    if (asString.includes('Error')) return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR"],
        asString
    ];
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OBJECT"],
        asString
    ];
};
const shouldSkip = ([TYPE, type])=>TYPE === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIMITIVE"] && (type === 'function' || type === 'symbol');
const serializer = (strict, json, $, _)=>{
    const as = (out, value)=>{
        const index = _.push(out) - 1;
        $.set(value, index);
        return index;
    };
    const pair = (value)=>{
        if ($.has(value)) return $.get(value);
        let [TYPE, type] = typeOf(value);
        switch(TYPE){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIMITIVE"]:
                {
                    let entry = value;
                    switch(type){
                        case 'bigint':
                            TYPE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BIGINT"];
                            entry = value.toString();
                            break;
                        case 'function':
                        case 'symbol':
                            if (strict) throw new TypeError('unable to serialize ' + type);
                            entry = null;
                            break;
                        case 'undefined':
                            return as([
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VOID"]
                            ], value);
                    }
                    return as([
                        TYPE,
                        entry
                    ], value);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ARRAY"]:
                {
                    if (type) {
                        let spread = value;
                        if (type === 'DataView') {
                            spread = new Uint8Array(value.buffer);
                        } else if (type === 'ArrayBuffer') {
                            spread = new Uint8Array(value);
                        }
                        return as([
                            type,
                            [
                                ...spread
                            ]
                        ], value);
                    }
                    const arr = [];
                    const index = as([
                        TYPE,
                        arr
                    ], value);
                    for (const entry of value)arr.push(pair(entry));
                    return index;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OBJECT"]:
                {
                    if (type) {
                        switch(type){
                            case 'BigInt':
                                return as([
                                    type,
                                    value.toString()
                                ], value);
                            case 'Boolean':
                            case 'Number':
                            case 'String':
                                return as([
                                    type,
                                    value.valueOf()
                                ], value);
                        }
                    }
                    if (json && 'toJSON' in value) return pair(value.toJSON());
                    const entries = [];
                    const index = as([
                        TYPE,
                        entries
                    ], value);
                    for (const key of keys(value)){
                        if (strict || !shouldSkip(typeOf(value[key]))) entries.push([
                            pair(key),
                            pair(value[key])
                        ]);
                    }
                    return index;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DATE"]:
                return as([
                    TYPE,
                    value.toISOString()
                ], value);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEXP"]:
                {
                    const { source, flags } = value;
                    return as([
                        TYPE,
                        {
                            source,
                            flags
                        }
                    ], value);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MAP"]:
                {
                    const entries = [];
                    const index = as([
                        TYPE,
                        entries
                    ], value);
                    for (const [key, entry] of value){
                        if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry)))) entries.push([
                            pair(key),
                            pair(entry)
                        ]);
                    }
                    return index;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SET"]:
                {
                    const entries = [];
                    const index = as([
                        TYPE,
                        entries
                    ], value);
                    for (const entry of value){
                        if (strict || !shouldSkip(typeOf(entry))) entries.push(pair(entry));
                    }
                    return index;
                }
        }
        const { message } = value;
        return as([
            TYPE,
            {
                name: type,
                message
            }
        ], value);
    };
    return pair;
};
const serialize = (value, { json, lossy } = {})=>{
    const _ = [];
    return serializer(!(json || lossy), !!json, new Map, _)(value), _;
};
}}),
"[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$deserialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/deserialize.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$serialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/serialize.js [app-ssr] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = typeof structuredClone === "function" ? /* c8 ignore start */ (any, options)=>options && ('json' in options || 'lossy' in options) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$deserialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deserialize"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$serialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serialize"])(any, options)) : structuredClone(any) : (any, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$deserialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deserialize"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$serialize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serialize"])(any, options));
;
}}),
"[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Element} Element
 * @typedef {import('hast').Parents} Parents
 */ /**
 * @template Fn
 * @template Fallback
 * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate
 */ /**
 * @callback Check
 *   Check that an arbitrary value is an element.
 * @param {unknown} this
 *   Context object (`this`) to call `test` with
 * @param {unknown} [element]
 *   Anything (typically a node).
 * @param {number | null | undefined} [index]
 *   Position of `element` in its parent.
 * @param {Parents | null | undefined} [parent]
 *   Parent of `element`.
 * @returns {boolean}
 *   Whether this is an element and passes a test.
 *
 * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test
 *   Check for an arbitrary element.
 *
 *   * when `string`, checks that the element has that tag name
 *   * when `function`, see `TestFunction`
 *   * when `Array`, checks if one of the subtests pass
 *
 * @callback TestFunction
 *   Check if an element passes a test.
 * @param {unknown} this
 *   The given context.
 * @param {Element} element
 *   An element.
 * @param {number | undefined} [index]
 *   Position of `element` in its parent.
 * @param {Parents | undefined} [parent]
 *   Parent of `element`.
 * @returns {boolean | undefined | void}
 *   Whether this element passes the test.
 *
 *   Note: `void` is included until TS sees no return as `undefined`.
 */ /**
 * Check if `element` is an `Element` and whether it passes the given test.
 *
 * @param element
 *   Thing to check, typically `element`.
 * @param test
 *   Check for a specific element.
 * @param index
 *   Position of `element` in its parent.
 * @param parent
 *   Parent of `element`.
 * @param context
 *   Context object (`this`) to call `test` with.
 * @returns
 *   Whether `element` is an `Element` and passes a test.
 * @throws
 *   When an incorrect `test`, `index`, or `parent` is given; there is no error
 *   thrown when `element` is not a node or not an element.
 */ __turbopack_context__.s({
    "convertElement": (()=>convertElement),
    "isElement": (()=>isElement)
});
const isElement = /**
     * @param {unknown} [element]
     * @param {Test | undefined} [test]
     * @param {number | null | undefined} [index]
     * @param {Parents | null | undefined} [parent]
     * @param {unknown} [context]
     * @returns {boolean}
     */ // eslint-disable-next-line max-params
function(element, test, index, parent, context) {
    const check = convertElement(test);
    if (index !== null && index !== undefined && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {
        throw new Error('Expected positive finite `index`');
    }
    if (parent !== null && parent !== undefined && (!parent.type || !parent.children)) {
        throw new Error('Expected valid `parent`');
    }
    if ((index === null || index === undefined) !== (parent === null || parent === undefined)) {
        throw new Error('Expected both `index` and `parent`');
    }
    return looksLikeAnElement(element) ? check.call(context, element, index, parent) : false;
};
const convertElement = /**
     * @param {Test | null | undefined} [test]
     * @returns {Check}
     */ function(test) {
    if (test === null || test === undefined) {
        return element;
    }
    if (typeof test === 'string') {
        return tagNameFactory(test);
    }
    // Assume array.
    if (typeof test === 'object') {
        return anyFactory(test);
    }
    if (typeof test === 'function') {
        return castFactory(test);
    }
    throw new Error('Expected function, string, or array as `test`');
};
/**
 * Handle multiple tests.
 *
 * @param {Array<TestFunction | string>} tests
 * @returns {Check}
 */ function anyFactory(tests) {
    /** @type {Array<Check>} */ const checks = [];
    let index = -1;
    while(++index < tests.length){
        checks[index] = convertElement(tests[index]);
    }
    return castFactory(any);
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {TestFunction}
   */ function any(...parameters) {
        let index = -1;
        while(++index < checks.length){
            if (checks[index].apply(this, parameters)) return true;
        }
        return false;
    }
}
/**
 * Turn a string into a test for an element with a certain type.
 *
 * @param {string} check
 * @returns {Check}
 */ function tagNameFactory(check) {
    return castFactory(tagName);
    "TURBOPACK unreachable";
    /**
   * @param {Element} element
   * @returns {boolean}
   */ function tagName(element) {
        return element.tagName === check;
    }
}
/**
 * Turn a custom test into a test for an element that passes that test.
 *
 * @param {TestFunction} testFunction
 * @returns {Check}
 */ function castFactory(testFunction) {
    return check;
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {Check}
   */ function check(value, index, parent) {
        return Boolean(looksLikeAnElement(value) && testFunction.call(this, value, typeof index === 'number' ? index : undefined, parent || undefined));
    }
}
/**
 * Make sure something is an element.
 *
 * @param {unknown} element
 * @returns {element is Element}
 */ function element(element) {
    return Boolean(element && typeof element === 'object' && 'type' in element && element.type === 'element' && 'tagName' in element && typeof element.tagName === 'string');
}
/**
 * @param {unknown} value
 * @returns {value is Element}
 */ function looksLikeAnElement(value) {
    return value !== null && typeof value === 'object' && 'type' in value && 'tagName' in value;
}
}}),
"[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "embedded": (()=>embedded)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
;
const embedded = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])(/**
   * @param element
   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}
   */ function(element) {
    return element.tagName === 'audio' || element.tagName === 'canvas' || element.tagName === 'embed' || element.tagName === 'iframe' || element.tagName === 'img' || element.tagName === 'math' || element.tagName === 'object' || element.tagName === 'picture' || element.tagName === 'svg' || element.tagName === 'video';
});
}}),
"[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Nodes} Nodes
 */ // HTML whitespace expression.
// See <https://infra.spec.whatwg.org/#ascii-whitespace>.
__turbopack_context__.s({
    "whitespace": (()=>whitespace)
});
const re = /[ \t\n\f\r]/g;
function whitespace(thing) {
    return typeof thing === 'object' ? thing.type === 'text' ? empty(thing.value) : false : empty(thing);
}
/**
 * @param {string} value
 * @returns {boolean}
 */ function empty(value) {
    return value.replace(re, '') === '';
}
}}),
"[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} Node
 * @typedef {import('unist').Parent} Parent
 */ /**
 * @template Fn
 * @template Fallback
 * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate
 */ /**
 * @callback Check
 *   Check that an arbitrary value is a node.
 * @param {unknown} this
 *   The given context.
 * @param {unknown} [node]
 *   Anything (typically a node).
 * @param {number | null | undefined} [index]
 *   The node’s position in its parent.
 * @param {Parent | null | undefined} [parent]
 *   The node’s parent.
 * @returns {boolean}
 *   Whether this is a node and passes a test.
 *
 * @typedef {Record<string, unknown> | Node} Props
 *   Object to check for equivalence.
 *
 *   Note: `Node` is included as it is common but is not indexable.
 *
 * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test
 *   Check for an arbitrary node.
 *
 * @callback TestFunction
 *   Check if a node passes a test.
 * @param {unknown} this
 *   The given context.
 * @param {Node} node
 *   A node.
 * @param {number | undefined} [index]
 *   The node’s position in its parent.
 * @param {Parent | undefined} [parent]
 *   The node’s parent.
 * @returns {boolean | undefined | void}
 *   Whether this node passes the test.
 *
 *   Note: `void` is included until TS sees no return as `undefined`.
 */ /**
 * Check if `node` is a `Node` and whether it passes the given test.
 *
 * @param {unknown} node
 *   Thing to check, typically `Node`.
 * @param {Test} test
 *   A check for a specific node.
 * @param {number | null | undefined} index
 *   The node’s position in its parent.
 * @param {Parent | null | undefined} parent
 *   The node’s parent.
 * @param {unknown} context
 *   Context object (`this`) to pass to `test` functions.
 * @returns {boolean}
 *   Whether `node` is a node and passes a test.
 */ __turbopack_context__.s({
    "convert": (()=>convert),
    "is": (()=>is)
});
const is = /**
     * @param {unknown} [node]
     * @param {Test} [test]
     * @param {number | null | undefined} [index]
     * @param {Parent | null | undefined} [parent]
     * @param {unknown} [context]
     * @returns {boolean}
     */ // eslint-disable-next-line max-params
function(node, test, index, parent, context) {
    const check = convert(test);
    if (index !== undefined && index !== null && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {
        throw new Error('Expected positive finite index');
    }
    if (parent !== undefined && parent !== null && (!is(parent) || !parent.children)) {
        throw new Error('Expected parent node');
    }
    if ((parent === undefined || parent === null) !== (index === undefined || index === null)) {
        throw new Error('Expected both parent and index');
    }
    return looksLikeANode(node) ? check.call(context, node, index, parent) : false;
};
const convert = /**
     * @param {Test} [test]
     * @returns {Check}
     */ function(test) {
    if (test === null || test === undefined) {
        return ok;
    }
    if (typeof test === 'function') {
        return castFactory(test);
    }
    if (typeof test === 'object') {
        return Array.isArray(test) ? anyFactory(test) : propsFactory(test);
    }
    if (typeof test === 'string') {
        return typeFactory(test);
    }
    throw new Error('Expected function, string, or object as test');
};
/**
 * @param {Array<Props | TestFunction | string>} tests
 * @returns {Check}
 */ function anyFactory(tests) {
    /** @type {Array<Check>} */ const checks = [];
    let index = -1;
    while(++index < tests.length){
        checks[index] = convert(tests[index]);
    }
    return castFactory(any);
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {TestFunction}
   */ function any(...parameters) {
        let index = -1;
        while(++index < checks.length){
            if (checks[index].apply(this, parameters)) return true;
        }
        return false;
    }
}
/**
 * Turn an object into a test for a node with a certain fields.
 *
 * @param {Props} check
 * @returns {Check}
 */ function propsFactory(check) {
    const checkAsRecord = check;
    return castFactory(all);
    "TURBOPACK unreachable";
    /**
   * @param {Node} node
   * @returns {boolean}
   */ function all(node) {
        const nodeAsRecord = node;
        /** @type {string} */ let key;
        for(key in check){
            if (nodeAsRecord[key] !== checkAsRecord[key]) return false;
        }
        return true;
    }
}
/**
 * Turn a string into a test for a node with a certain type.
 *
 * @param {string} check
 * @returns {Check}
 */ function typeFactory(check) {
    return castFactory(type);
    "TURBOPACK unreachable";
    /**
   * @param {Node} node
   */ function type(node) {
        return node && node.type === check;
    }
}
/**
 * Turn a custom test into a test for a node that passes that test.
 *
 * @param {TestFunction} testFunction
 * @returns {Check}
 */ function castFactory(testFunction) {
    return check;
    "TURBOPACK unreachable";
    /**
   * @this {unknown}
   * @type {Check}
   */ function check(value, index, parent) {
        return Boolean(looksLikeANode(value) && testFunction.call(this, value, typeof index === 'number' ? index : undefined, parent || undefined));
    }
}
function ok() {
    return true;
}
/**
 * @param {unknown} value
 * @returns {value is Node}
 */ function looksLikeANode(value) {
    return value !== null && typeof value === 'object' && 'type' in value;
}
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/block.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>
__turbopack_context__.s({
    "blocks": (()=>blocks)
});
const blocks = [
    'address',
    'article',
    'aside',
    'blockquote',
    'body',
    'br',
    'caption',
    'center',
    'col',
    'colgroup',
    'dd',
    'dialog',
    'dir',
    'div',
    'dl',
    'dt',
    'figcaption',
    'figure',
    'footer',
    'form',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'head',
    'header',
    'hgroup',
    'hr',
    'html',
    'legend',
    'li',
    'li',
    'listing',
    'main',
    'menu',
    'nav',
    'ol',
    'optgroup',
    'option',
    'p',
    'plaintext',
    'pre',
    'section',
    'summary',
    'table',
    'tbody',
    'td',
    'td',
    'tfoot',
    'th',
    'th',
    'thead',
    'tr',
    'ul',
    'wbr',
    'xmp' // Flow content, legacy
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/content.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "content": (()=>content)
});
const content = [
    // Form.
    'button',
    'input',
    'select',
    'textarea'
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/skippable.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "skippable": (()=>skippable)
});
const skippable = [
    'area',
    'base',
    'basefont',
    'dialog',
    'datalist',
    'head',
    'link',
    'meta',
    'noembed',
    'noframes',
    'param',
    'rp',
    'script',
    'source',
    'style',
    'template',
    'track',
    'title'
];
}}),
"[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes, Parents, Text} from 'hast'
 */ /**
 * @callback Collapse
 *   Collapse a string.
 * @param {string} value
 *   Value to collapse.
 * @returns {string}
 *   Collapsed value.
 *
 * @typedef Options
 *   Configuration.
 * @property {boolean | null | undefined} [newlines=false]
 *   Collapse whitespace containing newlines to `'\n'` instead of `' '`
 *   (default: `false`); the default is to collapse to a single space.
 *
 * @typedef Result
 *   Result.
 * @property {boolean} remove
 *   Whether to remove.
 * @property {boolean} ignore
 *   Whether to ignore.
 * @property {boolean} stripAtStart
 *   Whether to strip at the start.
 *
 * @typedef State
 *   Info passed around.
 * @property {Collapse} collapse
 *   Collapse.
 * @property {Whitespace} whitespace
 *   Current whitespace.
 * @property {boolean | undefined} [before]
 *   Whether there is a break before (default: `false`).
 * @property {boolean | undefined} [after]
 *   Whether there is a break after (default: `false`).
 *
 * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace
 *   Whitespace setting.
 */ __turbopack_context__.s({
    "minifyWhitespace": (()=>minifyWhitespace)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$content$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/content.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$skippable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/skippable.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/** @type {Options} */ const emptyOptions = {};
const ignorableNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])([
    'comment',
    'doctype'
]);
function minifyWhitespace(tree, options) {
    const settings = options || emptyOptions;
    minify(tree, {
        collapse: collapseFactory(settings.newlines ? replaceNewlines : replaceWhitespace),
        whitespace: 'normal'
    });
}
/**
 * @param {Nodes} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function minify(node, state) {
    if ('children' in node) {
        const settings = {
            ...state
        };
        if (node.type === 'root' || blocklike(node)) {
            settings.before = true;
            settings.after = true;
        }
        settings.whitespace = inferWhiteSpace(node, state);
        return all(node, settings);
    }
    if (node.type === 'text') {
        if (state.whitespace === 'normal') {
            return minifyText(node, state);
        }
        // Naïve collapse, but no trimming:
        if (state.whitespace === 'nowrap') {
            node.value = state.collapse(node.value);
        }
    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor
    // trimmed.
    }
    return {
        ignore: ignorableNode(node),
        stripAtStart: false,
        remove: false
    };
}
/**
 * @param {Text} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function minifyText(node, state) {
    const value = state.collapse(node.value);
    const result = {
        ignore: false,
        stripAtStart: false,
        remove: false
    };
    let start = 0;
    let end = value.length;
    if (state.before && removable(value.charAt(0))) {
        start++;
    }
    if (start !== end && removable(value.charAt(end - 1))) {
        if (state.after) {
            end--;
        } else {
            result.stripAtStart = true;
        }
    }
    if (start === end) {
        result.remove = true;
    } else {
        node.value = value.slice(start, end);
    }
    return result;
}
/**
 * @param {Parents} parent
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Result}
 *   Result.
 */ function all(parent, state) {
    let before = state.before;
    const after = state.after;
    const children = parent.children;
    let length = children.length;
    let index = -1;
    while(++index < length){
        const result = minify(children[index], {
            ...state,
            after: collapsableAfter(children, index, after),
            before
        });
        if (result.remove) {
            children.splice(index, 1);
            index--;
            length--;
        } else if (!result.ignore) {
            before = result.stripAtStart;
        }
        // If this element, such as a `<select>` or `<img>`, contributes content
        // somehow, allow whitespace again.
        if (content(children[index])) {
            before = false;
        }
    }
    return {
        ignore: false,
        stripAtStart: Boolean(before || after),
        remove: false
    };
}
/**
 * @param {Array<Nodes>} nodes
 *   Nodes.
 * @param {number} index
 *   Index.
 * @param {boolean | undefined} [after]
 *   Whether there is a break after `nodes` (default: `false`).
 * @returns {boolean | undefined}
 *   Whether there is a break after the node at `index`.
 */ function collapsableAfter(nodes, index, after) {
    while(++index < nodes.length){
        const node = nodes[index];
        let result = inferBoundary(node);
        if (result === undefined && 'children' in node && !skippable(node)) {
            result = collapsableAfter(node.children, -1);
        }
        if (typeof result === 'boolean') {
            return result;
        }
    }
    return after;
}
/**
 * Infer two types of boundaries:
 *
 * 1. `true` — boundary for which whitespace around it does not contribute
 *    anything
 * 2. `false` — boundary for which whitespace around it *does* contribute
 *
 * No result (`undefined`) is returned if it is unknown.
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean | undefined}
 *   Boundary.
 */ function inferBoundary(node) {
    if (node.type === 'element') {
        if (content(node)) {
            return false;
        }
        if (blocklike(node)) {
            return true;
        }
    // Unknown: either depends on siblings if embedded or metadata, or on
    // children.
    } else if (node.type === 'text') {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(node)) {
            return false;
        }
    } else if (!ignorableNode(node)) {
        return false;
    }
}
/**
 * Infer whether a node is skippable.
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is skippable.
 */ function content(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["embedded"])(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$content$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["content"]);
}
/**
 * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>
 *
 * @param {Nodes} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is block-like.
 */ function blocklike(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["blocks"]);
}
/**
 * @param {Parents} node
 *   Node.
 * @returns {boolean}
 *   Whether `node` is skippable.
 */ function skippable(node) {
    return Boolean(node.type === 'element' && node.properties.hidden) || ignorableNode(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isElement"])(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$skippable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["skippable"]);
}
/**
 * @param {string} character
 *   Character.
 * @returns {boolean}
 *   Whether `character` is removable.
 */ function removable(character) {
    return character === ' ' || character === '\n';
}
/**
 * @type {Collapse}
 */ function replaceNewlines(value) {
    const match = /\r?\n|\r/.exec(value);
    return match ? match[0] : ' ';
}
/**
 * @type {Collapse}
 */ function replaceWhitespace() {
    return ' ';
}
/**
 * @param {Collapse} replace
 * @returns {Collapse}
 *   Collapse.
 */ function collapseFactory(replace) {
    return collapse;
    "TURBOPACK unreachable";
    /**
   * @type {Collapse}
   */ function collapse(value) {
        return String(value).replace(/[\t\n\v\f\r ]+/g, replace);
    }
}
/**
 * We don’t need to support void elements here (so `nobr wbr` -> `normal` is
 * ignored).
 *
 * @param {Parents} node
 *   Node.
 * @param {State} state
 *   Info passed around.
 * @returns {Whitespace}
 *   Whitespace.
 */ function inferWhiteSpace(node, state) {
    if ('tagName' in node && node.properties) {
        switch(node.tagName){
            // Whitespace in script/style, while not displayed by CSS as significant,
            // could have some meaning in JS/CSS, so we can’t touch them.
            case 'listing':
            case 'plaintext':
            case 'script':
            case 'style':
            case 'xmp':
                {
                    return 'pre';
                }
            case 'nobr':
                {
                    return 'nowrap';
                }
            case 'pre':
                {
                    return node.properties.wrap ? 'pre-wrap' : 'pre';
                }
            case 'td':
            case 'th':
                {
                    return node.properties.noWrap ? 'nowrap' : state.whitespace;
                }
            case 'textarea':
                {
                    return 'pre-wrap';
                }
            default:
        }
    }
    return state.whitespace;
}
}}),
"[project]/node_modules/.pnpm/rehype-minify-whitespace@6.0.2/node_modules/rehype-minify-whitespace/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Options} from 'hast-util-minify-whitespace'
 * @import {Root} from 'hast'
 */ __turbopack_context__.s({
    "default": (()=>rehypeMinifyWhitespace)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-minify-whitespace@1.0.1/node_modules/hast-util-minify-whitespace/lib/index.js [app-ssr] (ecmascript)");
;
function rehypeMinifyWhitespace(options) {
    /**
   * @param {Root} tree
   *   Tree.
   * @returns {undefined}
   *   Nothing.
   */ return function(tree) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$minify$2d$whitespace$40$1$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["minifyWhitespace"])(tree, options);
    };
}
}}),
"[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/color.node.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @param {string} d
 * @returns {string}
 */ __turbopack_context__.s({
    "color": (()=>color)
});
function color(d) {
    return '\u001B[33m' + d + '\u001B[39m';
}
}}),
"[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} UnistNode
 * @typedef {import('unist').Parent} UnistParent
 */ /**
 * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test
 *   Test from `unist-util-is`.
 *
 *   Note: we have remove and add `undefined`, because otherwise when generating
 *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,
 *   which doesn’t work when publishing on npm.
 */ /**
 * @typedef {(
 *   Fn extends (value: any) => value is infer Thing
 *   ? Thing
 *   : Fallback
 * )} Predicate
 *   Get the value of a type guard `Fn`.
 * @template Fn
 *   Value; typically function that is a type guard (such as `(x): x is Y`).
 * @template Fallback
 *   Value to yield if `Fn` is not a type guard.
 */ /**
 * @typedef {(
 *   Check extends null | undefined // No test.
 *   ? Value
 *   : Value extends {type: Check} // String (type) test.
 *   ? Value
 *   : Value extends Check // Partial test.
 *   ? Value
 *   : Check extends Function // Function test.
 *   ? Predicate<Check, Value> extends Value
 *     ? Predicate<Check, Value>
 *     : never
 *   : never // Some other test?
 * )} MatchesOne
 *   Check whether a node matches a primitive check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test, but not arrays.
 */ /**
 * @typedef {(
 *   Check extends Array<any>
 *   ? MatchesOne<Value, Check[keyof Check]>
 *   : MatchesOne<Value, Check>
 * )} Matches
 *   Check whether a node matches a check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test.
 */ /**
 * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint
 *   Number; capped reasonably.
 */ /**
 * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment
 *   Increment a number in the type system.
 * @template {Uint} [I=0]
 *   Index.
 */ /**
 * @typedef {(
 *   Node extends UnistParent
 *   ? Node extends {children: Array<infer Children>}
 *     ? Child extends Children ? Node : never
 *     : never
 *   : never
 * )} InternalParent
 *   Collect nodes that can be parents of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent
 *   Collect nodes in `Tree` that can be parents of `Child`.
 * @template {UnistNode} Tree
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {(
 *   Depth extends Max
 *   ? never
 *   :
 *     | InternalParent<Node, Child>
 *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>
 * )} InternalAncestor
 *   Collect nodes in `Tree` that can be ancestors of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor
 *   Collect nodes in `Tree` that can be ancestors of `Child`.
 * @template {UnistNode} Tree
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {(
 *   Tree extends UnistParent
 *     ? Depth extends Max
 *       ? Tree
 *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>
 *     : Tree
 * )} InclusiveDescendant
 *   Collect all (inclusive) descendants of `Tree`.
 *
 *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to
 *   > recurse without actually running into an infinite loop, which the
 *   > previous version did.
 *   >
 *   > Practically, a max of `2` is typically enough assuming a `Root` is
 *   > passed, but it doesn’t improve performance.
 *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.
 *   > Using up to `10` doesn’t hurt or help either.
 * @template {UnistNode} Tree
 *   Tree type.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @typedef {'skip' | boolean} Action
 *   Union of the action types.
 *
 * @typedef {number} Index
 *   Move to the sibling at `index` next (after node itself is completely
 *   traversed).
 *
 *   Useful if mutating the tree, such as removing the node the visitor is
 *   currently on, or any of its previous siblings.
 *   Results less than 0 or greater than or equal to `children.length` stop
 *   traversing the parent.
 *
 * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple
 *   List with one or two values, the first an action, the second an index.
 *
 * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult
 *   Any value that can be returned from a visitor.
 */ /**
 * @callback Visitor
 *   Handle a node (matching `test`, if given).
 *
 *   Visitors are free to transform `node`.
 *   They can also transform the parent of node (the last of `ancestors`).
 *
 *   Replacing `node` itself, if `SKIP` is not returned, still causes its
 *   descendants to be walked (which is a bug).
 *
 *   When adding or removing previous siblings of `node` (or next siblings, in
 *   case of reverse), the `Visitor` should return a new `Index` to specify the
 *   sibling to traverse after `node` is traversed.
 *   Adding or removing next siblings of `node` (or previous siblings, in case
 *   of reverse) is handled as expected without needing to return a new `Index`.
 *
 *   Removing the children property of an ancestor still results in them being
 *   traversed.
 * @param {Visited} node
 *   Found node.
 * @param {Array<VisitedParents>} ancestors
 *   Ancestors of `node`.
 * @returns {VisitorResult}
 *   What to do next.
 *
 *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.
 *   An `Action` is treated as a tuple of `[Action]`.
 *
 *   Passing a tuple back only makes sense if the `Action` is `SKIP`.
 *   When the `Action` is `EXIT`, that action can be returned.
 *   When the `Action` is `CONTINUE`, `Index` can be returned.
 * @template {UnistNode} [Visited=UnistNode]
 *   Visited node type.
 * @template {UnistParent} [VisitedParents=UnistParent]
 *   Ancestor type.
 */ /**
 * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor
 *   Build a typed `Visitor` function from a tree and a test.
 *
 *   It will infer which values are passed as `node` and which as `parents`.
 * @template {UnistNode} [Tree=UnistNode]
 *   Tree type.
 * @template {Test} [Check=Test]
 *   Test type.
 */ __turbopack_context__.s({
    "CONTINUE": (()=>CONTINUE),
    "EXIT": (()=>EXIT),
    "SKIP": (()=>SKIP),
    "visitParents": (()=>visitParents)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$color$2e$node$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/color.node.js [app-ssr] (ecmascript)");
;
;
/** @type {Readonly<ActionTuple>} */ const empty = [];
const CONTINUE = true;
const EXIT = false;
const SKIP = 'skip';
function visitParents(tree, test, visitor, reverse) {
    /** @type {Test} */ let check;
    if (typeof test === 'function' && typeof visitor !== 'function') {
        reverse = visitor;
        // @ts-expect-error no visitor given, so `visitor` is test.
        visitor = test;
    } else {
        // @ts-expect-error visitor given, so `test` isn’t a visitor.
        check = test;
    }
    const is = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])(check);
    const step = reverse ? -1 : 1;
    factory(tree, undefined, [])();
    /**
   * @param {UnistNode} node
   * @param {number | undefined} index
   * @param {Array<UnistParent>} parents
   */ function factory(node, index, parents) {
        const value = node && typeof node === 'object' ? node : {};
        if (typeof value.type === 'string') {
            const name = // `hast`
            typeof value.tagName === 'string' ? value.tagName : typeof value.name === 'string' ? value.name : undefined;
            Object.defineProperty(visit, 'name', {
                value: 'node (' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$color$2e$node$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["color"])(node.type + (name ? '<' + name + '>' : '')) + ')'
            });
        }
        return visit;
        "TURBOPACK unreachable";
        function visit() {
            /** @type {Readonly<ActionTuple>} */ let result = empty;
            /** @type {Readonly<ActionTuple>} */ let subresult;
            /** @type {number} */ let offset;
            /** @type {Array<UnistParent>} */ let grandparents;
            if (!test || is(node, index, parents[parents.length - 1] || undefined)) {
                // @ts-expect-error: `visitor` is now a visitor.
                result = toResult(visitor(node, parents));
                if (result[0] === EXIT) {
                    return result;
                }
            }
            if ('children' in node && node.children) {
                const nodeAsParent = node;
                if (nodeAsParent.children && result[0] !== SKIP) {
                    offset = (reverse ? nodeAsParent.children.length : -1) + step;
                    grandparents = parents.concat(nodeAsParent);
                    while(offset > -1 && offset < nodeAsParent.children.length){
                        const child = nodeAsParent.children[offset];
                        subresult = factory(child, offset, grandparents)();
                        if (subresult[0] === EXIT) {
                            return subresult;
                        }
                        offset = typeof subresult[1] === 'number' ? subresult[1] : offset + step;
                    }
                }
            }
            return result;
        }
    }
}
/**
 * Turn a return value into a clean result.
 *
 * @param {VisitorResult} value
 *   Valid return values from visitors.
 * @returns {Readonly<ActionTuple>}
 *   Clean result.
 */ function toResult(value) {
    if (Array.isArray(value)) {
        return value;
    }
    if (typeof value === 'number') {
        return [
            CONTINUE,
            value
        ];
    }
    return value === null || value === undefined ? empty : [
        value
    ];
}
}}),
"[project]/node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} UnistNode
 * @typedef {import('unist').Parent} UnistParent
 * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult
 */ /**
 * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test
 *   Test from `unist-util-is`.
 *
 *   Note: we have remove and add `undefined`, because otherwise when generating
 *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,
 *   which doesn’t work when publishing on npm.
 */ // To do: use types from `unist-util-visit-parents` when it’s released.
/**
 * @typedef {(
 *   Fn extends (value: any) => value is infer Thing
 *   ? Thing
 *   : Fallback
 * )} Predicate
 *   Get the value of a type guard `Fn`.
 * @template Fn
 *   Value; typically function that is a type guard (such as `(x): x is Y`).
 * @template Fallback
 *   Value to yield if `Fn` is not a type guard.
 */ /**
 * @typedef {(
 *   Check extends null | undefined // No test.
 *   ? Value
 *   : Value extends {type: Check} // String (type) test.
 *   ? Value
 *   : Value extends Check // Partial test.
 *   ? Value
 *   : Check extends Function // Function test.
 *   ? Predicate<Check, Value> extends Value
 *     ? Predicate<Check, Value>
 *     : never
 *   : never // Some other test?
 * )} MatchesOne
 *   Check whether a node matches a primitive check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test, but not arrays.
 */ /**
 * @typedef {(
 *   Check extends Array<any>
 *   ? MatchesOne<Value, Check[keyof Check]>
 *   : MatchesOne<Value, Check>
 * )} Matches
 *   Check whether a node matches a check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test.
 */ /**
 * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint
 *   Number; capped reasonably.
 */ /**
 * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment
 *   Increment a number in the type system.
 * @template {Uint} [I=0]
 *   Index.
 */ /**
 * @typedef {(
 *   Node extends UnistParent
 *   ? Node extends {children: Array<infer Children>}
 *     ? Child extends Children ? Node : never
 *     : never
 *   : never
 * )} InternalParent
 *   Collect nodes that can be parents of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent
 *   Collect nodes in `Tree` that can be parents of `Child`.
 * @template {UnistNode} Tree
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 */ /**
 * @typedef {(
 *   Depth extends Max
 *   ? never
 *   :
 *     | InternalParent<Node, Child>
 *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>
 * )} InternalAncestor
 *   Collect nodes in `Tree` that can be ancestors of `Child`.
 * @template {UnistNode} Node
 *   All node types in a tree.
 * @template {UnistNode} Child
 *   Node to search for.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @typedef {(
 *   Tree extends UnistParent
 *     ? Depth extends Max
 *       ? Tree
 *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>
 *     : Tree
 * )} InclusiveDescendant
 *   Collect all (inclusive) descendants of `Tree`.
 *
 *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to
 *   > recurse without actually running into an infinite loop, which the
 *   > previous version did.
 *   >
 *   > Practically, a max of `2` is typically enough assuming a `Root` is
 *   > passed, but it doesn’t improve performance.
 *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.
 *   > Using up to `10` doesn’t hurt or help either.
 * @template {UnistNode} Tree
 *   Tree type.
 * @template {Uint} [Max=10]
 *   Max; searches up to this depth.
 * @template {Uint} [Depth=0]
 *   Current depth.
 */ /**
 * @callback Visitor
 *   Handle a node (matching `test`, if given).
 *
 *   Visitors are free to transform `node`.
 *   They can also transform `parent`.
 *
 *   Replacing `node` itself, if `SKIP` is not returned, still causes its
 *   descendants to be walked (which is a bug).
 *
 *   When adding or removing previous siblings of `node` (or next siblings, in
 *   case of reverse), the `Visitor` should return a new `Index` to specify the
 *   sibling to traverse after `node` is traversed.
 *   Adding or removing next siblings of `node` (or previous siblings, in case
 *   of reverse) is handled as expected without needing to return a new `Index`.
 *
 *   Removing the children property of `parent` still results in them being
 *   traversed.
 * @param {Visited} node
 *   Found node.
 * @param {Visited extends UnistNode ? number | undefined : never} index
 *   Index of `node` in `parent`.
 * @param {Ancestor extends UnistParent ? Ancestor | undefined : never} parent
 *   Parent of `node`.
 * @returns {VisitorResult}
 *   What to do next.
 *
 *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.
 *   An `Action` is treated as a tuple of `[Action]`.
 *
 *   Passing a tuple back only makes sense if the `Action` is `SKIP`.
 *   When the `Action` is `EXIT`, that action can be returned.
 *   When the `Action` is `CONTINUE`, `Index` can be returned.
 * @template {UnistNode} [Visited=UnistNode]
 *   Visited node type.
 * @template {UnistParent} [Ancestor=UnistParent]
 *   Ancestor type.
 */ /**
 * @typedef {Visitor<Visited, Parent<Ancestor, Visited>>} BuildVisitorFromMatch
 *   Build a typed `Visitor` function from a node and all possible parents.
 *
 *   It will infer which values are passed as `node` and which as `parent`.
 * @template {UnistNode} Visited
 *   Node type.
 * @template {UnistParent} Ancestor
 *   Parent type.
 */ /**
 * @typedef {(
 *   BuildVisitorFromMatch<
 *     Matches<Descendant, Check>,
 *     Extract<Descendant, UnistParent>
 *   >
 * )} BuildVisitorFromDescendants
 *   Build a typed `Visitor` function from a list of descendants and a test.
 *
 *   It will infer which values are passed as `node` and which as `parent`.
 * @template {UnistNode} Descendant
 *   Node type.
 * @template {Test} Check
 *   Test type.
 */ /**
 * @typedef {(
 *   BuildVisitorFromDescendants<
 *     InclusiveDescendant<Tree>,
 *     Check
 *   >
 * )} BuildVisitor
 *   Build a typed `Visitor` function from a tree and a test.
 *
 *   It will infer which values are passed as `node` and which as `parent`.
 * @template {UnistNode} [Tree=UnistNode]
 *   Node type.
 * @template {Test} [Check=Test]
 *   Test type.
 */ __turbopack_context__.s({
    "visit": (()=>visit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)");
;
;
function visit(tree, testOrVisitor, visitorOrReverse, maybeReverse) {
    /** @type {boolean | null | undefined} */ let reverse;
    /** @type {Test} */ let test;
    /** @type {Visitor} */ let visitor;
    if (typeof testOrVisitor === 'function' && typeof visitorOrReverse !== 'function') {
        test = undefined;
        visitor = testOrVisitor;
        reverse = visitorOrReverse;
    } else {
        // @ts-expect-error: assume the overload with test was given.
        test = testOrVisitor;
        // @ts-expect-error: assume the overload with test was given.
        visitor = visitorOrReverse;
        reverse = maybeReverse;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["visitParents"])(tree, test, overload, reverse);
    /**
   * @param {UnistNode} node
   * @param {Array<UnistParent>} parents
   */ function overload(node, parents) {
        const parent = parents[parents.length - 1];
        const index = parent ? parent.children.indexOf(node) : undefined;
        return visitor(node, index, parent);
    }
}
}}),
"[project]/node_modules/.pnpm/unist-util-position@5.0.0/node_modules/unist-util-position/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} Node
 * @typedef {import('unist').Point} Point
 * @typedef {import('unist').Position} Position
 */ /**
 * @typedef NodeLike
 * @property {string} type
 * @property {PositionLike | null | undefined} [position]
 *
 * @typedef PositionLike
 * @property {PointLike | null | undefined} [start]
 * @property {PointLike | null | undefined} [end]
 *
 * @typedef PointLike
 * @property {number | null | undefined} [line]
 * @property {number | null | undefined} [column]
 * @property {number | null | undefined} [offset]
 */ /**
 * Get the ending point of `node`.
 *
 * @param node
 *   Node.
 * @returns
 *   Point.
 */ __turbopack_context__.s({
    "pointEnd": (()=>pointEnd),
    "pointStart": (()=>pointStart),
    "position": (()=>position)
});
const pointEnd = point('end');
const pointStart = point('start');
/**
 * Get the positional info of `node`.
 *
 * @param {'end' | 'start'} type
 *   Side.
 * @returns
 *   Getter.
 */ function point(type) {
    return point;
    "TURBOPACK unreachable";
    /**
   * Get the point info of `node` at a bound side.
   *
   * @param {Node | NodeLike | null | undefined} [node]
   * @returns {Point | undefined}
   */ function point(node) {
        const point = node && node.position && node.position[type] || {};
        if (typeof point.line === 'number' && point.line > 0 && typeof point.column === 'number' && point.column > 0) {
            return {
                line: point.line,
                column: point.column,
                offset: typeof point.offset === 'number' && point.offset > -1 ? point.offset : undefined
            };
        }
    }
}
function position(node) {
    const start = pointStart(node);
    const end = pointEnd(node);
    if (start && end) {
        return {
            start,
            end
        };
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/a.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Link, PhrasingContent} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Link}
 *   mdast node.
 */ __turbopack_context__.s({
    "a": (()=>a)
});
function a(state, node) {
    const properties = node.properties || {};
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = state.all(node);
    /** @type {Link} */ const result = {
        type: 'link',
        url: state.resolve(String(properties.href || '') || null),
        title: properties.title ? String(properties.title) : null,
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/base.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {undefined}
 *   Nothing.
 */ __turbopack_context__.s({
    "base": (()=>base)
});
function base(state, node) {
    if (!state.baseFound) {
        state.frozenBaseUrl = String(node.properties && node.properties.href || '') || undefined;
        state.baseFound = true;
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/blockquote.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Blockquote} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Blockquote}
 *   mdast node.
 */ __turbopack_context__.s({
    "blockquote": (()=>blockquote)
});
function blockquote(state, node) {
    /** @type {Blockquote} */ const result = {
        type: 'blockquote',
        children: state.toFlow(state.all(node))
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/br.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Break} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Break}
 *   mdast node.
 */ __turbopack_context__.s({
    "br": (()=>br)
});
function br(state, node) {
    /** @type {Break} */ const result = {
        type: 'break'
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/unist-util-find-after@5.0.0/node_modules/unist-util-find-after/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('unist').Node} UnistNode
 * @typedef {import('unist').Parent} UnistParent
 */ /**
 * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test
 *   Test from `unist-util-is`.
 *
 *   Note: we have remove and add `undefined`, because otherwise when generating
 *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,
 *   which doesn’t work when publishing on npm.
 */ /**
 * @typedef {(
 *   Fn extends (value: any) => value is infer Thing
 *   ? Thing
 *   : Fallback
 * )} Predicate
 *   Get the value of a type guard `Fn`.
 * @template Fn
 *   Value; typically function that is a type guard (such as `(x): x is Y`).
 * @template Fallback
 *   Value to yield if `Fn` is not a type guard.
 */ /**
 * @typedef {(
 *   Check extends null | undefined // No test.
 *   ? Value
 *   : Value extends {type: Check} // String (type) test.
 *   ? Value
 *   : Value extends Check // Partial test.
 *   ? Value
 *   : Check extends Function // Function test.
 *   ? Predicate<Check, Value> extends Value
 *     ? Predicate<Check, Value>
 *     : never
 *   : never // Some other test?
 * )} MatchesOne
 *   Check whether a node matches a primitive check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test, but not arrays.
 */ /**
 * @typedef {(
 *   Check extends Array<any>
 *   ? MatchesOne<Value, Check[keyof Check]>
 *   : MatchesOne<Value, Check>
 * )} Matches
 *   Check whether a node matches a check in the type system.
 * @template Value
 *   Value; typically unist `Node`.
 * @template Check
 *   Value; typically `unist-util-is`-compatible test.
 */ /**
 * @typedef {(
 *   Kind extends {children: Array<infer Child>}
 *   ? Child
 *   : never
 * )} Child
 *   Collect nodes that can be parents of `Child`.
 * @template {UnistNode} Kind
 *   All node types.
 */ __turbopack_context__.s({
    "findAfter": (()=>findAfter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
;
const findAfter = /**
     * @param {UnistParent} parent
     * @param {UnistNode | number} index
     * @param {Test} [test]
     * @returns {UnistNode | undefined}
     */ function(parent, index, test) {
    const is = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])(test);
    if (!parent || !parent.type || !parent.children) {
        throw new Error('Expected parent node');
    }
    if (typeof index === 'number') {
        if (index < 0 || index === Number.POSITIVE_INFINITY) {
            throw new Error('Expected positive finite number as index');
        }
    } else {
        index = parent.children.indexOf(index);
        if (index < 0) {
            throw new Error('Expected child node or index');
        }
    }
    while(++index < parent.children.length){
        if (is(parent.children[index], index, parent)) {
            return parent.children[index];
        }
    }
    return undefined;
};
}}),
"[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Comment} Comment
 * @typedef {import('hast').Element} Element
 * @typedef {import('hast').Nodes} Nodes
 * @typedef {import('hast').Parents} Parents
 * @typedef {import('hast').Text} Text
 * @typedef {import('hast-util-is-element').TestFunction} TestFunction
 */ /**
 * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace
 *   Valid and useful whitespace values (from CSS).
 *
 * @typedef {0 | 1 | 2} BreakNumber
 *   Specific break:
 *
 *   *   `0` — space
 *   *   `1` — line ending
 *   *   `2` — blank line
 *
 * @typedef {'\n'} BreakForce
 *   Forced break.
 *
 * @typedef {boolean} BreakValue
 *   Whether there was a break.
 *
 * @typedef {BreakNumber | BreakValue | undefined} BreakBefore
 *   Any value for a break before.
 *
 * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter
 *   Any value for a break after.
 *
 * @typedef CollectionInfo
 *   Info on current collection.
 * @property {BreakAfter} breakAfter
 *   Whether there was a break after.
 * @property {BreakBefore} breakBefore
 *   Whether there was a break before.
 * @property {Whitespace} whitespace
 *   Current whitespace setting.
 *
 * @typedef Options
 *   Configuration.
 * @property {Whitespace | null | undefined} [whitespace='normal']
 *   Initial CSS whitespace setting to use (default: `'normal'`).
 */ __turbopack_context__.s({
    "toText": (()=>toText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$find$2d$after$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$find$2d$after$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-find-after@5.0.0/node_modules/unist-util-find-after/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
;
;
const searchLineFeeds = /\n/g;
const searchTabOrSpaces = /[\t ]+/g;
const br = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])('br');
const cell = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])(isCell);
const p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])('p');
const row = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])('tr');
// Note that we don’t need to include void elements here as they don’t have text.
// See: <https://github.com/wooorm/html-void-elements>
const notRendered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])([
    // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>
    'datalist',
    'head',
    'noembed',
    'noframes',
    'noscript',
    'rp',
    'script',
    'style',
    'template',
    'title',
    // Hidden attribute.
    hidden,
    // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>
    closedDialog
]);
// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>
const blockOrCaption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])([
    'address',
    'article',
    'aside',
    'blockquote',
    'body',
    'caption',
    'center',
    'dd',
    'dialog',
    'dir',
    'dl',
    'dt',
    'div',
    'figure',
    'figcaption',
    'footer',
    'form,',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'header',
    'hgroup',
    'hr',
    'html',
    'legend',
    'li',
    'listing',
    'main',
    'menu',
    'nav',
    'ol',
    'p',
    'plaintext',
    'pre',
    'section',
    'ul',
    'xmp' // Flow content (legacy)
]);
function toText(tree, options) {
    const options_ = options || {};
    const children = 'children' in tree ? tree.children : [];
    const block = blockOrCaption(tree);
    const whitespace = inferWhitespace(tree, {
        whitespace: options_.whitespace || 'normal',
        breakBefore: false,
        breakAfter: false
    });
    /** @type {Array<BreakNumber | string>} */ const results = [];
    // Treat `text` and `comment` as having normal white-space.
    // This deviates from the spec as in the DOM the node’s `.data` has to be
    // returned.
    // If you want that behavior use `hast-util-to-string`.
    // All other nodes are later handled as if they are `element`s (so the
    // algorithm also works on a `root`).
    // Nodes without children are treated as a void element, so `doctype` is thus
    // ignored.
    if (tree.type === 'text' || tree.type === 'comment') {
        results.push(...collectText(tree, {
            whitespace,
            breakBefore: true,
            breakAfter: true
        }));
    }
    // 1.  If this element is not being rendered, or if the user agent is a
    //     non-CSS user agent, then return the same value as the textContent IDL
    //     attribute on this element.
    //
    //     Note: we’re not supporting stylesheets so we’re acting as if the node
    //     is rendered.
    //
    //     If you want that behavior use `hast-util-to-string`.
    //     Important: we’ll have to account for this later though.
    // 2.  Let results be a new empty list.
    let index = -1;
    // 3.  For each child node node of this element:
    while(++index < children.length){
        // 3.1. Let current be the list resulting in running the inner text
        //      collection steps with node.
        //      Each item in results will either be a JavaScript string or a
        //      positive integer (a required line break count).
        // 3.2. For each item item in current, append item to results.
        results.push(...renderedTextCollection(children[index], // @ts-expect-error: `tree` is a parent if we’re here.
        tree, {
            whitespace,
            breakBefore: index ? undefined : block,
            breakAfter: index < children.length - 1 ? br(children[index + 1]) : block
        }));
    }
    // 4.  Remove any items from results that are the empty string.
    // 5.  Remove any runs of consecutive required line break count items at the
    //     start or end of results.
    // 6.  Replace each remaining run of consecutive required line break count
    //     items with a string consisting of as many U+000A LINE FEED (LF)
    //     characters as the maximum of the values in the required line break
    //     count items.
    /** @type {Array<string>} */ const result = [];
    /** @type {number | undefined} */ let count;
    index = -1;
    while(++index < results.length){
        const value = results[index];
        if (typeof value === 'number') {
            if (count !== undefined && value > count) count = value;
        } else if (value) {
            if (count !== undefined && count > -1) {
                result.push('\n'.repeat(count) || ' ');
            }
            count = -1;
            result.push(value);
        }
    }
    // 7.  Return the concatenation of the string items in results.
    return result.join('');
}
/**
 * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>
 *
 * @param {Nodes} node
 * @param {Parents} parent
 * @param {CollectionInfo} info
 * @returns {Array<BreakNumber | string>}
 */ function renderedTextCollection(node, parent, info) {
    if (node.type === 'element') {
        return collectElement(node, parent, info);
    }
    if (node.type === 'text') {
        return info.whitespace === 'normal' ? collectText(node, info) : collectPreText(node);
    }
    return [];
}
/**
 * Collect an element.
 *
 * @param {Element} node
 *   Element node.
 * @param {Parents} parent
 * @param {CollectionInfo} info
 *   Info on current collection.
 * @returns {Array<BreakNumber | string>}
 */ function collectElement(node, parent, info) {
    // First we infer the `white-space` property.
    const whitespace = inferWhitespace(node, info);
    const children = node.children || [];
    let index = -1;
    /** @type {Array<BreakNumber | string>} */ let items = [];
    // We’re ignoring point 3, and exiting without any content here, because we
    // deviated from the spec in `toText` at step 3.
    if (notRendered(node)) {
        return items;
    }
    /** @type {BreakNumber | undefined} */ let prefix;
    /** @type {BreakForce | BreakNumber | undefined} */ let suffix;
    // Note: we first detect if there is going to be a break before or after the
    // contents, as that changes the white-space handling.
    // 2.  If node’s computed value of `visibility` is not `visible`, then return
    //     items.
    //
    //     Note: Ignored, as everything is visible by default user agent styles.
    // 3.  If node is not being rendered, then return items. [...]
    //
    //     Note: We already did this above.
    // See `collectText` for step 4.
    // 5.  If node is a `<br>` element, then append a string containing a single
    //     U+000A LINE FEED (LF) character to items.
    if (br(node)) {
        suffix = '\n';
    } else if (row(node) && // @ts-expect-error: something up with types of parents.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$find$2d$after$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$find$2d$after$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findAfter"])(parent, node, row)) {
        suffix = '\n';
    } else if (p(node)) {
        prefix = 2;
        suffix = 2;
    } else if (blockOrCaption(node)) {
        prefix = 1;
        suffix = 1;
    }
    // 1.  Let items be the result of running the inner text collection steps with
    //     each child node of node in tree order, and then concatenating the
    //     results to a single list.
    while(++index < children.length){
        items = items.concat(renderedTextCollection(children[index], node, {
            whitespace,
            breakBefore: index ? undefined : prefix,
            breakAfter: index < children.length - 1 ? br(children[index + 1]) : suffix
        }));
    }
    // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS
    //     box is not the last `table-cell` box of its enclosing `table-row` box,
    //     then append a string containing a single U+0009 CHARACTER TABULATION
    //     (tab) character to items.
    //
    //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>
    if (cell(node) && // @ts-expect-error: something up with types of parents.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$find$2d$after$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$find$2d$after$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findAfter"])(parent, node, cell)) {
        items.push('\t');
    }
    // Add the pre- and suffix.
    if (prefix) items.unshift(prefix);
    if (suffix) items.push(suffix);
    return items;
}
/**
 * 4.  If node is a Text node, then for each CSS text box produced by node,
 *     in content order, compute the text of the box after application of the
 *     CSS `white-space` processing rules and `text-transform` rules, set
 *     items to the list of the resulting strings, and return items.
 *     The CSS `white-space` processing rules are slightly modified:
 *     collapsible spaces at the end of lines are always collapsed, but they
 *     are only removed if the line is the last line of the block, or it ends
 *     with a br element.
 *     Soft hyphens should be preserved.
 *
 *     Note: See `collectText` and `collectPreText`.
 *     Note: we don’t deal with `text-transform`, no element has that by
 *     default.
 *
 * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>
 *
 * @param {Comment | Text} node
 *   Text node.
 * @param {CollectionInfo} info
 *   Info on current collection.
 * @returns {Array<BreakNumber | string>}
 *   Result.
 */ function collectText(node, info) {
    const value = String(node.value);
    /** @type {Array<string>} */ const lines = [];
    /** @type {Array<BreakNumber | string>} */ const result = [];
    let start = 0;
    while(start <= value.length){
        searchLineFeeds.lastIndex = start;
        const match = searchLineFeeds.exec(value);
        const end = match && 'index' in match ? match.index : value.length;
        lines.push(// Any sequence of collapsible spaces and tabs immediately preceding or
        // following a segment break is removed.
        trimAndCollapseSpacesAndTabs(// […] ignoring bidi formatting characters (characters with the
        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if
        // they were not there.
        value.slice(start, end).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g, ''), start === 0 ? info.breakBefore : true, end === value.length ? info.breakAfter : true));
        start = end + 1;
    }
    // Collapsible segment breaks are transformed for rendering according to the
    // segment break transformation rules.
    // So here we jump to 4.1.2 of [CSSTEXT]:
    // Any collapsible segment break immediately following another collapsible
    // segment break is removed
    let index = -1;
    /** @type {BreakNumber | undefined} */ let join;
    while(++index < lines.length){
        // *   If the character immediately before or immediately after the segment
        //     break is the zero-width space character (U+200B), then the break is
        //     removed, leaving behind the zero-width space.
        if (lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */  || index < lines.length - 1 && lines[index + 1].charCodeAt(0) === 0x20_0b) {
            result.push(lines[index]);
            join = undefined;
        } else if (lines[index]) {
            if (typeof join === 'number') result.push(join);
            result.push(lines[index]);
            join = 0;
        } else if (index === 0 || index === lines.length - 1) {
            // If this line is empty, and it’s the first or last, add a space.
            // Note that this function is only called in normal whitespace, so we
            // don’t worry about `pre`.
            result.push(0);
        }
    }
    return result;
}
/**
 * Collect a text node as “pre” whitespace.
 *
 * @param {Text} node
 *   Text node.
 * @returns {Array<BreakNumber | string>}
 *   Result.
 */ function collectPreText(node) {
    return [
        String(node.value)
    ];
}
/**
 * 3.  Every collapsible tab is converted to a collapsible space (U+0020).
 * 4.  Any collapsible space immediately following another collapsible
 *     space—even one outside the boundary of the inline containing that
 *     space, provided both spaces are within the same inline formatting
 *     context—is collapsed to have zero advance width. (It is invisible,
 *     but retains its soft wrap opportunity, if any.)
 *
 * @param {string} value
 *   Value to collapse.
 * @param {BreakBefore} breakBefore
 *   Whether there was a break before.
 * @param {BreakAfter} breakAfter
 *   Whether there was a break after.
 * @returns {string}
 *   Result.
 */ function trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {
    /** @type {Array<string>} */ const result = [];
    let start = 0;
    /** @type {number | undefined} */ let end;
    while(start < value.length){
        searchTabOrSpaces.lastIndex = start;
        const match = searchTabOrSpaces.exec(value);
        end = match ? match.index : value.length;
        // If we’re not directly after a segment break, but there was white space,
        // add an empty value that will be turned into a space.
        if (!start && !end && match && !breakBefore) {
            result.push('');
        }
        if (start !== end) {
            result.push(value.slice(start, end));
        }
        start = match ? end + match[0].length : end;
    }
    // If we reached the end, there was trailing white space, and there’s no
    // segment break after this node, add an empty value that will be turned
    // into a space.
    if (start !== end && !breakAfter) {
        result.push('');
    }
    return result.join(' ');
}
/**
 * Figure out the whitespace of a node.
 *
 * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).
 *
 * @param {Nodes} node
 *   Node (typically `Element`).
 * @param {CollectionInfo} info
 *   Info on current collection.
 * @returns {Whitespace}
 *   Applied whitespace.
 */ function inferWhitespace(node, info) {
    if (node.type === 'element') {
        const properties = node.properties || {};
        switch(node.tagName){
            case 'listing':
            case 'plaintext':
            case 'xmp':
                {
                    return 'pre';
                }
            case 'nobr':
                {
                    return 'nowrap';
                }
            case 'pre':
                {
                    return properties.wrap ? 'pre-wrap' : 'pre';
                }
            case 'td':
            case 'th':
                {
                    return properties.noWrap ? 'nowrap' : info.whitespace;
                }
            case 'textarea':
                {
                    return 'pre-wrap';
                }
            default:
        }
    }
    return info.whitespace;
}
/**
 * @type {TestFunction}
 * @param {Element} node
 * @returns {node is {properties: {hidden: true}}}
 */ function hidden(node) {
    return Boolean((node.properties || {}).hidden);
}
/**
 * @type {TestFunction}
 * @param {Element} node
 * @returns {node is {tagName: 'td' | 'th'}}
 */ function isCell(node) {
    return node.tagName === 'td' || node.tagName === 'th';
}
/**
 * @type {TestFunction}
 */ function closedDialog(node) {
    return node.tagName === 'dialog' && !(node.properties || {}).open;
}
}}),
"[project]/node_modules/.pnpm/trim-trailing-lines@2.1.0/node_modules/trim-trailing-lines/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Remove final line endings from `value`
 *
 * @param {unknown} value
 *   Value with trailing line endings, coerced to string.
 * @return {string}
 *   Value without trailing line endings.
 */ __turbopack_context__.s({
    "trimTrailingLines": (()=>trimTrailingLines)
});
function trimTrailingLines(value) {
    const input = String(value);
    let end = input.length;
    while(end > 0){
        const code = input.codePointAt(end - 1);
        if (code !== undefined && (code === 10 || code === 13)) {
            end--;
        } else {
            break;
        }
    }
    return input.slice(0, end);
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/code.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Code} from 'mdast'
 */ __turbopack_context__.s({
    "code": (()=>code)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$trim$2d$trailing$2d$lines$40$2$2e$1$2e$0$2f$node_modules$2f$trim$2d$trailing$2d$lines$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/trim-trailing-lines@2.1.0/node_modules/trim-trailing-lines/index.js [app-ssr] (ecmascript)");
;
;
const prefix = 'language-';
function code(state, node) {
    const children = node.children;
    let index = -1;
    /** @type {Array<number | string> | undefined} */ let classList;
    /** @type {string | undefined} */ let lang;
    if (node.tagName === 'pre') {
        while(++index < children.length){
            const child = children[index];
            if (child.type === 'element' && child.tagName === 'code' && child.properties && child.properties.className && Array.isArray(child.properties.className)) {
                classList = child.properties.className;
                break;
            }
        }
    }
    if (classList) {
        index = -1;
        while(++index < classList.length){
            if (String(classList[index]).slice(0, prefix.length) === prefix) {
                lang = String(classList[index]).slice(prefix.length);
                break;
            }
        }
    }
    /** @type {Code} */ const result = {
        type: 'code',
        lang: lang || null,
        meta: null,
        value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$trim$2d$trailing$2d$lines$40$2$2e$1$2e$0$2f$node_modules$2f$trim$2d$trailing$2d$lines$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trimTrailingLines"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toText"])(node))
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/comment.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Comment} from 'hast'
 * @import {Html} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Comment>} node
 *   hast element to transform.
 * @returns {Html}
 *   mdast node.
 */ __turbopack_context__.s({
    "comment": (()=>comment)
});
function comment(state, node) {
    /** @type {Html} */ const result = {
        type: 'html',
        value: '<!--' + node.value + '-->'
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/del.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Delete, PhrasingContent} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Delete}
 *   mdast node.
 */ __turbopack_context__.s({
    "del": (()=>del)
});
function del(state, node) {
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = state.all(node);
    /** @type {Delete} */ const result = {
        type: 'delete',
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/list-items-spread.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {ListContent} from 'mdast'
 */ /**
 * Infer whether list items are spread.
 *
 * @param {Readonly<Array<Readonly<ListContent>>>} children
 *   List items.
 * @returns {boolean}
 *   Whether one or more list items are spread.
 */ __turbopack_context__.s({
    "listItemsSpread": (()=>listItemsSpread)
});
function listItemsSpread(children) {
    let index = -1;
    if (children.length > 1) {
        while(++index < children.length){
            if (children[index].spread) {
                return true;
            }
        }
    }
    return false;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/dl.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {ElementContent, Element} from 'hast'
 * @import {BlockContent, DefinitionContent, ListContent, ListItem, List} from 'mdast'
 */ /**
 * @typedef Group
 *   Title/definition group.
 * @property {Array<Element>} titles
 *   One or more titles.
 * @property {Array<ElementContent>} definitions
 *   One or more definitions.
 */ __turbopack_context__.s({
    "dl": (()=>dl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$list$2d$items$2d$spread$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/list-items-spread.js [app-ssr] (ecmascript)");
;
function dl(state, node) {
    /** @type {Array<ElementContent>} */ const clean = [];
    /** @type {Array<Group>} */ const groups = [];
    let index = -1;
    // Unwrap `<div>`s
    while(++index < node.children.length){
        const child = node.children[index];
        if (child.type === 'element' && child.tagName === 'div') {
            clean.push(...child.children);
        } else {
            clean.push(child);
        }
    }
    /** @type {Group} */ let group = {
        definitions: [],
        titles: []
    };
    index = -1;
    // Group titles and definitions.
    while(++index < clean.length){
        const child = clean[index];
        if (child.type === 'element' && child.tagName === 'dt') {
            const previous = clean[index - 1];
            if (previous && previous.type === 'element' && previous.tagName === 'dd') {
                groups.push(group);
                group = {
                    definitions: [],
                    titles: []
                };
            }
            group.titles.push(child);
        } else {
            group.definitions.push(child);
        }
    }
    groups.push(group);
    // Create items.
    index = -1;
    /** @type {Array<ListContent>} */ const content = [];
    while(++index < groups.length){
        const result = [
            ...handle(state, groups[index].titles),
            ...handle(state, groups[index].definitions)
        ];
        if (result.length > 0) {
            content.push({
                type: 'listItem',
                spread: result.length > 1,
                checked: null,
                children: result
            });
        }
    }
    // Create a list if there are items.
    if (content.length > 0) {
        /** @type {List} */ const result = {
            type: 'list',
            ordered: false,
            start: null,
            spread: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$list$2d$items$2d$spread$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["listItemsSpread"])(content),
            children: content
        };
        state.patch(node, result);
        return result;
    }
}
/**
 * @param {State} state
 *   State.
 * @param {Array<ElementContent>} children
 *   hast element children to transform.
 * @returns {Array<BlockContent | DefinitionContent>}
 *   mdast nodes.
 */ function handle(state, children) {
    const nodes = state.all({
        type: 'root',
        children
    });
    const listItems = state.toSpecificContent(nodes, create);
    if (listItems.length === 0) {
        return [];
    }
    if (listItems.length === 1) {
        return listItems[0].children;
    }
    return [
        {
            type: 'list',
            ordered: false,
            start: null,
            spread: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$list$2d$items$2d$spread$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["listItemsSpread"])(listItems),
            children: listItems
        }
    ];
}
/**
 * @returns {ListItem}
 */ function create() {
    return {
        type: 'listItem',
        spread: false,
        checked: null,
        children: []
    };
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/em.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Emphasis, PhrasingContent} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Emphasis}
 *   mdast node.
 */ __turbopack_context__.s({
    "em": (()=>em)
});
function em(state, node) {
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = state.all(node);
    /** @type {Emphasis} */ const result = {
        type: 'emphasis',
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes} from 'mdast'
 */ /**
 * Drop trailing initial and final `br`s.
 *
 * @template {Nodes} Node
 *   Node type.
 * @param {Array<Node>} nodes
 *   List of nodes.
 * @returns {Array<Node>}
 *   List of nodes w/o `break`s.
 */ __turbopack_context__.s({
    "dropSurroundingBreaks": (()=>dropSurroundingBreaks)
});
function dropSurroundingBreaks(nodes) {
    let start = 0;
    let end = nodes.length;
    while(start < end && nodes[start].type === 'break')start++;
    while(end > start && nodes[end - 1].type === 'break')end--;
    return start === 0 && end === nodes.length ? nodes : nodes.slice(start, end);
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/heading.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Heading, PhrasingContent} from 'mdast'
 */ __turbopack_context__.s({
    "heading": (()=>heading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js [app-ssr] (ecmascript)");
;
function heading(state, node) {
    const depth = /* c8 ignore next */ Number(node.tagName.charAt(1)) || 1;
    const children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dropSurroundingBreaks"])(state.all(node));
    /** @type {Heading} */ const result = {
        type: 'heading',
        depth,
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/hr.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {ThematicBreak} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {ThematicBreak}
 *   mdast node.
 */ __turbopack_context__.s({
    "hr": (()=>hr)
});
function hr(state, node) {
    /** @type {ThematicBreak} */ const result = {
        type: 'thematicBreak'
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/iframe.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Link} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Link | undefined}
 *   mdast node.
 */ __turbopack_context__.s({
    "iframe": (()=>iframe)
});
function iframe(state, node) {
    const properties = node.properties || {};
    const source = String(properties.src || '');
    const title = String(properties.title || '');
    // Only create a link if there is a title.
    // We can’t use the content of the frame because conforming HTML parsers treat
    // it as text, whereas legacy parsers treat it as HTML, so it will likely
    // contain tags that will show up in text.
    if (source && title) {
        /** @type {Link} */ const result = {
            type: 'link',
            title: null,
            url: state.resolve(source),
            children: [
                {
                    type: 'text',
                    value: title
                }
            ]
        };
        state.patch(node, result);
        return result;
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/img.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Image} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Image}
 *   mdast node.
 */ __turbopack_context__.s({
    "img": (()=>img)
});
function img(state, node) {
    const properties = node.properties || {};
    /** @type {Image} */ const result = {
        type: 'image',
        url: state.resolve(String(properties.src || '') || null),
        title: properties.title ? String(properties.title) : null,
        alt: properties.alt ? String(properties.alt) : ''
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/inline-code.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {InlineCode} from 'mdast'
 */ __turbopack_context__.s({
    "inlineCode": (()=>inlineCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)");
;
function inlineCode(state, node) {
    /** @type {InlineCode} */ const result = {
        type: 'inlineCode',
        value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toText"])(node)
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/find-selected-options.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Element, Properties} from 'hast'
 */ /**
 * @typedef {[string, Value]} Option
 *   Option, where the item at `0` is the label, the item at `1` the value.
 *
 * @typedef {Array<Option>} Options
 *   List of options.
 *
 * @typedef {string | undefined} Value
 *   `value` field of option.
 */ __turbopack_context__.s({
    "findSelectedOptions": (()=>findSelectedOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)");
;
function findSelectedOptions(node, explicitProperties) {
    /** @type {Array<Element>} */ const selectedOptions = [];
    /** @type {Options} */ const values = [];
    const properties = explicitProperties || node.properties || {};
    const options = findOptions(node);
    const size = Math.min(Number.parseInt(String(properties.size), 10), 0) || (properties.multiple ? 4 : 1);
    let index = -1;
    while(++index < options.length){
        const option = options[index];
        if (option && option.properties && option.properties.selected) {
            selectedOptions.push(option);
        }
    }
    const list = selectedOptions.length > 0 ? selectedOptions : options;
    const max = Math.min(list.length, size);
    index = -1;
    while(++index < max){
        const option = list[index];
        const properties = option.properties || {};
        const content = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toText"])(option);
        const label = content || String(properties.label || '');
        const value = String(properties.value || '') || content;
        values.push([
            value,
            label === value ? undefined : label
        ]);
    }
    return values;
}
/**
 * @param {Element} node
 *   Parent to find in.
 * @returns {Array<Element>}
 *   Option elements.
 */ function findOptions(node) {
    /** @type {Array<Element>} */ const results = [];
    let index = -1;
    while(++index < node.children.length){
        const child = node.children[index];
        if ('children' in child && Array.isArray(child.children)) {
            results.push(...findOptions(child));
        }
        if (child.type === 'element' && child.tagName === 'option' && (!child.properties || !child.properties.disabled)) {
            results.push(child);
        }
    }
    return results;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/input.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Image, Link, Text} from 'mdast'
 * @import {Options} from '../util/find-selected-options.js'
 */ __turbopack_context__.s({
    "input": (()=>input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$find$2d$selected$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/find-selected-options.js [app-ssr] (ecmascript)");
;
const defaultChecked = '[x]';
const defaultUnchecked = '[ ]';
function input(state, node) {
    const properties = node.properties || {};
    const value = String(properties.value || properties.placeholder || '');
    if (properties.disabled || properties.type === 'hidden' || properties.type === 'file') {
        return;
    }
    if (properties.type === 'checkbox' || properties.type === 'radio') {
        /** @type {Text} */ const result = {
            type: 'text',
            value: properties.checked ? state.options.checked || defaultChecked : state.options.unchecked || defaultUnchecked
        };
        state.patch(node, result);
        return result;
    }
    if (properties.type === 'image') {
        const alt = properties.alt || value;
        if (alt) {
            /** @type {Image} */ const result = {
                type: 'image',
                url: state.resolve(String(properties.src || '') || null),
                title: String(properties.title || '') || null,
                alt: String(alt)
            };
            state.patch(node, result);
            return result;
        }
        return;
    }
    /** @type {Options} */ let values = [];
    if (value) {
        values = [
            [
                value,
                undefined
            ]
        ];
    } else if (// `list` is not supported on these types:
    properties.type !== 'button' && properties.type !== 'file' && properties.type !== 'password' && properties.type !== 'reset' && properties.type !== 'submit' && properties.list) {
        const list = String(properties.list);
        const datalist = state.elementById.get(list);
        if (datalist && datalist.tagName === 'datalist') {
            values = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$find$2d$selected$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSelectedOptions"])(datalist, properties);
        }
    }
    if (values.length === 0) {
        return;
    }
    // Hide password value.
    if (properties.type === 'password') {
        // Passwords don’t support `list`.
        values[0] = [
            '•'.repeat(values[0][0].length),
            undefined
        ];
    }
    if (properties.type === 'email' || properties.type === 'url') {
        /** @type {Array<Link | Text>} */ const results = [];
        let index = -1;
        while(++index < values.length){
            const value = state.resolve(values[index][0]);
            /** @type {Link} */ const result = {
                type: 'link',
                title: null,
                url: properties.type === 'email' ? 'mailto:' + value : value,
                children: [
                    {
                        type: 'text',
                        value: values[index][1] || value
                    }
                ]
            };
            results.push(result);
            if (index !== values.length - 1) {
                results.push({
                    type: 'text',
                    value: ', '
                });
            }
        }
        return results;
    }
    /** @type {Array<string>} */ const texts = [];
    let index = -1;
    while(++index < values.length){
        texts.push(values[index][1] ? values[index][1] + ' (' + values[index][0] + ')' : values[index][0]);
    }
    /** @type {Text} */ const result = {
        type: 'text',
        value: texts.join(', ')
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-has-property@3.0.0/node_modules/hast-util-has-property/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Element} Element
 * @typedef {import('hast').Nodes} Nodes
 */ __turbopack_context__.s({
    "hasProperty": (()=>hasProperty)
});
const own = {}.hasOwnProperty;
function hasProperty(node, name) {
    const value = node.type === 'element' && own.call(node.properties, name) && node.properties[name];
    return value !== null && value !== undefined && value !== false;
}
}}),
"[project]/node_modules/.pnpm/hast-util-is-body-ok-link@3.0.1/node_modules/hast-util-is-body-ok-link/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Nodes} from 'hast'
 */ __turbopack_context__.s({
    "isBodyOkLink": (()=>isBodyOkLink)
});
const list = new Set([
    'pingback',
    'prefetch',
    'stylesheet'
]);
function isBodyOkLink(node) {
    if (node.type !== 'element' || node.tagName !== 'link') {
        return false;
    }
    if (node.properties.itemProp) {
        return true;
    }
    const value = node.properties.rel;
    let index = -1;
    if (!Array.isArray(value) || value.length === 0) {
        return false;
    }
    while(++index < value.length){
        if (!list.has(String(value[index]))) {
            return false;
        }
    }
    return true;
}
}}),
"[project]/node_modules/.pnpm/hast-util-phrasing@3.0.1/node_modules/hast-util-phrasing/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('hast').Nodes} Nodes
 */ __turbopack_context__.s({
    "phrasing": (()=>phrasing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-embedded@3.0.0/node_modules/hast-util-embedded/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$has$2d$property$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$has$2d$property$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-has-property@3.0.0/node_modules/hast-util-has-property/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-body-ok-link@3.0.1/node_modules/hast-util-is-body-ok-link/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-is-element@3.0.0/node_modules/hast-util-is-element/lib/index.js [app-ssr] (ecmascript)");
;
;
;
;
const basic = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])([
    'a',
    'abbr',
    // `area` is in fact only phrasing if it is inside a `map` element.
    // However, since `area`s are required to be inside a `map` element, and it’s
    // a rather involved check, it’s ignored here for now.
    'area',
    'b',
    'bdi',
    'bdo',
    'br',
    'button',
    'cite',
    'code',
    'data',
    'datalist',
    'del',
    'dfn',
    'em',
    'i',
    'input',
    'ins',
    'kbd',
    'keygen',
    'label',
    'map',
    'mark',
    'meter',
    'noscript',
    'output',
    'progress',
    'q',
    'ruby',
    's',
    'samp',
    'script',
    'select',
    'small',
    'span',
    'strong',
    'sub',
    'sup',
    'template',
    'textarea',
    'time',
    'u',
    'var',
    'wbr'
]);
const meta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$element$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$is$2d$element$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertElement"])('meta');
function phrasing(value) {
    return Boolean(value.type === 'text' || basic(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$embedded$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$embedded$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["embedded"])(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$is$2d$body$2d$ok$2d$link$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBodyOkLink"])(value) || meta(value) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$has$2d$property$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$has$2d$property$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hasProperty"])(value, 'itemProp'));
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/li.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {ListItem} from 'mdast'
 */ /**
 * @typedef ExtractResult
 *   Result of extracting a leading checkbox.
 * @property {Element | undefined} checkbox
 *   The checkbox that was removed, if any.
 * @property {Element} rest
 *   If there was a leading checkbox, a deep clone of the node w/o the leading
 *   checkbox; otherwise a reference to the given, untouched, node.
 */ __turbopack_context__.s({
    "li": (()=>li)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-phrasing@3.0.1/node_modules/hast-util-phrasing/lib/index.js [app-ssr] (ecmascript)");
;
function li(state, node) {
    // If the list item starts with a checkbox, remove the checkbox and mark the
    // list item as a GFM task list item.
    const { rest, checkbox } = extractLeadingCheckbox(node);
    const checked = checkbox ? Boolean(checkbox.properties.checked) : null;
    const spread = spreadout(rest);
    const children = state.toFlow(state.all(rest));
    /** @type {ListItem} */ const result = {
        type: 'listItem',
        spread,
        checked,
        children
    };
    state.patch(node, result);
    return result;
}
/**
 * Check if an element should spread out.
 *
 * The reason to spread out a markdown list item is primarily whether writing
 * the equivalent in markdown, would yield a spread out item.
 *
 * A spread out item results in `<p>` and `</p>` tags.
 * Otherwise, the phrasing would be output directly.
 * We can check for that: if there’s a `<p>` element, spread it out.
 *
 * But what if there are no paragraphs?
 * In that case, we can also assume that if two “block” things were written in
 * an item, that it is spread out, because blocks are typically joined by blank
 * lines, which also means a spread item.
 *
 * Lastly, because in HTML things can be wrapped in a `<div>` or similar, we
 * delve into non-phrasing elements here to figure out if they themselves
 * contain paragraphs or 2 or more flow non-phrasing elements.
 *
 * @param {Readonly<Element>} node
 * @returns {boolean}
 */ function spreadout(node) {
    let index = -1;
    let seenFlow = false;
    while(++index < node.children.length){
        const child = node.children[index];
        if (child.type === 'element') {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["phrasing"])(child)) continue;
            if (child.tagName === 'p' || seenFlow || spreadout(child)) {
                return true;
            }
            seenFlow = true;
        }
    }
    return false;
}
/**
 * Extract a leading checkbox from a list item.
 *
 * If there was a leading checkbox, makes a deep clone of the node w/o the
 * leading checkbox; otherwise a reference to the given, untouched, node is
 * given back.
 *
 * So for example:
 *
 * ```html
 * <li><input type="checkbox">Text</li>
 * ```
 *
 * …becomes:
 *
 * ```html
 * <li>Text</li>
 * ```
 *
 * ```html
 * <li><p><input type="checkbox">Text</p></li>
 * ```
 *
 * …becomes:
 *
 * ```html
 * <li><p>Text</p></li>
 * ```
 *
 * @param {Readonly<Element>} node
 * @returns {ExtractResult}
 */ function extractLeadingCheckbox(node) {
    const head = node.children[0];
    if (head && head.type === 'element' && head.tagName === 'input' && head.properties && (head.properties.type === 'checkbox' || head.properties.type === 'radio')) {
        const rest = {
            ...node,
            children: node.children.slice(1)
        };
        return {
            checkbox: head,
            rest
        };
    }
    // The checkbox may be nested in another element.
    // If the first element has children, look for a leading checkbox inside it.
    //
    // This only handles nesting in `<p>` elements, which is most common.
    // It’s possible a leading checkbox might be nested in other types of flow or
    // phrasing elements (and *deeply* nested, which is not possible with `<p>`).
    // Limiting things to `<p>` elements keeps this simpler for now.
    if (head && head.type === 'element' && head.tagName === 'p') {
        const { checkbox, rest: restHead } = extractLeadingCheckbox(head);
        if (checkbox) {
            const rest = {
                ...node,
                children: [
                    restHead,
                    ...node.children.slice(1)
                ]
            };
            return {
                checkbox,
                rest
            };
        }
    }
    return {
        checkbox: undefined,
        rest: node
    };
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/list.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {ListItem, List} from 'mdast'
 */ __turbopack_context__.s({
    "list": (()=>list)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$list$2d$items$2d$spread$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/list-items-spread.js [app-ssr] (ecmascript)");
;
function list(state, node) {
    const ordered = node.tagName === 'ol';
    const children = state.toSpecificContent(state.all(node), create);
    /** @type {number | null} */ let start = null;
    if (ordered) {
        start = node.properties && node.properties.start ? Number.parseInt(String(node.properties.start), 10) : 1;
    }
    /** @type {List} */ const result = {
        type: 'list',
        ordered,
        start,
        spread: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$list$2d$items$2d$spread$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["listItemsSpread"])(children),
        children
    };
    state.patch(node, result);
    return result;
}
/**
 * @returns {ListItem}
 */ function create() {
    return {
        type: 'listItem',
        spread: false,
        checked: null,
        children: []
    };
}
}}),
"[project]/node_modules/.pnpm/mdast-util-to-string@4.0.0/node_modules/mdast-util-to-string/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('mdast').Nodes} Nodes
 *
 * @typedef Options
 *   Configuration (optional).
 * @property {boolean | null | undefined} [includeImageAlt=true]
 *   Whether to use `alt` for `image`s (default: `true`).
 * @property {boolean | null | undefined} [includeHtml=true]
 *   Whether to use `value` of HTML (default: `true`).
 */ /** @type {Options} */ __turbopack_context__.s({
    "toString": (()=>toString)
});
const emptyOptions = {};
function toString(value, options) {
    const settings = options || emptyOptions;
    const includeImageAlt = typeof settings.includeImageAlt === 'boolean' ? settings.includeImageAlt : true;
    const includeHtml = typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true;
    return one(value, includeImageAlt, includeHtml);
}
/**
 * One node or several nodes.
 *
 * @param {unknown} value
 *   Thing to serialize.
 * @param {boolean} includeImageAlt
 *   Include image `alt`s.
 * @param {boolean} includeHtml
 *   Include HTML.
 * @returns {string}
 *   Serialized node.
 */ function one(value, includeImageAlt, includeHtml) {
    if (node(value)) {
        if ('value' in value) {
            return value.type === 'html' && !includeHtml ? '' : value.value;
        }
        if (includeImageAlt && 'alt' in value && value.alt) {
            return value.alt;
        }
        if ('children' in value) {
            return all(value.children, includeImageAlt, includeHtml);
        }
    }
    if (Array.isArray(value)) {
        return all(value, includeImageAlt, includeHtml);
    }
    return '';
}
/**
 * Serialize a list of nodes.
 *
 * @param {Array<unknown>} values
 *   Thing to serialize.
 * @param {boolean} includeImageAlt
 *   Include image `alt`s.
 * @param {boolean} includeHtml
 *   Include HTML.
 * @returns {string}
 *   Serialized nodes.
 */ function all(values, includeImageAlt, includeHtml) {
    /** @type {Array<string>} */ const result = [];
    let index = -1;
    while(++index < values.length){
        result[index] = one(values[index], includeImageAlt, includeHtml);
    }
    return result.join('');
}
/**
 * Check if `value` looks like a node.
 *
 * @param {unknown} value
 *   Thing.
 * @returns {value is Nodes}
 *   Whether `value` is a node.
 */ function node(value) {
    return Boolean(value && typeof value === 'object');
}
}}),
"[project]/node_modules/.pnpm/mdast-util-phrasing@4.1.0/node_modules/mdast-util-phrasing/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @typedef {import('mdast').Html} Html
 * @typedef {import('mdast').PhrasingContent} PhrasingContent
 */ __turbopack_context__.s({
    "phrasing": (()=>phrasing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-is@6.0.0/node_modules/unist-util-is/lib/index.js [app-ssr] (ecmascript)");
;
const phrasing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$is$40$6$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$is$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convert"])([
    'break',
    'delete',
    'emphasis',
    // To do: next major: removed since footnotes were added to GFM.
    'footnote',
    'footnoteReference',
    'image',
    'imageReference',
    'inlineCode',
    // Enabled by `mdast-util-math`:
    'inlineMath',
    'link',
    'linkReference',
    // Enabled by `mdast-util-mdx`:
    'mdxJsxTextElement',
    // Enabled by `mdast-util-mdx`:
    'mdxTextExpression',
    'strong',
    'text',
    // Enabled by `mdast-util-directive`:
    'textDirective'
]);
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/wrap.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {} from 'mdast-util-to-hast'
 * @import {
 *   BlockContent,
 *   Delete,
 *   Link,
 *   Nodes,
 *   Paragraph,
 *   Parents,
 *   PhrasingContent,
 *   RootContent
 * } from 'mdast'
 */ __turbopack_context__.s({
    "wrap": (()=>wrap),
    "wrapNeeded": (()=>wrapNeeded)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-phrasing@3.0.1/node_modules/hast-util-phrasing/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-whitespace@3.0.0/node_modules/hast-util-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$mdast$2d$util$2d$phrasing$40$4$2e$1$2e$0$2f$node_modules$2f$mdast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/mdast-util-phrasing@4.1.0/node_modules/mdast-util-phrasing/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js [app-ssr] (ecmascript)");
;
;
;
;
;
function wrapNeeded(nodes) {
    let index = -1;
    while(++index < nodes.length){
        const node = nodes[index];
        if (!phrasing(node) || 'children' in node && wrapNeeded(node.children)) {
            return true;
        }
    }
    return false;
}
function wrap(nodes) {
    return runs(nodes, onphrasing, function(d) {
        return d;
    });
    "TURBOPACK unreachable";
    /**
   * @param {Array<PhrasingContent>} nodes
   * @returns {Array<Paragraph>}
   */ function onphrasing(nodes) {
        return nodes.every(function(d) {
            return d.type === 'text' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$whitespace$40$3$2e$0$2e$0$2f$node_modules$2f$hast$2d$util$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["whitespace"])(d.value) : false;
        }) ? [] : [
            {
                type: 'paragraph',
                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dropSurroundingBreaks"])(nodes)
            }
        ];
    }
}
/**
 * @param {Delete | Link} node
 * @returns {Array<BlockContent>}
 */ function split(node) {
    return runs(node.children, onphrasing, onnonphrasing);
    "TURBOPACK unreachable";
    /**
   * Use `parent`, put the phrasing run inside it.
   *
   * @param {Array<PhrasingContent>} nodes
   * @returns {Array<BlockContent>}
   */ function onphrasing(nodes) {
        const newParent = cloneWithoutChildren(node);
        newParent.children = nodes;
        // @ts-expect-error Assume fine.
        return [
            newParent
        ];
    }
    /**
   * Use `child`, add `parent` as its first child, put the original children
   * into `parent`.
   * If `child` is not a parent, `parent` will not be added.
   *
   * @param {BlockContent} child
   * @returns {BlockContent}
   */ function onnonphrasing(child) {
        if ('children' in child && 'children' in node) {
            const newParent = cloneWithoutChildren(node);
            const newChild = cloneWithoutChildren(child);
            // @ts-expect-error Assume fine.
            newParent.children = child.children;
            // @ts-expect-error Assume fine.
            newChild.children.push(newParent);
            return newChild;
        }
        return {
            ...child
        };
    }
}
/**
 * Wrap all runs of mdast phrasing content in `paragraph` nodes.
 *
 * @param {Array<RootContent>} nodes
 *   List of input nodes.
 * @param {(nodes: Array<PhrasingContent>) => Array<BlockContent>} onphrasing
 *   Turn phrasing content into block content.
 * @param {(node: BlockContent) => BlockContent} onnonphrasing
 *   Map block content (defaults to keeping them as-is).
 * @returns {Array<BlockContent>}
 */ function runs(nodes, onphrasing, onnonphrasing) {
    const flattened = flatten(nodes);
    /** @type {Array<BlockContent>} */ const result = [];
    /** @type {Array<PhrasingContent>} */ let queue = [];
    let index = -1;
    while(++index < flattened.length){
        const node = flattened[index];
        if (phrasing(node)) {
            queue.push(node);
        } else {
            if (queue.length > 0) {
                result.push(...onphrasing(queue));
                queue = [];
            }
            // @ts-expect-error Assume non-phrasing.
            result.push(onnonphrasing(node));
        }
    }
    if (queue.length > 0) {
        result.push(...onphrasing(queue));
        queue = [];
    }
    return result;
}
/**
 * Flatten a list of nodes.
 *
 * @param {Array<RootContent>} nodes
 *   List of nodes, will unravel `delete` and `link`.
 * @returns {Array<RootContent>}
 *   Unraveled nodes.
 */ function flatten(nodes) {
    /** @type {Array<RootContent>} */ const flattened = [];
    let index = -1;
    while(++index < nodes.length){
        const node = nodes[index];
        // Straddling: some elements are *weird*.
        // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.
        // See: <https://html.spec.whatwg.org/#paragraphs>.
        // Paragraphs are the weirdest of them all.
        // See the straddling fixture for more info!
        // `ins` is ignored in mdast, so we don’t need to worry about that.
        // `map` maps to its content, so we don’t need to worry about that either.
        // `del` maps to `delete` and `a` to `link`, so we do handle those.
        // What we’ll do is split `node` over each of its children.
        if ((node.type === 'delete' || node.type === 'link') && wrapNeeded(node.children)) {
            flattened.push(...split(node));
        } else {
            flattened.push(node);
        }
    }
    return flattened;
}
/**
 * Check if an mdast node is phrasing.
 *
 * Also supports checking embedded hast fields.
 *
 * @param {Nodes} node
 *   mdast node to check.
 * @returns {node is PhrasingContent}
 *   Whether `node` is phrasing content (includes nodes with `hName` fields
 *   set to phrasing hast element names).
 */ function phrasing(node) {
    const tagName = node.data && node.data.hName;
    return tagName ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$phrasing$40$3$2e$0$2e$1$2f$node_modules$2f$hast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["phrasing"])({
        type: 'element',
        tagName,
        properties: {},
        children: []
    }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$mdast$2d$util$2d$phrasing$40$4$2e$1$2e$0$2f$node_modules$2f$mdast$2d$util$2d$phrasing$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["phrasing"])(node);
}
/**
 * @template {Parents} ParentType
 *   Parent type.
 * @param {ParentType} node
 *   Node to clone.
 * @returns {ParentType}
 *   Cloned node, without children.
 */ function cloneWithoutChildren(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])({
        ...node,
        children: []
    });
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/media.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Image, Link, PhrasingContent, RootContent as MdastRootContent, Root} from 'mdast'
 */ __turbopack_context__.s({
    "media": (()=>media)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$mdast$2d$util$2d$to$2d$string$40$4$2e$0$2e$0$2f$node_modules$2f$mdast$2d$util$2d$to$2d$string$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/mdast-util-to-string@4.0.0/node_modules/mdast-util-to-string/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/wrap.js [app-ssr] (ecmascript)");
;
;
;
function media(state, node) {
    const properties = node.properties || {};
    const poster = node.tagName === 'video' ? String(properties.poster || '') : '';
    let source = String(properties.src || '');
    let index = -1;
    let linkInFallbackContent = false;
    let nodes = state.all(node);
    /** @type {Root} */ const fragment = {
        type: 'root',
        children: nodes
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["visit"])(fragment, function(node) {
        if (node.type === 'link') {
            linkInFallbackContent = true;
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EXIT"];
        }
    });
    // If the content links to something, or if it’s not phrasing…
    if (linkInFallbackContent || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapNeeded"])(nodes)) {
        return nodes;
    }
    // Find the source.
    while(!source && ++index < node.children.length){
        const child = node.children[index];
        if (child.type === 'element' && child.tagName === 'source' && child.properties) {
            source = String(child.properties.src || '');
        }
    }
    // If there’s a poster defined on the video, create an image.
    if (poster) {
        /** @type {Image} */ const image = {
            type: 'image',
            title: null,
            url: state.resolve(poster),
            alt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$mdast$2d$util$2d$to$2d$string$40$4$2e$0$2e$0$2f$node_modules$2f$mdast$2d$util$2d$to$2d$string$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toString"])(nodes)
        };
        state.patch(node, image);
        nodes = [
            image
        ];
    }
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = nodes;
    // Link to the media resource.
    /** @type {Link} */ const result = {
        type: 'link',
        title: properties.title ? String(properties.title) : null,
        url: state.resolve(source),
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/p.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Paragraph, PhrasingContent} from 'mdast'
 */ __turbopack_context__.s({
    "p": (()=>p)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js [app-ssr] (ecmascript)");
;
function p(state, node) {
    const children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$drop$2d$surrounding$2d$breaks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dropSurroundingBreaks"])(state.all(node));
    if (children.length > 0) {
        /** @type {Paragraph} */ const result = {
            type: 'paragraph',
            children
        };
        state.patch(node, result);
        return result;
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/q.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {RootContent as MdastRootContent} from 'mdast'
 */ __turbopack_context__.s({
    "q": (()=>q)
});
const defaultQuotes = [
    '"'
];
function q(state, node) {
    const quotes = state.options.quotes || defaultQuotes;
    state.qNesting++;
    const contents = state.all(node);
    state.qNesting--;
    const quote = quotes[state.qNesting % quotes.length];
    const head = contents[0];
    const tail = contents[contents.length - 1];
    const open = quote.charAt(0);
    const close = quote.length > 1 ? quote.charAt(1) : quote;
    if (head && head.type === 'text') {
        head.value = open + head.value;
    } else {
        contents.unshift({
            type: 'text',
            value: open
        });
    }
    if (tail && tail.type === 'text') {
        tail.value += close;
    } else {
        contents.push({
            type: 'text',
            value: close
        });
    }
    return contents;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/root.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Root as HastRoot} from 'hast'
 * @import {Root as MdastRoot} from 'mdast'
 */ __turbopack_context__.s({
    "root": (()=>root)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/wrap.js [app-ssr] (ecmascript)");
;
function root(state, node) {
    let children = state.all(node);
    if (state.options.document || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapNeeded"])(children)) {
        children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrap"])(children);
    }
    /** @type {MdastRoot} */ const result = {
        type: 'root',
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/select.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Text} from 'mdast'
 */ __turbopack_context__.s({
    "select": (()=>select)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$find$2d$selected$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/find-selected-options.js [app-ssr] (ecmascript)");
;
function select(state, node) {
    const values = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$find$2d$selected$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSelectedOptions"])(node);
    let index = -1;
    /** @type {Array<string>} */ const results = [];
    while(++index < values.length){
        const value = values[index];
        results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0]);
    }
    if (results.length > 0) {
        /** @type {Text} */ const result = {
            type: 'text',
            value: results.join(', ')
        };
        state.patch(node, result);
        return result;
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/strong.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {PhrasingContent, Strong} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Strong}
 *   mdast node.
 */ __turbopack_context__.s({
    "strong": (()=>strong)
});
function strong(state, node) {
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = state.all(node);
    /** @type {Strong} */ const result = {
        type: 'strong',
        children
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table-cell.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {PhrasingContent, TableCell} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {TableCell}
 *   mdast node.
 */ __turbopack_context__.s({
    "tableCell": (()=>tableCell)
});
function tableCell(state, node) {
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    const children = state.all(node);
    /** @type {TableCell} */ const result = {
        type: 'tableCell',
        children
    };
    state.patch(node, result);
    if (node.properties) {
        const rowSpan = node.properties.rowSpan;
        const colSpan = node.properties.colSpan;
        if (rowSpan || colSpan) {
            const data = result.data || (result.data = {});
            if (rowSpan) data.hastUtilToMdastTemporaryRowSpan = rowSpan;
            if (colSpan) data.hastUtilToMdastTemporaryColSpan = colSpan;
        }
    }
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table-row.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {RowContent, TableRow} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {TableRow}
 *   mdast node.
 */ __turbopack_context__.s({
    "tableRow": (()=>tableRow)
});
function tableRow(state, node) {
    const children = state.toSpecificContent(state.all(node), create);
    /** @type {TableRow} */ const result = {
        type: 'tableRow',
        children
    };
    state.patch(node, result);
    return result;
}
/**
 * @returns {RowContent}
 */ function create() {
    return {
        type: 'tableCell',
        children: []
    };
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {AlignType, RowContent, TableContent, Table, Text} from 'mdast'
 */ /**
 * @typedef Info
 *   Inferred info on a table.
 * @property {Array<AlignType>} align
 *   Alignment.
 * @property {boolean} headless
 *   Whether a `thead` is missing.
 */ __turbopack_context__.s({
    "table": (()=>table)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit-parents@6.0.1/node_modules/unist-util-visit-parents/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js [app-ssr] (ecmascript) <locals>");
;
;
function table(state, node) {
    // Ignore nested tables.
    if (state.inTable) {
        /** @type {Text} */ const result = {
            type: 'text',
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toText"])(node)
        };
        state.patch(node, result);
        return result;
    }
    state.inTable = true;
    const { align, headless } = inspect(node);
    const rows = state.toSpecificContent(state.all(node), createRow);
    // Add an empty header row.
    if (headless) {
        rows.unshift(createRow());
    }
    let rowIndex = -1;
    while(++rowIndex < rows.length){
        const row = rows[rowIndex];
        const cells = state.toSpecificContent(row.children, createCell);
        row.children = cells;
    }
    let columns = 1;
    rowIndex = -1;
    while(++rowIndex < rows.length){
        const cells = rows[rowIndex].children;
        let cellIndex = -1;
        while(++cellIndex < cells.length){
            const cell = cells[cellIndex];
            if (cell.data) {
                const data = cell.data;
                const colSpan = Number.parseInt(String(data.hastUtilToMdastTemporaryColSpan), 10) || 1;
                const rowSpan = Number.parseInt(String(data.hastUtilToMdastTemporaryRowSpan), 10) || 1;
                if (colSpan > 1 || rowSpan > 1) {
                    let otherRowIndex = rowIndex - 1;
                    while(++otherRowIndex < rowIndex + rowSpan){
                        let colIndex = cellIndex - 1;
                        while(++colIndex < cellIndex + colSpan){
                            if (!rows[otherRowIndex]) {
                                break;
                            }
                            /** @type {Array<RowContent>} */ const newCells = [];
                            if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {
                                newCells.push({
                                    type: 'tableCell',
                                    children: []
                                });
                            }
                            rows[otherRowIndex].children.splice(colIndex, 0, ...newCells);
                        }
                    }
                }
                // Clean the data fields.
                if ('hastUtilToMdastTemporaryColSpan' in cell.data) delete cell.data.hastUtilToMdastTemporaryColSpan;
                if ('hastUtilToMdastTemporaryRowSpan' in cell.data) delete cell.data.hastUtilToMdastTemporaryRowSpan;
                if (Object.keys(cell.data).length === 0) delete cell.data;
            }
        }
        if (cells.length > columns) columns = cells.length;
    }
    // Add extra empty cells.
    rowIndex = -1;
    while(++rowIndex < rows.length){
        const cells = rows[rowIndex].children;
        let cellIndex = cells.length - 1;
        while(++cellIndex < columns){
            cells.push({
                type: 'tableCell',
                children: []
            });
        }
    }
    let alignIndex = align.length - 1;
    while(++alignIndex < columns){
        align.push(null);
    }
    state.inTable = false;
    /** @type {Table} */ const result = {
        type: 'table',
        align,
        children: rows
    };
    state.patch(node, result);
    return result;
}
/**
 * Infer whether the HTML table has a head and how it aligns.
 *
 * @param {Readonly<Element>} node
 *   Table element to check.
 * @returns {Info}
 *   Info.
 */ function inspect(node) {
    /** @type {Info} */ const info = {
        align: [
            null
        ],
        headless: true
    };
    let rowIndex = 0;
    let cellIndex = 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["visit"])(node, function(child) {
        if (child.type === 'element') {
            // Don’t enter nested tables.
            if (child.tagName === 'table' && node !== child) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$2d$parents$40$6$2e$0$2e$1$2f$node_modules$2f$unist$2d$util$2d$visit$2d$parents$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SKIP"];
            }
            if ((child.tagName === 'th' || child.tagName === 'td') && child.properties) {
                if (!info.align[cellIndex]) {
                    const value = String(child.properties.align || '') || null;
                    if (value === 'center' || value === 'left' || value === 'right' || value === null) {
                        info.align[cellIndex] = value;
                    }
                }
                // If there is a `th` in the first row, assume there is a header row.
                if (info.headless && rowIndex < 2 && child.tagName === 'th') {
                    info.headless = false;
                }
                cellIndex++;
            } else if (child.tagName === 'thead') {
                info.headless = false;
            } else if (child.tagName === 'tr') {
                rowIndex++;
                cellIndex = 0;
            }
        }
    });
    return info;
}
/**
 * @returns {RowContent}
 */ function createCell() {
    return {
        type: 'tableCell',
        children: []
    };
}
/**
 * @returns {TableContent}
 */ function createRow() {
    return {
        type: 'tableRow',
        children: []
    };
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/text.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Text as HastText} from 'hast'
 * @import {Text as MdastText} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<HastText>} node
 *   hast element to transform.
 * @returns {MdastText}
 *   mdast node.
 */ __turbopack_context__.s({
    "text": (()=>text)
});
function text(state, node) {
    /** @type {MdastText} */ const result = {
        type: 'text',
        value: node.value
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/textarea.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Text} from 'mdast'
 */ __turbopack_context__.s({
    "textarea": (()=>textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-text@4.0.2/node_modules/hast-util-to-text/lib/index.js [app-ssr] (ecmascript)");
;
function textarea(state, node) {
    /** @type {Text} */ const result = {
        type: 'text',
        value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$text$40$4$2e$0$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$text$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toText"])(node)
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/wbr.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Text} from 'mdast'
 */ /**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Text}
 *   mdast node.
 */ __turbopack_context__.s({
    "wbr": (()=>wbr)
});
function wbr(state, node) {
    /** @type {Text} */ const result = {
        type: 'text',
        value: '\u200B'
    };
    state.patch(node, result);
    return result;
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Parents} from 'hast'
 */ __turbopack_context__.s({
    "handlers": (()=>handlers),
    "nodeHandlers": (()=>nodeHandlers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$a$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/a.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/base.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$blockquote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/blockquote.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$br$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/br.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$comment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/comment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$del$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/del.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$dl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/dl.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$em$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/em.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/heading.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$hr$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/hr.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$iframe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/iframe.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$img$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/img.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/inline-code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$input$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/input.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$li$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/li.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/list.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$media$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/media.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$p$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/p.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$q$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/q.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$root$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/root.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/select.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$strong$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/strong.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2d$cell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table-cell.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2d$row$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table-row.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/table.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$textarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/textarea.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$wbr$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/wbr.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const nodeHandlers = {
    comment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$comment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["comment"],
    doctype: ignore,
    root: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$root$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["root"],
    text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["text"]
};
const handlers = {
    // Ignore:
    applet: ignore,
    area: ignore,
    basefont: ignore,
    bgsound: ignore,
    caption: ignore,
    col: ignore,
    colgroup: ignore,
    command: ignore,
    content: ignore,
    datalist: ignore,
    dialog: ignore,
    element: ignore,
    embed: ignore,
    frame: ignore,
    frameset: ignore,
    isindex: ignore,
    keygen: ignore,
    link: ignore,
    math: ignore,
    menu: ignore,
    menuitem: ignore,
    meta: ignore,
    nextid: ignore,
    noembed: ignore,
    noframes: ignore,
    optgroup: ignore,
    option: ignore,
    param: ignore,
    script: ignore,
    shadow: ignore,
    source: ignore,
    spacer: ignore,
    style: ignore,
    svg: ignore,
    template: ignore,
    title: ignore,
    track: ignore,
    // Use children:
    abbr: all,
    acronym: all,
    bdi: all,
    bdo: all,
    big: all,
    blink: all,
    button: all,
    canvas: all,
    cite: all,
    data: all,
    details: all,
    dfn: all,
    font: all,
    ins: all,
    label: all,
    map: all,
    marquee: all,
    meter: all,
    nobr: all,
    noscript: all,
    object: all,
    output: all,
    progress: all,
    rb: all,
    rbc: all,
    rp: all,
    rt: all,
    rtc: all,
    ruby: all,
    slot: all,
    small: all,
    span: all,
    sup: all,
    sub: all,
    tbody: all,
    tfoot: all,
    thead: all,
    time: all,
    // Use children as flow.
    address: flow,
    article: flow,
    aside: flow,
    body: flow,
    center: flow,
    div: flow,
    fieldset: flow,
    figcaption: flow,
    figure: flow,
    form: flow,
    footer: flow,
    header: flow,
    hgroup: flow,
    html: flow,
    legend: flow,
    main: flow,
    multicol: flow,
    nav: flow,
    picture: flow,
    section: flow,
    // Handle.
    a: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$a$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["a"],
    audio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$media$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["media"],
    b: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$strong$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["strong"],
    base: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["base"],
    blockquote: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$blockquote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["blockquote"],
    br: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$br$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["br"],
    code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["inlineCode"],
    dir: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["list"],
    dl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$dl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dl"],
    dt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$li$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["li"],
    dd: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$li$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["li"],
    del: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$del$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["del"],
    em: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$em$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["em"],
    h1: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    h2: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    h3: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    h4: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    h5: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    h6: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$heading$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["heading"],
    hr: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$hr$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hr"],
    i: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$em$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["em"],
    iframe: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$iframe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["iframe"],
    img: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$img$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["img"],
    image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$img$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["img"],
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$input$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["input"],
    kbd: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["inlineCode"],
    li: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$li$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["li"],
    listing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["code"],
    mark: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$em$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["em"],
    ol: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["list"],
    p: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$p$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["p"],
    plaintext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["code"],
    pre: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["code"],
    q: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$q$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["q"],
    s: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$del$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["del"],
    samp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["inlineCode"],
    select: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["select"],
    strike: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$del$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["del"],
    strong: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$strong$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["strong"],
    summary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$p$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["p"],
    table: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["table"],
    td: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2d$cell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tableCell"],
    textarea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$textarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["textarea"],
    th: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2d$cell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tableCell"],
    tr: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$table$2d$row$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tableRow"],
    tt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["inlineCode"],
    u: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$em$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["em"],
    ul: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["list"],
    var: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$inline$2d$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["inlineCode"],
    video: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$media$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["media"],
    wbr: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$wbr$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wbr"],
    xmp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["code"]
};
/**
 * @param {State} state
 *   State.
 * @param {Parents} node
 *   Parent to transform.
 */ function all(state, node) {
    return state.all(node);
}
/**
 * @param {State} state
 *   State.
 * @param {Parents} node
 *   Parent to transform.
 */ function flow(state, node) {
    return state.toFlow(state.all(node));
}
/**
 * @returns {undefined}
 */ function ignore() {}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/state.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Element, Nodes, Parents} from 'hast'
 * @import {
 *   BlockContent as MdastBlockContent,
 *   DefinitionContent as MdastDefinitionContent,
 *   Nodes as MdastNodes,
 *   Parents as MdastParents,
 *   RootContent as MdastRootContent
 * } from 'mdast'
 */ /**
 * @typedef {MdastBlockContent | MdastDefinitionContent} MdastFlowContent
 */ /**
 * @callback All
 *   Transform the children of a hast parent to mdast.
 * @param {Parents} parent
 *   Parent.
 * @returns {Array<MdastRootContent>}
 *   mdast children.
 *
 * @callback Handle
 *   Handle a particular element.
 * @param {State} state
 *   Info passed around about the current state.
 * @param {Element} element
 *   Element to transform.
 * @param {Parents | undefined} parent
 *   Parent of `element`.
 * @returns {Array<MdastNodes> | MdastNodes | undefined | void}
 *   mdast node or nodes.
 *
 *   Note: `void` is included until TS nicely infers `undefined`.
 *
 * @callback NodeHandle
 *   Handle a particular node.
 * @param {State} state
 *   Info passed around about the current state.
 * @param {any} node
 *   Node to transform.
 * @param {Parents | undefined} parent
 *   Parent of `node`.
 * @returns {Array<MdastNodes> | MdastNodes | undefined | void}
 *   mdast node or nodes.
 *
 *   Note: `void` is included until TS nicely infers `undefined`.
 *
 * @callback One
 *   Transform a hast node to mdast.
 * @param {Nodes} node
 *   Expected hast node.
 * @param {Parents | undefined} parent
 *   Parent of `node`.
 * @returns {Array<MdastNodes> | MdastNodes | undefined}
 *   mdast result.
 *
 * @typedef Options
 *   Configuration.
 * @property {string | null | undefined} [checked='[x]']
 *   Value to use for a checked checkbox or radio input (default: `'[x]'`)
 * @property {boolean | null | undefined} [document]
 *   Whether the given tree represents a complete document (optional).
 *
 *   Applies when the `tree` is a `root` node.
 *   When the tree represents a complete document, then things are wrapped in
 *   paragraphs when needed, and otherwise they’re left as-is.
 *   The default checks for whether there’s mixed content: some phrasing nodes
 *   *and* some non-phrasing nodes.
 * @property {Record<string, Handle | null | undefined> | null | undefined} [handlers]
 *   Object mapping tag names to functions handling the corresponding elements
 *   (optional).
 *
 *   Merged into the defaults.
 * @property {boolean | null | undefined} [newlines=false]
 *   Keep line endings when collapsing whitespace (default: `false`).
 *
 *   The default collapses to a single space.
 * @property {Record<string, NodeHandle | null | undefined> | null | undefined} [nodeHandlers]
 *   Object mapping node types to functions handling the corresponding nodes
 *   (optional).
 *
 *   Merged into the defaults.
 * @property {Array<string> | null | undefined} [quotes=['"']]
 *   List of quotes to use (default: `['"']`).
 *
 *   Each value can be one or two characters.
 *   When two, the first character determines the opening quote and the second
 *   the closing quote at that level.
 *   When one, both the opening and closing quote are that character.
 *
 *   The order in which the preferred quotes appear determines which quotes to
 *   use at which level of nesting.
 *   So, to prefer `‘’` at the first level of nesting, and `“”` at the second,
 *   pass `['‘’', '“”']`.
 *   If `<q>`s are nested deeper than the given amount of quotes, the markers
 *   wrap around: a third level of nesting when using `['«»', '‹›']` should
 *   have double guillemets, a fourth single, a fifth double again, etc.
 * @property {string | null | undefined} [unchecked='[ ]']
 *   Value to use for an unchecked checkbox or radio input (default: `'[ ]'`).
 *
 * @callback Patch
 *   Copy a node’s positional info.
 * @param {Nodes} from
 *   hast node to copy from.
 * @param {MdastNodes} to
 *   mdast node to copy into.
 * @returns {undefined}
 *   Nothing.
 *
 * @callback Resolve
 *   Resolve a URL relative to a base.
 * @param {string | null | undefined} url
 *   Possible URL value.
 * @returns {string}
 *   URL, resolved to a `base` element, if any.
 *
 * @typedef State
 *   Info passed around about the current state.
 * @property {All} all
 *   Transform the children of a hast parent to mdast.
 * @property {boolean} baseFound
 *   Whether a `<base>` element was seen.
 * @property {Map<string, Element>} elementById
 *   Elements by their `id`.
 * @property {string | undefined} frozenBaseUrl
 *   `href` of `<base>`, if any.
 * @property {Record<string, Handle>} handlers
 *   Applied element handlers.
 * @property {boolean} inTable
 *   Whether we’re in a table.
 * @property {Record<string, NodeHandle>} nodeHandlers
 *   Applied node handlers.
 * @property {One} one
 *   Transform a hast node to mdast.
 * @property {Options} options
 *   User configuration.
 * @property {Patch} patch
 *   Copy a node’s positional info.
 * @property {number} qNesting
 *   Non-negative finite integer representing how deep we’re in `<q>`s.
 * @property {Resolve} resolve
 *   Resolve a URL relative to a base.
 * @property {ToFlow} toFlow
 *   Transform a list of mdast nodes to flow.
 * @property {<ChildType extends MdastNodes, ParentType extends MdastParents & {'children': Array<ChildType>}>(nodes: Array<MdastRootContent>, build: (() => ParentType)) => Array<ParentType>} toSpecificContent
 *   Turn arbitrary content into a list of a particular node type.
 *
 *   This is useful for example for lists, which must have list items as
 *   content.
 *   in this example, when non-items are found, they will be queued, and
 *   inserted into an adjacent item.
 *   When no actual items exist, one will be made with `build`.
 *
 * @callback ToFlow
 *   Transform a list of mdast nodes to flow.
 * @param {Array<MdastRootContent>} nodes
 *   mdast nodes.
 * @returns {Array<MdastFlowContent>}
 *   mdast flow children.
 */ __turbopack_context__.s({
    "createState": (()=>createState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$position$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$position$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-position@5.0.0/node_modules/unist-util-position/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/handlers/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/util/wrap.js [app-ssr] (ecmascript)");
;
;
;
const own = {}.hasOwnProperty;
function createState(options) {
    return {
        all,
        baseFound: false,
        elementById: new Map(),
        frozenBaseUrl: undefined,
        handlers: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handlers"],
            ...options.handlers
        },
        inTable: false,
        nodeHandlers: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$handlers$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["nodeHandlers"],
            ...options.nodeHandlers
        },
        one,
        options,
        patch,
        qNesting: 0,
        resolve,
        toFlow,
        toSpecificContent
    };
}
/**
 * Transform the children of a hast parent to mdast.
 *
 * You might want to combine this with `toFlow` or `toSpecificContent`.
 *
 * @this {State}
 *   Info passed around about the current state.
 * @param {Parents} parent
 *   Parent.
 * @returns {Array<MdastRootContent>}
 *   mdast children.
 */ function all(parent) {
    const children = parent.children || [];
    /** @type {Array<MdastRootContent>} */ const results = [];
    let index = -1;
    while(++index < children.length){
        const child = children[index];
        // Content -> content.
        const result = this.one(child, parent);
        if (Array.isArray(result)) {
            results.push(...result);
        } else if (result) {
            results.push(result);
        }
    }
    return results;
}
/**
 * Transform a hast node to mdast.
 *
 * @this {State}
 *   Info passed around about the current state.
 * @param {Nodes} node
 *   hast node to transform.
 * @param {Parents | undefined} parent
 *   Parent of `node`.
 * @returns {Array<MdastNodes> | MdastNodes | undefined}
 *   mdast result.
 */ function one(node, parent) {
    if (node.type === 'element') {
        if (node.properties && node.properties.dataMdast === 'ignore') {
            return;
        }
        if (own.call(this.handlers, node.tagName)) {
            return this.handlers[node.tagName](this, node, parent) || undefined;
        }
    } else if (own.call(this.nodeHandlers, node.type)) {
        return this.nodeHandlers[node.type](this, node, parent) || undefined;
    }
    // Unknown literal.
    if ('value' in node && typeof node.value === 'string') {
        /** @type {MdastRootContent} */ const result = {
            type: 'text',
            value: node.value
        };
        this.patch(node, result);
        return result;
    }
    // Unknown parent.
    if ('children' in node) {
        return this.all(node);
    }
}
/**
 * Copy a node’s positional info.
 *
 * @param {Nodes} origin
 *   hast node to copy from.
 * @param {MdastNodes} node
 *   mdast node to copy into.
 * @returns {undefined}
 *   Nothing.
 */ function patch(origin, node) {
    if (origin.position) node.position = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$position$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$position$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["position"])(origin);
}
/**
 * @this {State}
 *   Info passed around about the current state.
 * @param {string | null | undefined} url
 *   Possible URL value.
 * @returns {string}
 *   URL, resolved to a `base` element, if any.
 */ function resolve(url) {
    const base = this.frozenBaseUrl;
    if (url === null || url === undefined) {
        return '';
    }
    if (base) {
        return String(new URL(url, base));
    }
    return url;
}
/**
 * Transform a list of mdast nodes to flow.
 *
 * @this {State}
 *   Info passed around about the current state.
 * @param {Array<MdastRootContent>} nodes
 *   Parent.
 * @returns {Array<MdastFlowContent>}
 *   mdast flow children.
 */ function toFlow(nodes) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$util$2f$wrap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrap"])(nodes);
}
/**
 * Turn arbitrary content into a particular node type.
 *
 * This is useful for example for lists, which must have list items as content.
 * in this example, when non-items are found, they will be queued, and
 * inserted into an adjacent item.
 * When no actual items exist, one will be made with `build`.
 *
 * @template {MdastNodes} ChildType
 *   Node type of children.
 * @template {MdastParents & {'children': Array<ChildType>}} ParentType
 *   Node type of parent.
 * @param {Array<MdastRootContent>} nodes
 *   Nodes, which are either `ParentType`, or will be wrapped in one.
 * @param {() => ParentType} build
 *   Build a parent if needed (must have empty `children`).
 * @returns {Array<ParentType>}
 *   List of parents.
 */ function toSpecificContent(nodes, build) {
    const reference = build();
    /** @type {Array<ParentType>} */ const results = [];
    /** @type {Array<ChildType>} */ let queue = [];
    let index = -1;
    while(++index < nodes.length){
        const node = nodes[index];
        if (expectedParent(node)) {
            if (queue.length > 0) {
                node.children.unshift(...queue);
                queue = [];
            }
            results.push(node);
        } else {
            // Assume `node` can be a child of `ParentType`.
            // If we start checking nodes, we’d run into problems with unknown nodes,
            // which we do want to support.
            const child = node;
            queue.push(child);
        }
    }
    if (queue.length > 0) {
        let node = results[results.length - 1];
        if (!node) {
            node = build();
            results.push(node);
        }
        node.children.push(...queue);
        queue = [];
    }
    return results;
    "TURBOPACK unreachable";
    /**
   * @param {MdastNodes} node
   * @returns {node is ParentType}
   */ function expectedParent(node) {
        return node.type === reference.type;
    }
}
}}),
"[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Options} from 'hast-util-to-mdast'
 * @import {Nodes} from 'hast'
 * @import {Nodes as MdastNodes, RootContent as MdastRootContent} from 'mdast'
 */ __turbopack_context__.s({
    "toMdast": (()=>toMdast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$minify$2d$whitespace$40$6$2e$0$2e$2$2f$node_modules$2f$rehype$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-minify-whitespace@6.0.2/node_modules/rehype-minify-whitespace/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/state.js [app-ssr] (ecmascript)");
;
;
;
;
/** @type {Readonly<Options>} */ const emptyOptions = {};
function toMdast(tree, options) {
    // We have to clone, cause we’ll use `rehype-minify-whitespace` on the tree,
    // which modifies.
    const cleanTree = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$ungap$2b$structured$2d$clone$40$1$2e$3$2e$0$2f$node_modules$2f40$ungap$2f$structured$2d$clone$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(tree);
    const settings = options || emptyOptions;
    const transformWhitespace = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$minify$2d$whitespace$40$6$2e$0$2e$2$2f$node_modules$2f$rehype$2d$minify$2d$whitespace$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        newlines: settings.newlines === true
    });
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createState"])(settings);
    /** @type {MdastNodes} */ let mdast;
    // @ts-expect-error: fine to pass an arbitrary node.
    transformWhitespace(cleanTree);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["visit"])(cleanTree, function(node) {
        if (node && node.type === 'element' && node.properties) {
            const id = String(node.properties.id || '') || undefined;
            if (id && !state.elementById.has(id)) {
                state.elementById.set(id, node);
            }
        }
    });
    const result = state.one(cleanTree, undefined);
    if (!result) {
        mdast = {
            type: 'root',
            children: []
        };
    } else if (Array.isArray(result)) {
        // Assume content.
        const children = result;
        mdast = {
            type: 'root',
            children
        };
    } else {
        mdast = result;
    }
    // Collapse text nodes, and fix whitespace.
    //
    // Most of this is taken care of by `rehype-minify-whitespace`, but
    // we’re generating some whitespace too, and some nodes are in the end
    // ignored.
    // So clean up.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$unist$2d$util$2d$visit$40$5$2e$0$2e$0$2f$node_modules$2f$unist$2d$util$2d$visit$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["visit"])(mdast, function(node, index, parent) {
        if (node.type === 'text' && index !== undefined && parent) {
            const previous = parent.children[index - 1];
            if (previous && previous.type === node.type) {
                previous.value += node.value;
                parent.children.splice(index, 1);
                if (previous.position && node.position) {
                    previous.position.end = node.position.end;
                }
                // Iterate over the previous node again, to handle its total value.
                return index - 1;
            }
            node.value = node.value.replace(/[\t ]*(\r?\n|\r)[\t ]*/, '$1');
            // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),
            // as there the whitespace matters.
            if (parent && (parent.type === 'heading' || parent.type === 'paragraph' || parent.type === 'root')) {
                if (!index) {
                    node.value = node.value.replace(/^[\t ]+/, '');
                }
                if (index === parent.children.length - 1) {
                    node.value = node.value.replace(/[\t ]+$/, '');
                }
            }
            if (!node.value) {
                parent.children.splice(index, 1);
                return index;
            }
        }
    });
    return mdast;
}
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/lib/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @import {Root as HastRoot} from 'hast'
 * @import {Options} from 'hast-util-to-mdast'
 * @import {Root as MdastRoot} from 'mdast'
 * @import {Processor} from 'unified'
 * @import {VFile} from 'vfile'
 */ /**
 * @callback TransformBridge
 *   Bridge-mode.
 *
 *   Runs the destination with the new mdast tree.
 *   Discards result.
 * @param {HastRoot} tree
 *   Tree.
 * @param {VFile} file
 *   File.
 * @returns {Promise<undefined>}
 *   Nothing.
 *
 * @callback TransformMutate
 *  Mutate-mode.
 *
 *  Further transformers run on the mdast tree.
 * @param {HastRoot} tree
 *   Tree.
 * @param {VFile} file
 *   File.
 * @returns {MdastRoot}
 *   Tree (mdast).
 */ __turbopack_context__.s({
    "default": (()=>rehypeRemark)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/hast-util-to-mdast@10.1.2/node_modules/hast-util-to-mdast/lib/index.js [app-ssr] (ecmascript)");
;
/** @satisfies {Options} */ const defaults = {
    document: true
};
function rehypeRemark(destination, options) {
    if (destination && 'run' in destination) {
        /**
     * @type {TransformBridge}
     */ return async function(tree, file) {
            const mdastTree = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toMdast"])(tree, {
                ...defaults,
                ...options
            });
            await destination.run(mdastTree, file);
        };
    }
    /**
   * @type {TransformMutate}
   */ return function(tree) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$hast$2d$util$2d$to$2d$mdast$40$10$2e$1$2e$2$2f$node_modules$2f$hast$2d$util$2d$to$2d$mdast$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toMdast"])(tree, {
            ...defaults,
            ...destination
        });
    };
}
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$rehype$2d$remark$40$10$2e$0$2e$1$2f$node_modules$2f$rehype$2d$remark$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=node_modules__pnpm_eb9918b0._.js.map