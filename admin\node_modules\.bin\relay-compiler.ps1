#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\bin\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\node_modules;D:\Projects\shigriyat\admin\node_modules;D:\Projects\shigriyat\node_modules;D:\Projects\node_modules;D:\node_modules;D:\Projects\shigriyat\admin\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/bin/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/relay-compiler/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/@ardatan/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules:/mnt/d/Projects/shigriyat/node_modules:/mnt/d/Projects/node_modules:/mnt/d/node_modules:/mnt/d/Projects/shigriyat/admin/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" $args
  } else {
    & "node$exe"  "$basedir/../@ardatan/relay-compiler/bin/relay-compiler" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
