hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/provider-utils@2.2.7(zod@3.24.3)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@1.1.3':
    '@ai-sdk/provider': private
  '@ai-sdk/react@1.2.9(react@19.1.0)(zod@3.24.3)':
    '@ai-sdk/react': private
  '@ai-sdk/ui-utils@1.2.8(zod@3.24.3)':
    '@ai-sdk/ui-utils': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ardatan/relay-compiler@12.0.3(graphql@16.10.0)':
    '@ardatan/relay-compiler': private
  '@ariakit/core@0.4.15':
    '@ariakit/core': private
  '@ariakit/react-core@0.4.17(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ariakit/react-core': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.10':
    '@babel/core': private
  '@babel/generator@7.27.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.0':
    '@babel/helpers': private
  '@babel/parser@7.27.0':
    '@babel/parser': private
  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@babel/template@7.27.0':
    '@babel/template': private
  '@babel/traverse@7.27.0':
    '@babel/traverse': private
  '@babel/types@7.27.0':
    '@babel/types': private
  '@date-fns/tz@1.2.0':
    '@date-fns/tz': private
  '@effect/platform@0.69.8(effect@3.10.3)':
    '@effect/platform': private
  '@envelop/core@5.2.3':
    '@envelop/core': private
  '@envelop/instrumentation@1.0.0':
    '@envelop/instrumentation': private
  '@envelop/types@5.2.1':
    '@envelop/types': private
  '@eslint-community/eslint-utils@4.6.1(eslint@9.25.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.1':
    '@eslint/config-helpers': public
  '@eslint/core@0.13.0':
    '@eslint/core': public
  '@eslint/js@9.25.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': public
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.26.28(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@graphql-codegen/add@5.0.3(graphql@16.10.0)':
    '@graphql-codegen/add': private
  '@graphql-codegen/core@4.0.2(graphql@16.10.0)':
    '@graphql-codegen/core': private
  '@graphql-codegen/gql-tag-operations@4.0.17(graphql@16.10.0)':
    '@graphql-codegen/gql-tag-operations': private
  '@graphql-codegen/plugin-helpers@5.1.0(graphql@16.10.0)':
    '@graphql-codegen/plugin-helpers': private
  '@graphql-codegen/typed-document-node@5.1.1(graphql@16.10.0)':
    '@graphql-codegen/typed-document-node': private
  '@graphql-codegen/visitor-plugin-common@5.8.0(graphql@16.10.0)':
    '@graphql-codegen/visitor-plugin-common': private
  '@graphql-hive/signal@1.0.0':
    '@graphql-hive/signal': private
  '@graphql-tools/apollo-engine-loader@8.0.20(graphql@16.10.0)':
    '@graphql-tools/apollo-engine-loader': private
  '@graphql-tools/batch-execute@9.0.15(graphql@16.10.0)':
    '@graphql-tools/batch-execute': private
  '@graphql-tools/code-file-loader@8.1.20(graphql@16.10.0)':
    '@graphql-tools/code-file-loader': private
  '@graphql-tools/delegate@10.2.17(graphql@16.10.0)':
    '@graphql-tools/delegate': private
  '@graphql-tools/documents@1.0.1(graphql@16.10.0)':
    '@graphql-tools/documents': private
  '@graphql-tools/executor-common@0.0.4(graphql@16.10.0)':
    '@graphql-tools/executor-common': private
  '@graphql-tools/executor-graphql-ws@2.0.5(graphql@16.10.0)':
    '@graphql-tools/executor-graphql-ws': private
  '@graphql-tools/executor-http@1.3.3(@types/node@20.17.30)(graphql@16.10.0)':
    '@graphql-tools/executor-http': private
  '@graphql-tools/executor-legacy-ws@1.1.17(graphql@16.10.0)':
    '@graphql-tools/executor-legacy-ws': private
  '@graphql-tools/executor@1.4.7(graphql@16.10.0)':
    '@graphql-tools/executor': private
  '@graphql-tools/git-loader@8.0.24(graphql@16.10.0)':
    '@graphql-tools/git-loader': private
  '@graphql-tools/github-loader@8.0.20(@types/node@20.17.30)(graphql@16.10.0)':
    '@graphql-tools/github-loader': private
  '@graphql-tools/graphql-file-loader@8.0.19(graphql@16.10.0)':
    '@graphql-tools/graphql-file-loader': private
  '@graphql-tools/graphql-tag-pluck@8.3.19(graphql@16.10.0)':
    '@graphql-tools/graphql-tag-pluck': private
  '@graphql-tools/import@7.0.18(graphql@16.10.0)':
    '@graphql-tools/import': private
  '@graphql-tools/json-file-loader@8.0.18(graphql@16.10.0)':
    '@graphql-tools/json-file-loader': private
  '@graphql-tools/load@8.1.0(graphql@16.10.0)':
    '@graphql-tools/load': private
  '@graphql-tools/merge@9.0.24(graphql@16.10.0)':
    '@graphql-tools/merge': private
  '@graphql-tools/optimize@2.0.0(graphql@16.10.0)':
    '@graphql-tools/optimize': private
  '@graphql-tools/prisma-loader@8.0.17(@types/node@20.17.30)(graphql@16.10.0)':
    '@graphql-tools/prisma-loader': private
  '@graphql-tools/relay-operation-optimizer@7.0.19(graphql@16.10.0)':
    '@graphql-tools/relay-operation-optimizer': private
  '@graphql-tools/schema@10.0.23(graphql@16.10.0)':
    '@graphql-tools/schema': private
  '@graphql-tools/url-loader@8.0.31(@types/node@20.17.30)(graphql@16.10.0)':
    '@graphql-tools/url-loader': private
  '@graphql-tools/utils@10.8.6(graphql@16.10.0)':
    '@graphql-tools/utils': private
  '@graphql-tools/wrap@10.0.35(graphql@16.10.0)':
    '@graphql-tools/wrap': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.10.0)':
    '@graphql-typed-document-node/core': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@mantine/core@7.17.8(@mantine/hooks@7.17.8(react@19.1.0))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@mantine/core': private
  '@mantine/hooks@7.17.8(react@19.1.0)':
    '@mantine/hooks': private
  '@mantine/utils@6.0.22(react@19.1.0)':
    '@mantine/utils': private
  '@next/env@15.2.4':
    '@next/env': private
  '@next/eslint-plugin-next@15.2.4':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.2.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.2.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.2.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.2.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.2.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.2.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.2.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.2.4':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': private
  '@pdf-lib/standard-fonts@1.0.0':
    '@pdf-lib/standard-fonts': private
  '@pdf-lib/upng@1.0.1':
    '@pdf-lib/upng': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.11(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.6(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.0(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-toggle-group@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle-group': private
  '@radix-ui/react-toggle@1.1.6(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.0.0(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.0(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@repeaterjs/repeater@3.0.6':
    '@repeaterjs/repeater': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': public
  '@shikijs/types@3.2.1':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.4':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.4':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.4':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.4':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.4':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.4':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.4':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.4':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.4':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.4':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.4':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.4':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.4':
    '@tailwindcss/oxide': private
  '@tanstack/query-core@5.74.4':
    '@tanstack/query-core': private
  '@tiptap/core@2.26.1(@tiptap/pm@2.26.1)':
    '@tiptap/core': private
  '@tiptap/extension-bold@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-code@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-code': private
  '@tiptap/extension-floating-menu@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-history@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-history': private
  '@tiptap/extension-horizontal-rule@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-italic@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-link@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-link': private
  '@tiptap/extension-paragraph@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-strike@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-table-cell@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-table-cell': private
  '@tiptap/extension-table-header@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-table-header': private
  '@tiptap/extension-text@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-text': private
  '@tiptap/extension-underline@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-underline': private
  '@tiptap/pm@2.26.1':
    '@tiptap/pm': private
  '@tiptap/react@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@tiptap/react': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/js-yaml@4.0.9':
    '@types/js-yaml': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@typescript-eslint/eslint-plugin@8.30.1(@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@8.30.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.30.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.30.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.30.1':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-darwin-arm64@1.6.0':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.6.0':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.6.0':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.6.0':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.6.0':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.6.0':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.6.0':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.6.0':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.6.0':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.6.0':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.6.0':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.6.0':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.6.0':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.6.0':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.6.0':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.6.0':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@uploadthing/mime-types@0.3.1':
    '@uploadthing/mime-types': private
  '@uploadthing/shared@7.1.0':
    '@uploadthing/shared': private
  '@whatwg-node/disposablestack@0.0.6':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/fetch@0.10.6':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.18':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.1':
    '@whatwg-node/promise-helpers': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  astral-regex@2.0.0:
    astral-regex: private
  async-function@1.0.0:
    async-function: private
  auto-bind@4.0.0:
    auto-bind: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-arraybuffer@1.0.2:
    base64-arraybuffer: private
  base64-js@1.5.1:
    base64-js: private
  bl@4.1.0:
    bl: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer@5.7.1:
    buffer: private
  busboy@1.6.0:
    busboy: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  caniuse-lite@1.0.30001715:
    caniuse-lite: private
  capital-case@1.0.4:
    capital-case: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  change-case-all@1.0.15:
    change-case-all: private
  change-case@4.1.2:
    change-case: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  chardet@0.7.0:
    chardet: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@2.1.0:
    cli-truncate: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorette@2.0.20:
    colorette: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@10.0.1:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  concat-map@0.0.1:
    concat-map: private
  constant-case@3.0.4:
    constant-case: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@0.7.2:
    cookie: private
  cosmiconfig@8.3.6(typescript@5.8.3):
    cosmiconfig: private
  crelt@1.0.6:
    crelt: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-inspect@1.0.1:
    cross-inspect: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-line-break@2.1.0:
    css-line-break: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dataloader@2.2.3:
    dataloader: private
  date-fns-jalali@4.1.0-0:
    date-fns-jalali: private
  debounce@1.2.1:
    debounce: private
  debug@4.4.0:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dependency-graph@0.11.0:
    dependency-graph: private
  dequal@2.0.3:
    dequal: private
  detect-indent@6.1.0:
    detect-indent: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  dot-case@3.0.4:
    dot-case: private
  dset@3.1.4:
    dset: private
  dunder-proto@1.0.1:
    dunder-proto: private
  effect@3.10.3:
    effect: private
  electron-to-chromium@1.5.139:
    electron-to-chromium: private
  emoji-mart@5.6.0:
    emoji-mart: private
  emoji-regex@9.2.2:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0)(eslint@9.25.0(jiti@2.4.2)):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0)(eslint@9.25.0(jiti@2.4.2)))(eslint@9.25.0(jiti@2.4.2)):
    eslint-module-utils: public
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.0)(eslint@9.25.0(jiti@2.4.2)):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.25.0(jiti@2.4.2)):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@9.25.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@9.25.0(jiti@2.4.2)):
    eslint-plugin-react: public
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  execa@7.2.0:
    execa: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  fast-check@3.23.2:
    fast-check: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  fetch-blob@3.2.0:
    fetch-blob: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-selector@0.6.0:
    file-selector: private
  fill-range@7.1.1:
    fill-range: private
  find-my-way-ts@0.1.5:
    find-my-way-ts: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  fs-extra@11.3.0:
    fs-extra: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql-config@5.1.4(@types/node@20.17.30)(graphql@16.10.0)(typescript@5.8.3):
    graphql-config: private
  graphql-request@6.1.0(graphql@16.10.0):
    graphql-request: private
  graphql-sock@1.0.1(graphql@16.10.0):
    graphql-sock: private
  graphql-tag@2.12.6(graphql@16.10.0):
    graphql-tag: private
  graphql-ws@6.0.4(graphql@16.10.0)(ws@8.18.1):
    graphql-ws: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-embedded@3.0.0:
    hast-util-embedded: private
  hast-util-format@1.1.0:
    hast-util-format: private
  hast-util-from-dom@5.0.1:
    hast-util-from-dom: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-has-property@3.0.0:
    hast-util-has-property: private
  hast-util-is-body-ok-link@3.0.1:
    hast-util-is-body-ok-link: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-minify-whitespace@1.0.1:
    hast-util-minify-whitespace: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-phrasing@3.0.1:
    hast-util-phrasing: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-mdast@10.1.2:
    hast-util-to-mdast: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  header-case@2.0.4:
    header-case: private
  highlight.js@11.11.1:
    highlight.js: private
  html-void-elements@3.0.0:
    html-void-elements: private
  html-whitespace-sensitive-tag-names@3.0.1:
    html-whitespace-sensitive-tag-names: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@4.3.1:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  immutable@3.7.6:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-from@4.0.0:
    import-from: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inherits@2.0.4:
    inherits: private
  inquirer@8.2.6:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  is-absolute@1.0.0:
    is-absolute: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@2.0.0:
    is-interactive: private
  is-lower-case@2.0.2:
    is-lower-case: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-relative@1.0.0:
    is-relative: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@3.0.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unc-path@1.0.0:
    is-unc-path: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@2.0.2:
    is-upper-case: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isomorphic-ws@5.0.0(ws@8.18.1):
    isomorphic-ws: private
  isomorphic.js@0.2.5:
    isomorphic.js: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jiti@1.21.7:
    jiti: private
  jose@4.15.9:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-to-pretty-yaml@1.2.2:
    json-to-pretty-yaml: private
  json5@1.0.2:
    json5: private
  jsondiffpatch@0.6.0:
    jsondiffpatch: private
  jsonfile@6.1.0:
    jsonfile: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lib0@0.2.109:
    lib0: private
  lightningcss-darwin-arm64@1.29.2:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.29.2:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.29.2:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.29.2:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.29.2:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.29.2:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.29.2:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.29.2:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.29.2:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.29.2:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.29.2:
    lightningcss: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  linkifyjs@4.3.1:
    linkifyjs: private
  listr2@4.0.5:
    listr2: private
  load-script@1.0.0:
    load-script: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@4.0.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@2.0.2:
    lower-case-first: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@6.0.0:
    lru-cache: private
  map-cache@0.2.2:
    map-cache: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  memoize-one@5.2.1:
    memoize-one: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  meros@1.3.0(@types/node@20.17.30):
    meros: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  ms@2.1.3:
    ms: private
  multipasta@0.2.5:
    multipasta: private
  mute-stream@0.0.8:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.1.1:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  no-case@3.0.4:
    no-case: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@3.3.2:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@2.1.1:
    normalize-path: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nullthrows@1.1.1:
    nullthrows: private
  oauth@0.9.15:
    oauth: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@2.2.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  oidc-token-hash@5.1.0:
    oidc-token-hash: private
  onetime@6.0.0:
    onetime: private
  openid-client@5.7.1:
    openid-client: private
  optionator@0.9.4:
    optionator: private
  ora@6.3.1:
    ora: private
  orderedmap@2.1.1:
    orderedmap: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  pako@1.0.11:
    pako: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-filepath@1.0.2:
    parse-filepath: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.2.1:
    parse5: private
  pascal-case@3.1.2:
    pascal-case: private
  path-case@3.0.4:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-root-regex@0.1.2:
    path-root-regex: private
  path-root@0.1.1:
    path-root: private
  path-type@4.0.0:
    path-type: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.3:
    postcss: private
  preact-render-to-string@5.2.6(preact@10.26.5):
    preact-render-to-string: private
  preact@10.26.5:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@3.8.0:
    pretty-format: private
  promise@7.3.1:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.1.0:
    property-information: private
  prosemirror-changeset@2.3.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.7.1:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.2:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.3.2:
    prosemirror-gapcursor: private
  prosemirror-highlight@0.13.0(@shikijs/types@3.2.1)(@types/hast@3.0.4)(highlight.js@11.11.1)(lowlight@3.3.0)(prosemirror-model@1.25.2)(prosemirror-state@1.4.3)(prosemirror-transform@1.10.4)(prosemirror-view@1.40.0):
    prosemirror-highlight: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.5.0:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.3:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.2:
    prosemirror-markdown: private
  prosemirror-menu@1.2.5:
    prosemirror-menu: private
  prosemirror-model@1.25.2:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.4:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.1:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.7.1:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.25.2)(prosemirror-state@1.4.3)(prosemirror-view@1.40.0):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.4:
    prosemirror-transform: private
  prosemirror-view@1.40.0:
    prosemirror-view: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-icons@5.5.0(react@19.1.0):
    react-icons: private
  react-is@16.13.1:
    react-is: private
  react-number-format@5.4.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-number-format: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.2)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@19.1.2)(react@19.1.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.1.2)(react@19.1.0):
    react-style-singleton: private
  readable-stream@3.6.2:
    readable-stream: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  rehype-format@5.0.1:
    rehype-format: private
  rehype-minify-whitespace@6.0.2:
    rehype-minify-whitespace: private
  rehype-parse@9.0.1:
    rehype-parse: private
  rehype-remark@10.0.1:
    rehype-remark: private
  rehype-stringify@10.0.1:
    rehype-stringify: private
  relay-runtime@12.0.0:
    relay-runtime: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  remedial@1.0.8:
    remedial: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  remove-trailing-spaces@1.0.9:
    remove-trailing-spaces: private
  require-directory@2.1.1:
    require-directory: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rope-sequence@1.3.4:
    rope-sequence: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  scuid@1.1.0:
    scuid: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@6.3.1:
    semver: private
  sentence-case@3.0.4:
    sentence-case: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  signedsource@1.0.0:
    signedsource: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@3.0.0:
    slice-ansi: private
  snake-case@3.0.4:
    snake-case: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  sponge-case@1.0.1:
    sponge-case: private
  sqids@0.3.0:
    sqids: private
  stable-hash@0.0.5:
    stable-hash: private
  stdin-discarder@0.1.0:
    stdin-discarder: private
  streamsearch@1.1.0:
    streamsearch: private
  string-env-interpolation@1.0.1:
    string-env-interpolation: private
  string-width@4.2.3:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.6(@babel/core@7.26.10)(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@2.0.2:
    swap-case: private
  swr@2.3.3(react@19.1.0):
    swr: private
  sync-fetch@0.6.0-2:
    sync-fetch: private
  tabbable@6.2.0:
    tabbable: private
  tapable@2.2.1:
    tapable: private
  text-segmentation@1.0.3:
    text-segmentation: private
  throttleit@2.1.0:
    throttleit: private
  through@2.3.8:
    through: private
  timeout-signal@2.0.0:
    timeout-signal: private
  tinyglobby@0.2.13:
    tinyglobby: private
  tippy.js@6.3.7:
    tippy.js: private
  title-case@3.0.3:
    title-case: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trim-trailing-lines@2.1.0:
    trim-trailing-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-log@2.2.7:
    ts-log: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ua-parser-js@1.0.40:
    ua-parser-js: private
  uc.micro@2.1.0:
    uc.micro: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unc-path-regex@0.1.2:
    unc-path-regex: private
  undici-types@6.19.8:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unixify@1.0.0:
    unixify: private
  unrs-resolver@1.6.0:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  upper-case-first@2.0.2:
    upper-case-first: private
  upper-case@2.0.2:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@10.0.0:
    urlpattern-polyfill: private
  use-callback-ref@1.3.3(@types/react@19.1.2)(react@19.1.0):
    use-callback-ref: private
  use-composed-ref@1.4.0(@types/react@19.1.2)(react@19.1.0):
    use-composed-ref: private
  use-isomorphic-layout-effect@1.2.0(@types/react@19.1.2)(react@19.1.0):
    use-isomorphic-layout-effect: private
  use-latest@1.3.0(@types/react@19.1.2)(react@19.1.0):
    use-latest: private
  use-sidecar@1.1.3(@types/react@19.1.2)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utrie@1.0.2:
    utrie: private
  uuid@8.3.2:
    uuid: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  wcwidth@1.0.1:
    wcwidth: private
  web-namespaces@2.0.1:
    web-namespaces: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  ws@8.18.1:
    ws: private
  y-prosemirror@1.3.7(prosemirror-model@1.25.2)(prosemirror-state@1.4.3)(prosemirror-view@1.40.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27):
    y-prosemirror: private
  y-protocols@1.0.6(yjs@13.6.27):
    y-protocols: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml-ast-parser@0.0.43:
    yaml-ast-parser: private
  yaml@2.7.1:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yjs@13.6.27:
    yjs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.5(zod@3.24.3):
    zod-to-json-schema: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.2.0
pendingBuilds: []
prunedAt: Fri, 11 Jul 2025 21:07:05 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@napi-rs/wasm-runtime@0.2.9'
  - '@next/swc-darwin-arm64@15.2.4'
  - '@next/swc-darwin-x64@15.2.4'
  - '@next/swc-linux-arm64-gnu@15.2.4'
  - '@next/swc-linux-arm64-musl@15.2.4'
  - '@next/swc-linux-x64-gnu@15.2.4'
  - '@next/swc-linux-x64-musl@15.2.4'
  - '@next/swc-win32-arm64-msvc@15.2.4'
  - '@tailwindcss/oxide-android-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-x64@4.1.4'
  - '@tailwindcss/oxide-freebsd-x64@4.1.4'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.4'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.4'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.4'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.4'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.6.0'
  - '@unrs/resolver-binding-darwin-x64@1.6.0'
  - '@unrs/resolver-binding-freebsd-x64@1.6.0'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.6.0'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.6.0'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.6.0'
  - '@unrs/resolver-binding-linux-arm64-musl@1.6.0'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.6.0'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.6.0'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.6.0'
  - '@unrs/resolver-binding-linux-x64-gnu@1.6.0'
  - '@unrs/resolver-binding-linux-x64-musl@1.6.0'
  - '@unrs/resolver-binding-wasm32-wasi@1.6.0'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.6.0'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.6.0'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\Projects\shigriyat\admin\node_modules\.pnpm
virtualStoreDirMaxLength: 120
