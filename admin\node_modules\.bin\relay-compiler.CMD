@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\bin\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\node_modules;D:\Projects\shigriyat\admin\node_modules;D:\Projects\shigriyat\node_modules;D:\Projects\node_modules;D:\node_modules;D:\Projects\shigriyat\admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\bin\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\relay-compiler\node_modules;D:\Projects\shigriyat\admin\node_modules\@ardatan\node_modules;D:\Projects\shigriyat\admin\node_modules;D:\Projects\shigriyat\node_modules;D:\Projects\node_modules;D:\node_modules;D:\Projects\shigriyat\admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@ardatan\relay-compiler\bin\relay-compiler" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@ardatan\relay-compiler\bin\relay-compiler" %*
)
