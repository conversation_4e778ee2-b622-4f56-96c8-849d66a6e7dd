"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthors } from "@/lib/hooks/use-authors";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronsUpDown, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface AuthorSelectProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export function AuthorSelect({ value, onChange, disabled }: AuthorSelectProps) {
  const [authorSearchTerm, setAuthorSearchTerm] = useState("");
  const [debouncedAuthorSearchTerm, setDebouncedAuthorSearchTerm] = useState("");
  const [openAuthorPopover, setOpenAuthorPopover] = useState(false);
  const [selectedAuthor, setSelectedAuthor] = useState<{ id: string, displayName: string } | null>(null);
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const authorSearchInputRef = useRef<HTMLInputElement>(null);

  // Дебаунсинг для поиска авторов
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedAuthorSearchTerm(authorSearchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [authorSearchTerm]);

  const { data: authorsData, isLoading: isLoadingAuthors } = useAuthors(1, 50, debouncedAuthorSearchTerm);
  const authors = authorsData?.authors || [];

  // Автоматический фокус на поле поиска автора при открытии поповера
  useEffect(() => {
    if (openAuthorPopover && authorSearchInputRef.current) {
      setTimeout(() => {
        authorSearchInputRef.current?.focus();
      }, 100);
    }
  }, [openAuthorPopover]);

  // Установка выбранного автора при изменении value
  useEffect(() => {
    if (value && authors.length > 0) {
      const author = authors.find(a => a.id === value);
      if (author) {
        setSelectedAuthor({ id: author.id, displayName: author.displayName });
      }
    } else {
      setSelectedAuthor(null);
    }
  }, [value, authors]);

  return (
    <Popover open={openAuthorPopover} onOpenChange={setOpenAuthorPopover}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={openAuthorPopover}
          className="w-full justify-between"
          disabled={disabled || isLoadingAuthors}
        >
          {isLoadingAuthors ? (
            "Авторлар йөкләнә..."
          ) : value ? (
            selectedAuthor?.displayName || authorsData?.authors.find((author) => author.id === value)?.displayName || "Автор сайланган"
          ) : (
            "Автор сайлагыз"
          )}
          <div className="flex">
            {value && (
              <div
                className="h-4 w-4 mr-1 flex items-center justify-center cursor-pointer rounded-sm hover:bg-accent hover:text-accent-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange("");
                  setSelectedAuthor(null);
                  setOpenAuthorPopover(false);
                }}
                style={{ opacity: isLoadingAuthors ? 0.5 : 1, pointerEvents: isLoadingAuthors ? 'none' : 'auto' }}
              >
                <X className="h-3 w-3" />
              </div>
            )}
            <ChevronsUpDown className="ml-1 h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col w-full">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              ref={authorSearchInputRef}
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground"
              placeholder="Автор эзләү..."
              value={authorSearchTerm}
              onChange={(e) => {
                setAuthorSearchTerm(e.target.value);
                setHighlightedIndex(0);
              }}
              onKeyDown={(e) => {
                if (!authors.length) return;

                if (e.key === "ArrowDown") {
                  e.preventDefault();
                  setHighlightedIndex((prev) =>
                    prev < authors.length - 1 ? prev + 1 : prev
                  );
                } else if (e.key === "ArrowUp") {
                  e.preventDefault();
                  setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : 0));
                } else if (e.key === "Enter" && highlightedIndex >= 0) {
                  e.preventDefault();
                  const author = authors[highlightedIndex];
                  if (author) {
                    const newAuthorId = author.id === value ? "" : author.id;
                    onChange(newAuthorId);
                    if (newAuthorId) {
                      setSelectedAuthor({ id: author.id, displayName: author.displayName });
                    } else {
                      setSelectedAuthor(null);
                    }
                    setAuthorSearchTerm("");
                    setOpenAuthorPopover(false);
                  }
                } else if (e.key === "Escape") {
                  e.preventDefault();
                  setOpenAuthorPopover(false);
                }
              }}
            />
          </div>

          <div className="max-h-[300px] overflow-y-auto overflow-x-hidden">
            {isLoadingAuthors ? (
              <div className="py-6 text-center text-sm">Авторлар йөкләнә...</div>
            ) : authors.length === 0 ? (
              <div className="py-6 text-center text-sm">Авторлар табылмады</div>
            ) : (
              <div className="p-1">
                {authors.map((author, index) => (
                  <div
                    key={author.id}
                    className={cn(
                      "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                      (value === author.id || index === highlightedIndex) && "bg-accent text-accent-foreground"
                    )}
                    onClick={() => {
                      const newAuthorId = author.id === value ? "" : author.id;
                      onChange(newAuthorId);
                      if (newAuthorId) {
                        setSelectedAuthor({ id: author.id, displayName: author.displayName });
                      } else {
                        setSelectedAuthor(null);
                      }
                      setAuthorSearchTerm("");
                      setOpenAuthorPopover(false);
                    }}
                    onMouseEnter={() => setHighlightedIndex(index)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === author.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {author.displayName}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
} 