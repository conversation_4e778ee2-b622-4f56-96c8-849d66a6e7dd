{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@blocknote+core@0.33.0_@types+hast@3.0.4_highlight.js@11.11.1_lowlight@3.3.0/node_modules/@blocknote/core/src/fonts/inter.css"], "sourcesContent": ["/* Generated using https://google-webfonts-helper.herokuapp.com/fonts/inter?subsets=latin */\n\n/* inter-100 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 100;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-100.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-100.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-200 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 200;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-200.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-200.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-300 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 300;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-300.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-300.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-regular - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 400;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-regular.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-regular.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-500 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 500;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-500.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-500.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-600 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 600;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-600.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-600.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-700 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 700;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-700.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-700.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-800 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 800;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-800.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-800.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n/* inter-900 - latin */\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 900;\n  src:\n    local(\"\"),\n    url(\"./inter-v12-latin/inter-v12-latin-900.woff2\") format(\"woff2\"),\n    /* Chrome 26+, Opera 23+, Firefox 39+ */\n      url(\"./inter-v12-latin/inter-v12-latin-900.woff\") format(\"woff\"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */\n}\n"], "names": [], "mappings": "AAGA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA;;;;;;;AAWA", "ignoreList": [0]}}]}