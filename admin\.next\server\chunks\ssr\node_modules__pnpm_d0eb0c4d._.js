module.exports = {

"[project]/node_modules/.pnpm/rehype-parse@9.0.1/node_modules/rehype-parse/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_10d4cc1b._.js",
  "server/chunks/ssr/[root of the server]__27bdde4f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-parse@9.0.1/node_modules/rehype-parse/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-stringify@10.0.1/node_modules/rehype-stringify/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_19ce5953._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-stringify@10.0.1/node_modules/rehype-stringify/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/unified@11.0.5/node_modules/unified/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root of the server]__d16606a1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/unified@11.0.5/node_modules/unified/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/hast-util-from-dom@5.0.1/node_modules/hast-util-from-dom/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_7b762fb5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/hast-util-from-dom@5.0.1/node_modules/hast-util-from-dom/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_eb9918b0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-remark@10.0.1/node_modules/rehype-remark/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-gfm@4.0.1/node_modules/remark-gfm/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_5b97127c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-gfm@4.0.1/node_modules/remark-gfm/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-stringify@11.0.0/node_modules/remark-stringify/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_5777a775._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-stringify@11.0.0/node_modules/remark-stringify/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-parse@11.0.0/node_modules/remark-parse/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_2643121b._.js",
  "server/chunks/ssr/[root of the server]__e2940cb3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-parse@11.0.0/node_modules/remark-parse/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/remark-rehype@11.1.2/node_modules/remark-rehype/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_0388d1ba._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/remark-rehype@11.1.2/node_modules/remark-rehype/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_3f39f5a5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/rehype-format@5.0.1/node_modules/rehype-format/index.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/emoji-mart@5.6.0/node_modules/emoji-mart/dist/module.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/c3d6e_emoji-mart_dist_module_8eb65c45.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/emoji-mart@5.6.0/node_modules/emoji-mart/dist/module.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@emoji-mart+data@1.2.1/node_modules/@emoji-mart/data/sets/15/native.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/6c0ff_@emoji-mart_data_sets_15_native_json_7c5cbf12._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@emoji-mart+data@1.2.1/node_modules/@emoji-mart/data/sets/15/native.json (json)");
    });
});
}}),

};